rm /export/home/<USER>/interactive/printout.ELSO
rm /export/home/<USER>/interactive/ELSO


#esembe0f
proc -g code=ansi_c mode=oracle dbms=v7 unsafe_null=yes iname=esembe0f include=/u01/app/oracle/product/12.2.0/dbhome_1/precomp/public include=/u01/app/oracle/product/12.2.0/dbhome_1/lib include=/usr/include
/usr/bin/gcc  -Wall -g -O3  -trigraphs -fPIC -DPRECOMP -I/u01/app/oracle/product/12.2.0/dbhome_1/precomp/public -I/u01/app/oracle/product/12.2.0/dbhome_1/lib -I/usr/include -DLINUX -DORAX86_64 -D_GNU_SOURCE -D_LARGEFILE64_SOURCE=1 -D_LARGEFILE_SOURCE=1 -DSLTS_ENABLE -DSLMXMX_ENABLE -D_REENTRANT -DNS_THREADS -DLONG_IS_64 -DSS_64BIT_SERVER -DLDAP_CM     -m64 -DCOSITE -c esembe0f.c
make --debug=b -f new_curses.mk EXE=esembe0f OBJS='esembe0f.o screen.o utility.o login.o' build

#----------------------------------------------------------------------------------------------

#esemba0x
/usr/bin/gcc  -Wall -g -O3  -march=native -trigraphs -fPIC -DPRECOMP -I/u01/app/oracle/product/12.2.0/dbhome_1/precomp/public -I/u01/app/oracle/product/12.2.0/dbhome_1/lib -I/usr/include -DLINUX -DORAX86_64 -D_GNU_SOURCE -D_LARGEFILE64_SOURCE=1 -D_LARGEFILE_SOURCE=1 -DSLTS_ENABLE -DSLMXMX_ENABLE -D_REENTRANT -DNS_THREADS -DLONG_IS_64 -DSS_64BIT_SERVER -DLDAP_CM     -m64 -DBATCHED_EMC   -c esemda0r.c
/usr/bin/gcc  -Wall -g -O3  -march=native -trigraphs -fPIC -DPRECOMP -I/u01/app/oracle/product/12.2.0/dbhome_1/precomp/public -I/u01/app/oracle/product/12.2.0/dbhome_1/lib -I/usr/include -DLINUX -DORAX86_64 -D_GNU_SOURCE -D_LARGEFILE64_SOURCE=1 -D_LARGEFILE_SOURCE=1 -DSLTS_ENABLE -DSLMXMX_ENABLE -D_REENTRANT -DNS_THREADS -DLONG_IS_64 -DSS_64BIT_SERVER -DLDAP_CM     -m64 -DBATCHED_EMC   -c esemba0x.c

#esemss0x
proc -g code=ansi_c mode=oracle dbms=v7 unsafe_null=yes iname=esemss0x include=/u01/app/oracle/product/12.2.0/dbhome_1/precomp/public include=/u01/app/oracle/product/12.2.0/dbhome_1/lib include=/usr/include
/usr/bin/gcc  -Wall -g -O3   -march=native -trigraphs -fPIC -DPRECOMP -I/u01/app/oracle/product/12.2.0/dbhome_1/precomp/public -I/u01/app/oracle/product/12.2.0/dbhome_1/lib -I/usr/include   -DLINUX -DORAX86_64 -D_GNU_SOURCE -D_LARGEFILE64_SOURCE=1 -D_LARGEFILE_SOURCE=1 -DSLTS_ENABLE -DSLMXMX_ENABLE -D_REENTRANT -DNS_THREADS -DLONG_IS_64 -DSS_64BIT_SERVER -DLDAP_CM     -m64 -DBATCHED_EMC -c esemss0x.c

#esemco0r
proc -g LINES=YES code=ansi_c mode=oracle dbms=v7 unsafe_null=yes iname=esemco0r include=/u01/app/oracle/product/12.2.0/dbhome_1/precomp/public include=/u01/app/oracle/product/12.2.0/dbhome_1/lib
/usr/bin/gcc  -Wall -g -O3  -trigraphs -fPIC -DPRECOMP -I/u01/app/oracle/product/12.2.0/dbhome_1/precomp/public -I/u01/app/oracle/product/12.2.0/dbhome_1/lib -DLINUX -DORAX86_64 -D_GNU_SOURCE -D_LARGEFILE64_SOURCE=1 -D_LARGEFILE_SOURCE=1 -DSLTS_ENABLE -DSLMXMX_ENABLE -D_REENTRANT -DNS_THREADS -DLONG_IS_64 -DSS_64BIT_SERVER -DLDAP_CM     -m64 -c esemco0r.c

#esemdc0r
proc -g LINES=YES code=ansi_c mode=oracle dbms=v7 unsafe_null=yes iname=esemdc0r include=/u01/app/oracle/product/12.2.0/dbhome_1/precomp/public include=/u01/app/oracle/product/12.2.0/dbhome_1/lib
/usr/bin/gcc  -Wall -g -O3  -trigraphs -fPIC -DPRECOMP -I/u01/app/oracle/product/12.2.0/dbhome_1/precomp/public -I/u01/app/oracle/product/12.2.0/dbhome_1/lib -DLINUX -DORAX86_64 -D_GNU_SOURCE -D_LARGEFILE64_SOURCE=1 -D_LARGEFILE_SOURCE=1 -DSLTS_ENABLE -DSLMXMX_ENABLE -D_REENTRANT -DNS_THREADS -DLONG_IS_64 -DSS_64BIT_SERVER -DLDAP_CM     -m64 -c esemdc0r.c

#esemda0r
proc -g LINES=YES code=ansi_c mode=oracle dbms=v7 unsafe_null=yes iname=esemda0r include=/u01/app/oracle/product/12.2.0/dbhome_1/precomp/public include=/u01/app/oracle/product/12.2.0/dbhome_1/lib
/usr/bin/gcc  -Wall -g -O3  -trigraphs -fPIC -DPRECOMP -I/u01/app/oracle/product/12.2.0/dbhome_1/precomp/public -I/u01/app/oracle/product/12.2.0/dbhome_1/lib -DLINUX -DORAX86_64 -D_GNU_SOURCE -D_LARGEFILE64_SOURCE=1 -D_LARGEFILE_SOURCE=1 -DSLTS_ENABLE -DSLMXMX_ENABLE -D_REENTRANT -DNS_THREADS -DLONG_IS_64 -DSS_64BIT_SERVER -DLDAP_CM     -m64 -c esemda0r.c


make --debug=b -f new_proc.mk EXE=esemba0x OBJS='esemba0x.o esemrl0x.o esemtn0x.o esemco0r.o esemin0x.o esemss0x.o esemda0r.o esemsb0x.o esemgd0x.o esemdc0r.o esemdl0x.o esemim0r.o utility.o login.o' build
