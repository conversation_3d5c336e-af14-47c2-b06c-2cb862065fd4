/**********************************************************************/
/*                                                                    */
/*    Module Name   :  main (esemagmr.pc)                             */
/*    Date Written  :  July, 1993                                     */
/*    Author        :  <PERSON><PERSON> <PERSON>. Mak                                      */
/*                                                                    */
/*    Callers       :  main (esemep0f.c)                              */  
/*                                                                    */
/*    Parameters    :  error message                                  */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  Prints antenna gain of all entries in table    */
/*                     HZ_ANTENNA_GAIN or VT_ANTENNA_GAIN depending   */
/*                     on whether user supplies a '-h' or '-v' flag.  */
/*                     The report is sorted by antenna type and lag.  */
/*                     degree.                                        */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Jul-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>


#ifndef ERROR
#define ERROR    1
#endif

#ifndef OK
#define OK       0
#endif

#ifndef LOGIN_OK
#define LOGIN_OK    0
#endif

#ifndef NOT_FOUND
#define NOT_FOUND   1403
#endif

#define  MAX_RPT_LINES       57
#define  ITEMS_PER_LINE      6
#define  HEADLINES           4
#define  COLUMN_HEADLINES    6

/* modes of antenna gain 'HZ' - horizontal, 'VT' - vertical */
#define  HZ      0
#define  VT      1

/*   strip blank mode   */
#define  TRAIL          0
#define  LEADING        1

char    user_id[20];        /* user login name       */
char	sys_date[9];        /* system date           */
char	sys_time[9];        /* system time           */
char	yymmdd[7];          /* system date in yymmdd */
char	hhmmss[7];          /* system time in hhmmss */
char	printer_id[15];     /* destinated printer id */
char    err_msg[133];

char       *getenv();
struct tm  *get_date_time();


EXEC SQL BEGIN DECLARE SECTION;

    float    o_gain;
    int      o_degree;
    VARCHAR  o_ant_desc[31];
    char     select1[256];

EXEC SQL END DECLARE SECTION;

EXEC SQL INCLUDE SQLCA;


int user_login    (char *,char *,char *);
int strip_blank   (char *,char *);

int get_sys_date_time(char *,char *,char *,char *);



void print_rpt_col();
void print_rpt_head();


int main(argc, argv)
int    argc;
char   **argv;
{
    char   *rpt_dir;
    char   rpt_line[133];
    char   sub_rpt_line[30];
    char   fname[120];
    char   table_name[35];
    char   cmdline[100];
    char   prev_ant_desc[31];
    FILE   *rfp;
    int    which_form;
    int    line_cnt = 0;
    int    page_cnt = 1;
    int    item_cnt = 0;

    

    EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn;

    if (argc != 6)
    {
        printf("Fatal error: invalid no. of arguments passed to %s\n", argv[0]);
        printf("Usage: esemagmr username password [-h or -v] -P printer_id\n");
        exit(1);
    }

    if (strcmp(argv[3], "-h") && strcmp(argv[3], "-v"))
    {
        printf("Fatal error: invalid no. of arguments passed to %s\n", argv[0]);
        printf("Usage: esemagmr username password [-h or -v] -P printer_id\n");
        exit(1);
    }

    if (strcmp(argv[4], "-P"))
    {
        printf("Fatal error: invalid no. of arguments passed to %s\n", argv[0]);
        printf("Usage: esemagmr username password [-h or -v] -P printer_id\n");
        exit(1);
    }

    strcpy(user_id, argv[1]);
    rpt_dir = getenv("REPORT_DIR");
    sprintf(fname, "%s/%s.%s", rpt_dir, argv[0], user_id);
    if((rfp = fopen(fname, "w")) == (FILE *) NULL)
    {
        printf("Fail to open %s, press any key to exit", fname);
        getchar();
        goto force_exit;
    }

    if (user_login(user_id, argv[2], err_msg) != LOGIN_OK)
    {
        strip_blank(err_msg, TRAIL);
        strcat(err_msg, "  ");
        strcat(err_msg, argv[1]);
        strcat(err_msg, argv[2]);
        fprintf(rfp, "%s\n", err_msg);
        fclose(rfp);
        goto force_exit;
    }

    if (argv[3][1] == 'h')
    {
        strcpy(table_name, "HZ_ANTENNA_GAIN");
        which_form = HZ;
    }
    else
    {
        strcpy(table_name, "VT_ANTENNA_GAIN");
        which_form = VT;
    }

    strcpy(printer_id, argv[5]);

    sprintf(select1, "SELECT ANTENNA_DESC, DEGREE, NVL(GAIN_IN_DB, 0) FROM %s X, ANTENNA Y ", table_name);
    strcat(select1, "WHERE X.ANTENNA = Y.ANTENNA ORDER BY X.ANTENNA, DEGREE");

    EXEC SQL PREPARE S1 FROM :select1;
    EXEC SQL DECLARE C1 CURSOR FOR S1;

    EXEC SQL OPEN C1;

    EXEC SQL FETCH C1 INTO :o_ant_desc, :o_degree, :o_gain;
    if (sqlca.sqlcode == NOT_FOUND)
    {
        fprintf(rfp, "Fatal error: no record found in %s", table_name);
        EXEC SQL CLOSE C1;
        fclose(rfp);
        goto force_exit;
    }

    get_sys_date_time(sys_date, sys_time, yymmdd, hhmmss);

    print_rpt_head(rfp, which_form, page_cnt); 
    page_cnt++;
    line_cnt = HEADLINES + COLUMN_HEADLINES;
    rpt_line[0] = prev_ant_desc[0] = '\0';

    for (; ;)
    {
        o_ant_desc.arr[o_ant_desc.len] = '\0';

/*        if (strcmp(o_ant_desc.arr, prev_ant_desc)) */
        if (strcmp((char *)o_ant_desc.arr, prev_ant_desc))
        {
            if (prev_ant_desc[0] != '\0')
            {
                fprintf(rfp, "%s\n", rpt_line);
                line_cnt++;
                rpt_line[0] = '\0';
                item_cnt = 0;
                if ((line_cnt + COLUMN_HEADLINES) >= MAX_RPT_LINES)
                {
                    print_rpt_head(rfp, which_form, page_cnt); 
                    page_cnt++;
                    line_cnt = HEADLINES + COLUMN_HEADLINES;
                }
                else
                {
                    print_rpt_col(rfp); 
                    line_cnt += COLUMN_HEADLINES;
                }
            }
/*            strcpy(prev_ant_desc, o_ant_desc.arr); */
            strcpy(prev_ant_desc, (char *)o_ant_desc.arr);
        }

        sprintf(sub_rpt_line, "   %3d/%-8.5f      ", o_degree, o_gain);
        strcat(rpt_line, sub_rpt_line);

        item_cnt = (item_cnt + 1) % ITEMS_PER_LINE;
        if (item_cnt == 0)
        {
            if (line_cnt == MAX_RPT_LINES)
            {
                print_rpt_head(rfp, which_form, page_cnt); 
                page_cnt++;
                line_cnt = HEADLINES + COLUMN_HEADLINES;
            }
            fprintf(rfp, "%s\n", rpt_line);
            rpt_line[0] = '\0';
            line_cnt++;
        }

        EXEC SQL FETCH C1 INTO :o_ant_desc, :o_degree, :o_gain;

        if (sqlca.sqlcode == NOT_FOUND)
        {
            if (item_cnt != 0)
                fprintf(rfp, "%s\n", rpt_line);

            break;
        }
    }
    
    EXEC SQL CLOSE C1;

    fprintf(rfp, "\n\n%57s** END OF REPORT **", "");
    fclose(rfp);

    sprintf(cmdline, "lp -dprinter1 %s > /dev/null", fname);
    system(cmdline);
/*
    unlink(fname);
*/

    exit(0);

sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    fprintf(rfp, "%s\n", err_msg);
    fclose(rfp);

force_exit:
    exit(1);

}


/**********************************************************************/
/*  print antenna gain report column heading                          */
/**********************************************************************/

void print_rpt_col(rfp)
FILE   *rfp;
{
    int    i;

    fprintf(rfp, "\n\nANTENNA TYPE : %s\n\n", o_ant_desc.arr);
    for (i = 0; i < ITEMS_PER_LINE - 1; i++)
        fprintf(rfp, "DEGREE/GAIN IN DB    ");
    fprintf(rfp, "DEGREE/GAIN IN DB\n");
    for (i = 0; i < ITEMS_PER_LINE - 1; i++)
        fprintf(rfp, "=================    ");
    fprintf(rfp, "=================\n");
}


/**********************************************************************/
/*  print antenna gain report heading                                 */
/**********************************************************************/

void print_rpt_head(rfp, which_form, page_cnt)
FILE    *rfp;
int     which_form;
int     page_cnt;
{
   if (page_cnt > 1)
       fprintf(rfp, "\f");
   fprintf(rfp, "USER ID  : %-15s%26s", user_id, "");
   fprintf(rfp, "ENHANCED SPECTRUM MANAGEMENT SYSTEM");
   fprintf(rfp, "%27sPAGE    : %-d\n", "", page_cnt);
   fprintf(rfp, "RUN DATE : %s", sys_date);
   (which_form == HZ) ?
       fprintf(rfp, "%29sHORIZONTAL ANTENNA GAIN MAINTENANCE REPORT", ""):
       fprintf(rfp, "%30sVERTICAL ANTENNA GAIN MAINTENANCE REPORT ", "");
   fprintf(rfp, "%24sPROGRAM : esemagmr\n", ""); 
   fprintf(rfp, "%56s(SORTED BY ANTENNA, DEGREE)\n\n", "");   /*20170613 Cyrus [Add] [, ""] */
   print_rpt_col(rfp);
}
