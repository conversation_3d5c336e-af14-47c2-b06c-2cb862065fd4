/**********************************************************************/
/*                                                                    */
/*    Module Name   :  main (esemba0x.c)                              */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  <PERSON><PERSON> <PERSON><PERSON>                                      */
/*                                                                    */
/*    Callers       :                                                 */  
/*                                                                    */
/*    Parameters    :                                                 */
/*                                                                    */
/*    Called Modules:  load_reference (esemrl0x.pc)                   */
/*                     get_local_height (esemtn0x.pc)                 */
/*                     desensit_analysis (esemda0r.c)                 */
/*                     intermod_2 (esemim0r.c)                        */
/*                     intermod_3 (esemim0r.c)                        */
/*                     cochaninf (esemco0r.pc)                        */
/*                                                                    */
/*    Purpose       :  Batch EMC analysis control module. Read in EMC */
/*                     batch records and call the subordinate modules */
/*                     to accomplish EMC analysis.                    */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/


#include <stdio.h>
#include <string.h>
/* #include "../include/math.h" */
#include <math.h>
#include "../include/define.h"
#include "../include/propose.h"
#include "../include/exist.h"
#include "../include/emc.h"
#include "../include/presul.h"


#include <unistd.h>   /*20180711 Cyrus [Add] */
#include <stdlib.h>   /*20180711 Cyrus [Add] */

#ifndef ERROR
#define ERROR    1
#endif

#ifndef OK
#define OK       0
#endif

#ifndef M_PI
#define M_PI            3.14159265358979323846
#endif

#define  END_OF_LIST    -1

#define  TRAIL          0
#define  LEADING        1

#define  LOGIN_OK    0

char       *getenv();
float      get_local_height();
struct tm  *get_date_time();

extern double  atof();

void print_emc_summary();                           /*20180711 Cyrus [Add] */ 
int  get_sys_date_time(char *,char *,char *,char *); /*20180711 Cyrus [Add] */
int  user_login    (char *,char *,char *);                           /*20170707 Cyrus Add */
void load_reference();
int  strip_blank   (char *,char *);									/*20170707 Cyrus Add */
int  get_district(char *, char *, int *, int*);
int  set_band_mode(char); /* 20170703 Cyrusma Added */
void cochaninf();
int  desensit_analysis();
void intermod_2();
void intermod_3();
void user_logout();

extern char msg[];

int main(argc, argv)
int   argc;
char  **argv;
{
    char       emc_batch[100];
    char       interact_dir[100];
    char       audit[100];
    char       cmdline[120];
    char       err_msg[80];
    char       instr[200];
    char       att_sign, pw_sign;
    char       tmp_grid[15], tmp_att_db[10], tmp_ht[10];
    char       tmp_pw_dbw[10], tmp_az[10], tmp_feed[10];
    char       tmp_freq[15];
    FILE       *bfp;
    int        prev_stn_node = -1;
    int        i, j, k;
    int        status;

    if ((argc != 6) && (argc != 9))
    {
        printf("Invalid no. of arguments passed to %s\n", argv[0]);
        printf("Usage: esemba0x username password -P printer_id print_file [-i {-l|s} emc_uid]\n");
        exit(1);
    }

    if (strcmp(argv[3], "-P"))
    {
        printf("Invalid argument '%s'\n", argv[3]);
        printf("Usage: esemba0x username password -P printer_id print_file [-i {-l|s} emc_uid]\n");
        exit(1);
    }
    
    emc_dir = getenv("EMC");
    interactive = FALSE;

    if (argc == 9)
    {
        if (strcmp(argv[6], "-i"))
        {
            printf("Fatal error: invalid argument '%s'\n", argv[6]);
            printf("Usage: esemba0x username password -P printer_id print_file [-i {-l|s} emc_uid]\n");
            exit(1);
        }
        else
        {
            strcpy(emc_uid, argv[8]);
            interactive = TRUE;
            sprintf(interact_dir, "%s/interactive", emc_dir);
        }

        if (strcmp(argv[7], "-l") && strcmp(argv[7], "-s"))
        {
            printf("Fatal error: invalid argument '%s'\n", argv[7]);
            printf("Usage: esemba0x username password -P printer_id print_file [-i {-l|s} emc_uid]\n");
            exit(1);
        }
    }

    /********************************************************************/
    /* for batched EMC mode, create EMC lock file for running EMC in    */
    /* exclusive mode                                                   */
    /********************************************************************/
    if (interactive == FALSE)
    {
        sprintf(print_file, "%s.%s", argv[5], argv[1]);
    }
    else
        sprintf(print_file, "%s.%s", argv[5], emc_uid);

    strcpy(printer_id, argv[4]);

    /* remove last EMC print file */
    sprintf(cmdline, "rm -f %s", print_file);
    system(cmdline);

    get_sys_date_time(sys_date, sys_time, yymmdd, hhmmss);

#ifdef DEBUG
    if (interactive == FALSE)
    {
        printf("%s %s %s\n", sys_date, sys_time, yymmdd);
        fflush(stdin);
    }
#endif

    if (interactive == TRUE)
        sprintf(emc_batch, "%s/%s", interact_dir, emc_uid);
    else
        sprintf(emc_batch, "%s/batch/%s", emc_dir, yymmdd);

    if((bfp = fopen(emc_batch, "r")) == (FILE *) NULL)
    {
        printf("Fatal error: fail to open EMC batch : %s\n", emc_batch);
        exit(1);
    }

    sprintf(audit, "%s/audit/%s", emc_dir, yymmdd);
    if ((afp = fopen(audit, "w")) == (FILE *) NULL)
    {
        printf("Fatal error: fail to open audit file : %s\n", audit);
        exit(1);
    }

    if (user_login(argv[1], argv[2], err_msg) != LOGIN_OK)
    {
        fprintf(afp, "%s", err_msg);
        fflush(afp);
        fclose(afp);
        exit(1);
    }

    load_reference();

    if (fgets(instr, sizeof(instr), bfp) == (char *) NULL)
    {
        printf("Empty EMC batch : %s\n", emc_batch);
        exit(1);
    }

    if ((prop = (PROPOSE *) malloc(sizeof(PROPOSE))) == (PROPOSE *) NULL)
    {
        printf("Cannot allocate space for proposed station\n");
        exit(1);
    }

    for (i = 0; i < MAX_EXIST; i++)
        exist[i] = (EXIST *)NULL;

    for (i = 0; i < MAX_EXIST*2 + 500; i++)
        fq_list[i] = s_fq_list[i] = (EXIST_FREQ *)NULL;


    i = fq_cnt = 0;

    do
    {
        if (exist[i] == (EXIST *) NULL)
            if ((exist[i] = (EXIST *) malloc(sizeof(EXIST))) == (EXIST *) NULL)
            {
                printf("Cannot allocate EXIST space for proposed station\n");
                exit(1);
            }

        strncpy(exist[i]->emc_uid, instr, 19);
        strip_blank(exist[i]->emc_uid, TRAIL);
        strncpy(tmp_grid, instr+19, 5);
        exist[i]->east_grid  = atoi(tmp_grid);
        strncpy(tmp_grid, instr+24, 5);
        exist[i]->north_grid = atoi(tmp_grid);
/*
        prop->east_grid  = prop->east_grid * 10;
        prop->north_grid = prop->north_grid * 10;
*/

        exist[i]->sys_category = 'X';
        strcpy(exist[i]->sys_type, "XX");
        sprintf(exist[i]->sys_no, "%04d", i);
        strcpy(exist[i]->sys_suffix, "000");

        strncpy(exist[i]->sub_district, instr+29, 3);
        strip_blank(exist[i]->sub_district, TRAIL);

	exist[i]->station_type = instr[32];

        strncpy(exist[i]->antenna, instr+33, 2);
        strip_blank(exist[i]->antenna, TRAIL);
        strncpy(tmp_az, instr+35, 3);
        exist[i]->az_max_rad = atoi(tmp_az);
        exist[i]->az_max_rad_r = (float)exist[i]->az_max_rad / 180 * M_PI;

        strncpy(tmp_ht, instr+38, 3);
        exist[i]->ant_height = atoi(tmp_ht);

	pw_sign = instr[41];
        strncpy(tmp_pw_dbw, instr+42, 4);
        exist[i]->pw_dbw = atof(tmp_pw_dbw);
        if (pw_sign == '-')
            exist[i]->pw_dbw = exist[i]->pw_dbw * (-1);

/*
        strncpy(tmp_az, instr+27, 3);
        prop->az_max_rad = atoi(tmp_az);
        prop->az_max_rad_r = (float)prop->az_max_rad / 180 * M_PI;
*/
        strncpy(tmp_feed, instr+46, 3);
        exist[i]->feed_loss = atoi(tmp_feed);

	att_sign = instr[49];
        strncpy(tmp_att_db, instr+50, 4);
        exist[i]->desen_att_db = atof(tmp_att_db);
        if (att_sign == '-')
            exist[i]->desen_att_db = exist[i]->desen_att_db * (-1);

	att_sign = instr[54];
        strncpy(tmp_att_db, instr+55, 4);
        exist[i]->intmod_att_db = atof(tmp_att_db);
        if (att_sign == '-')
            exist[i]->intmod_att_db = exist[i]->intmod_att_db * (-1);

        strncpy(exist[i]->sfx_filter, instr+59, 10);
        strip_blank(exist[i]->sfx_filter, TRAIL);
/*
        printf("\nHERE %s %d %d %s %c %f %f %s %d %f %d %d\n", 
        emc_uid, exist[i]->east_grid, exist[i]->north_grid, exist[i]->sub_district,
        exist[i]->station_type, exist[i]->desen_att_db, exist[i]->intmod_att_db, 
        exist[i]->antenna, exist[i]->ant_height, exist[i]->pw_dbw, exist[i]->az_max_rad,
        exist[i]->feed_loss);
        fflush(stdout);
*/

        for (k = 69; ;)
        {

            if (fq_list[fq_cnt] == (EXIST_FREQ *) NULL)
            {

                fq_list[fq_cnt] = (EXIST_FREQ *) malloc(sizeof(EXIST_FREQ));
                s_fq_list[fq_cnt] = (EXIST_FREQ *) malloc(sizeof(EXIST_FREQ));
            }

            if ((fq_list[fq_cnt] == (EXIST_FREQ *) NULL)
            ||  (s_fq_list[fq_cnt] == (EXIST_FREQ *) NULL))
            {
                printf("Fatal error: cannot allocate space for channels ");
                printf("of existing station\n");
                fflush(afp);
                fclose(afp);
                exit(1);
            }

            strncpy(tmp_freq, instr+k, 11);

            fq_list[fq_cnt]->tx_freq = s_fq_list[fq_cnt]->tx_freq = atof(tmp_freq);
            fq_list[fq_cnt]->tx_channel 
                = s_fq_list[fq_cnt]->tx_channel
                = (int)(fq_list[fq_cnt]->tx_freq/MIN_CHANNEL_SEP + .5);

            k += 11;

            strncpy(tmp_freq, instr+k, 11);

            fq_list[fq_cnt]->rx_freq = s_fq_list[fq_cnt]->rx_freq = atof(tmp_freq);
            fq_list[fq_cnt]->rx_channel 
                = s_fq_list[fq_cnt]->rx_channel
                = (int)(fq_list[fq_cnt]->rx_freq/MIN_CHANNEL_SEP + .5);


            fq_list[fq_cnt]->stn_node = s_fq_list[fq_cnt]->stn_node = i;
/*
printf("cnt tx rx txc rxc node: %d %lf %lf %d %d %d\n", fq_cnt,
            fq_list[fq_cnt]->tx_freq,
            fq_list[fq_cnt]->rx_freq,
            fq_list[fq_cnt]->tx_channel,
            fq_list[fq_cnt]->rx_channel,
            fq_list[fq_cnt]->stn_node);
*/
            exist_tx_freq = fq_list[fq_cnt]->tx_freq;
            exist_rx_freq = fq_list[fq_cnt]->rx_freq;
            e_idx = i;
/*
            set_band_mode(IS_EXIST);
*/

            k += 11;

            fq_cnt++;

            if (instr[k] == '\n') 
                break;
        }


		
        /* get local terrain height */
        exist[i]->height_asl = get_local_height((float)exist[i]->east_grid/10, 
                                            (float)exist[i]->north_grid/10);
/* 
printf("prop ht: %f\n", exist[i]->height_asl);
*/
        /* get dist_type, noise_code, dist_index */
        status = get_district(exist[i]->sub_district, &exist[i]->dist_type, 
                              &exist[i]->noise_code, &exist[i]->dist_index);
        if (status == ERROR)
        {
            printf("Error: sub-district code not found in SUBDISTRICT_TAB : ");
            printf("%s\n", exist[i]->sub_district);
            continue;
        }
        i++;

    }
    while (fgets(instr, sizeof(instr), bfp) != (char *) NULL);
/*
if (interactive == FALSE)
    printf("after input i fq_cnt: %d %d\n", i, fq_cnt);
*/

    prop_stn_cnt = i;
    prop_fq_cnt = fq_cnt;

    for (j = 0; j < prop_fq_cnt; j++)
    {
        if (prev_stn_node != fq_list[j]->stn_node)
        {
            prev_stn_node = prop->stn_node = i = fq_list[j]->stn_node;
            strcpy(emc_uid, exist[i]->emc_uid);
            prop->sys_category = exist[i]->sys_category;
            strcpy(prop->sys_type, exist[i]->sys_type);
            strcpy(prop->sys_no, exist[i]->sys_no);
            strcpy(prop->sys_suffix, exist[i]->sys_suffix);
            prop->east_grid = exist[i]->east_grid;
            prop->north_grid = exist[i]->north_grid;
            strcpy(prop->sub_district, exist[i]->sub_district);
            prop->station_type = exist[i]->station_type;
            prop->desen_att_db = exist[i]->desen_att_db;
            prop->intmod_att_db = exist[i]->intmod_att_db;
            strcpy(prop->antenna, exist[i]->antenna);
            prop->ant_height = exist[i]->ant_height;
            prop->pw_dbw = exist[i]->pw_dbw;
            prop->az_max_rad = exist[i]->az_max_rad;
            prop->az_max_rad_r = exist[i]->az_max_rad_r;
            prop->feed_loss = exist[i]->feed_loss;
	    strcpy(prop->sfx_filter, exist[i]->sfx_filter);
            prop->height_asl = exist[i]->height_asl;
            prop->dist_type = exist[i]->dist_type;
            prop->noise_code = exist[i]->noise_code;
            prop->dist_index = exist[i]->dist_index;

#ifdef DEBUG
    if (interactive == FALSE)
    {
            printf("\n\n\n\n\nProposed station information\n");
            printf("----------------------------\n");
            printf(" %s %d %d %s %c %f %f %s %d %f %d %d\n", 
            emc_uid, prop->east_grid, prop->north_grid, prop->sub_district,
            prop->station_type, prop->desen_att_db, prop->intmod_att_db,
            prop->antenna, prop->ant_height,
            prop->pw_dbw,   prop->az_max_rad, prop->feed_loss);
            printf("%s %f %d %c %d\n", 
	    prop->sfx_filter, prop->height_asl, prop->noise_code, 
            prop->dist_type, prop->dist_index);
    }
#endif
        }
        
        prop_tx_freq = fq_list[j]->tx_freq;
        prop_rx_freq = fq_list[j]->rx_freq;
        prop->tx_channel = fq_list[j]->tx_channel;
        prop->rx_channel = fq_list[j]->rx_channel;

        set_band_mode(IS_PROPOSED);    /* a proposed system may have several */
                                       /* proposed channels at one time and  */
                                       /* these channels may be of different */
                                       /* bands and transmission modes (SFX  */
                                       /* or DFX), therefore we have to      */
                                       /* 'set_band_mode' for every channel  */

        get_sys_date_time(sys_date, sys_time, yymmdd, hhmmss);

        fprintf(afp, "Prop. channel (tx/rx): %lf/%lf%4sGrid (N/E): %d/%d%4sTime: %s\n",
                prop_tx_freq, prop_rx_freq, "", prop->north_grid, 
                prop->east_grid, "", sys_time);
        fprintf(afp, "============================================================================================\n\n");

        prev_tx_grid[0] = prev_tx_grid[1] = 0;
        prev_rx_grid[0] = prev_rx_grid[1] = 0;
        prev_diff_loss  = 0.0;

        cochan_tot = 0;
        desen_tot = 0;
        intmod2_vict_tot = 0;
        intmod2_tx1_tot = 0;
        intmod2_tx2_tot = 0;
        intmod3_vict_tot = 0;
        intmod3_tx1_tot = 0;
        intmod3_tx3_tot = 0;
        cull_stn_cnt = 0;

        /* co-channel stations listing */
		

        cochaninf();

        /* desensitisation analysis */



		
        status = desensit_analysis();

        if (status == ERROR)
            continue;

        if (cull_stn_cnt == 0)
        {
            print_emc_summary();
            continue;
        }

        /* 2, 3-signal intermodulation analysis */
        intermod_2();
        intermod_3();

        fprintf(afp, "\n\n\n");
/*
        printf("\n%d %d %s %c %f %s %d %f %d %d\n", 
        prop->east_grid, prop->north_grid, prop->sub_district,
        prop->station_type, prop->att_db, prop->antenna, prop->ant_height,
        prop->pw_dbw,   prop->az_max_rad, prop->feed_loss);
        printf("%s %lf %lf %f %c %d %d %c %d\n", 
	prop->sfx_filter, prop->tx_freq[i], prop->rx_freq[i],
	prop->height_asl, prop->mode, prop->band, prop->noise_code, 
        prop->dist_type, prop->dist_index);
*/

        print_emc_summary();
    }

    fclose(afp);
/*
    sprintf(cmdline, "lp -dprinter1 %s > /dev/null", audit);
*/
    sprintf(cmdline, "cat %s >> %s", audit, print_file);
    system(cmdline);


    /**********************************************************/
    /* if interactive EMC analysis and local print is chosen, */
    /* use 'cat' to redirect reports to local printer         */
    /* else user system printer to print reports              */
    /**********************************************************/

    if ((interactive == TRUE) && (!strcmp(argv[7], "-l")))
        sprintf(cmdline, "echo '[5i'; cat %s; echo '[4i'", print_file);
    else
        sprintf(cmdline, "lp -dprinter1 %s > /dev/null", print_file);
    
    system(cmdline);
    user_logout();

    if (interactive == TRUE)
    {
		
//20180703 Cyrus Add function for change output files to window format.
char sp='"';
sprintf(msg, " awk 'sub(%c$%c,%c\r%c)' batch.out.ELSO_WEB.ANALYSIS > batch.out.ELSO_WEB.ANALYSIS2 "  , sp,sp,sp,sp  );
system(msg);
sprintf(msg, " rm -f batch.out.ELSO_WEB.ANALYSIS "  );
system(msg);
sprintf(msg, " mv -v batch.out.ELSO_WEB.ANALYSIS2 batch.out.ELSO_WEB.ANALYSIS "  );	
system(msg);
		
        printf("[23;0f[2K[23;30f** EMC finishes **");
        unlink(emc_batch);   /* remove interactive EMC file */
    }

	return(0);
	
}


/**********************************************************************/
/*    Print a EMC summary report of all analyses that have been       */
/*    done for current proposed channel.                              */
/**********************************************************************/

void print_emc_summary()
{
    FILE    *sfp;
    char    sfname[120];
    char    cmdline[150];

    sprintf(sfname, "%s/summary/%.5lf.%s", emc_dir, prop_tx_freq, hhmmss);
    if ((sfp = fopen(sfname, "w")) == (FILE *) NULL)
    {
        printf("Fatal error: fail to open summary file : %s\n", sfname);
        exit(1);
    }

   fprintf(sfp, "RUN DATE: %s%16s", sys_date, "");
   fprintf(sfp,
          "*****************************************************************");
   fprintf(sfp, "%15sPAGE   : 1\n", "");
   fprintf(sfp, "RUN TIME: %s%16s", sys_time, "");
   fprintf(sfp,
         "*                                                               *");
   fprintf(sfp, "%15sPROGRAM: esemba0x\n", "");
   fprintf(sfp, "USER ID : %-19s%5s", emc_uid, "");
   fprintf(sfp,
         "*                  EMC ANALYSIS - SUMMARY LOG                   *\n");
   fprintf(sfp, "%34s", "");
   fprintf(sfp, 
         "*                 PROPOSED STATION INFORMATION                  *\n");
   fprintf(sfp, "%34s", "");
   fprintf(sfp,
         "*                                                               *\n");
   fprintf(sfp, "%34s", "");
   fprintf(sfp, "*  PROP. SYSTEM    : %c%s%s-%s%32s*\n", prop->sys_category,
           prop->sys_type, prop->sys_no, prop->sys_suffix, "");
   fprintf(sfp, "%34s", "");
   fprintf(sfp, 
          "*  RX FREQ (MHz)   : %10.4lf   TX FREQ (MHz)   : %10.4lf  *\n",
          prop_rx_freq, prop_tx_freq);
   fprintf(sfp, "%34s", "");
   fprintf(sfp, 
          "*  GRID EAST       :      %5d   GRID NORTH      :      %5d  *\n",
          prop->east_grid, prop->north_grid);
   fprintf(sfp, "%34s", "");
   fprintf(sfp, 
          "*  SUB-DISTRICT    :        %3s   ERP (DBW)       :     %6.2f  *\n",
          prop->sub_district, prop->pw_dbw);
   fprintf(sfp, "%34s", "");
   fprintf(sfp, 
          "*  ANTENNA HT (M)  :        %3d   TERRAIN HT (M)  :      %5.1f  *\n",
          prop->ant_height, prop->height_asl);
   fprintf(sfp, "%34s", "");
   fprintf(sfp, 
          "*  ANTENNA TYPE    :        %3s   AZ OF MAX RAD   :        %3d  *\n",
          prop->antenna, prop->az_max_rad);
   fprintf(sfp, "%34s", "");
   fprintf(sfp, 
          "*  DESEN ADJ LEVEL :     %6.1f   INTMOD ADJ LEVEL:     %6.1f  *\n",
          prop->desen_att_db, prop->intmod_att_db);
   fprintf(sfp, "%34s", "");
   if (prop->sfx_filter[0] != '\0')
       fprintf(sfp, 
              "*  FEED LOSS (DBW) :        %3d   SFX FILTER TYPE  : %10s  *\n",
              prop->feed_loss, prop->sfx_filter);
   else
       fprintf(sfp, "*  FEED LOSS (DBW) :        %3d%33s*\n",
              prop->feed_loss, "");
   fprintf(sfp, "%34s", "");
   fprintf(sfp, "*  STATION TYPE    :          %c%33s*\n",
         prop->station_type, "");
   fprintf(sfp, "%34s", "");
   fprintf(sfp,
         "*                                                               *\n");
   fprintf(sfp, "%34s", "");
   fprintf(sfp, 
         "*****************************************************************\n");
   fprintf(sfp, "\n\n\n\n");
   fprintf(sfp, "** Co-channel Information Report\n"); 
   fprintf(sfp, "   Number of Co-channel Stations : %d\n\n\n", cochan_tot);

   if (cull_stn_cnt == 0)
       fprintf(sfp, "** No stations within culling distance for further analyses\f");
   else
   {
       fprintf(sfp, "** Interference and Power Analysis Report (Desensitisation)\n");
       fprintf(sfp, "   Number of Interferences : %d\n\n\n", desen_tot);
       fprintf(sfp, "** 2-signal Intermodulation Report");
       fprintf(sfp, "(Proposed Station as Victim)\n");
       fprintf(sfp, "   Number of Combinations : %d\n\n", intmod2_vict_tot);
       fprintf(sfp, "** 2-signal Intermodulation Report");
       fprintf(sfp, "(Proposed Station as Tx1)\n");
       fprintf(sfp, "   Number of Combinations : %d\n\n", intmod2_tx1_tot);
       fprintf(sfp, "** 2-signal Intermodulation Report");
       fprintf(sfp, "(Proposed Station as Tx2)\n");
       fprintf(sfp, "   Number of Combinations : %d\n\n\n", intmod2_tx2_tot);
       fprintf(sfp, "** 3-signal Intermodulation Report");
       fprintf(sfp, "(Proposed Station as Victim)\n");
       fprintf(sfp, "   Number of Combinations : %d\n\n", intmod3_vict_tot);
       fprintf(sfp, "** 3-signal Intermodulation Report");
       fprintf(sfp, "(Proposed Station as Tx1)\n");
       fprintf(sfp, "   Number of Combinations : %d\n\n", intmod3_tx1_tot);
       fprintf(sfp, "** 3-signal Intermodulation Report");
       fprintf(sfp, "(Proposed Station as Tx3)\n");
       fprintf(sfp, "   Number of Combinations : %d\f", intmod3_tx3_tot);
   }

   fclose(sfp);

/*
    sprintf(cmdline, "lp -dprinter1 %s > /dev/null", sfname);
*/
    sprintf(cmdline, "cat %s >> %s", sfname, print_file);
    system(cmdline);
}
