
/* Result Sets Interface */
#ifndef SQL_CRSR
#  define SQL_CRSR
  struct sql_cursor
  {
    unsigned int curocn;
    void *ptr1;
    void *ptr2;
    unsigned int magic;
  };
  typedef struct sql_cursor sql_cursor;
  typedef struct sql_cursor SQL_CURSOR;
#endif /* SQL_CRSR */

/* Thread Safety */
typedef void * sql_context;
typedef void * SQL_CONTEXT;

/* Object support */
struct sqltvn
{
  unsigned char *tvnvsn; 
  unsigned short tvnvsnl; 
  unsigned char *tvnnm;
  unsigned short tvnnml; 
  unsigned char *tvnsnm;
  unsigned short tvnsnml;
};
typedef struct sqltvn sqltvn;

struct sqladts
{
  unsigned int adtvsn; 
  unsigned short adtmode; 
  unsigned short adtnum;  
  sqltvn adttvn[1];       
};
typedef struct sqladts sqladts;

static struct sqladts sqladt = {
  1,0,0,
};

/* Binding to PL/SQL Records */
struct sqltdss
{
  unsigned int tdsvsn; 
  unsigned short tdsnum; 
  unsigned char *tdsval[1]; 
};
typedef struct sqltdss sqltdss;
static struct sqltdss sqltds =
{
  1,
  0,
};

/* File name & Package Name */
struct sqlcxp
{
  unsigned short fillen;
           char  filnam[12];
};
static const struct sqlcxp sqlfpn =
{
    11,
    "esembe0f.pc"
};


static unsigned int sqlctx = 148843;


static struct sqlexd {
   unsigned long  sqlvsn;
   unsigned int   arrsiz;
   unsigned int   iters;
   unsigned int   offset;
   unsigned short selerr;
   unsigned short sqlety;
   unsigned int   occurs;
      const short *cud;
   unsigned char  *sqlest;
      const char  *stmt;
   sqladts *sqladtp;
   sqltdss *sqltdsp;
   unsigned char  **sqphsv;
   unsigned long  *sqphsl;
            int   *sqphss;
            short **sqpind;
            int   *sqpins;
   unsigned long  *sqparm;
   unsigned long  **sqparc;
   unsigned short  *sqpadto;
   unsigned short  *sqptdso;
   unsigned int   sqlcmax;
   unsigned int   sqlcmin;
   unsigned int   sqlcincr;
   unsigned int   sqlctimeout;
   unsigned int   sqlcnowait;
            int   sqfoff;
   unsigned int   sqcmod;
   unsigned int   sqfmod;
   unsigned int   sqlpfmem;
   unsigned char  *sqhstv[4];
   unsigned long  sqhstl[4];
            int   sqhsts[4];
            short *sqindv[4];
            int   sqinds[4];
   unsigned long  sqharm[4];
   unsigned long  *sqharc[4];
   unsigned short  sqadto[4];
   unsigned short  sqtdso[4];
} sqlstm = {13,4};

/* SQLLIB Prototypes */
extern void sqlcxt (void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlcx2t(void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlbuft(void **, char *);
extern void sqlgs2t(void **, char *);
extern void sqlorat(void **, unsigned int *, void *);

/* Forms Interface */
static const int IAPSUCC = 0;
static const int IAPFAIL = 1403;
static const int IAPFTL  = 535;
extern void sqliem(unsigned char *, signed int *);

typedef struct { unsigned short len; unsigned char arr[1]; } VARCHAR;
typedef struct { unsigned short len; unsigned char arr[1]; } varchar;

/* cud (compilation unit data) array */
static const short sqlcud0[] =
{13,4242,1,0,0,
5,0,0,1,72,0,4,1551,0,0,2,1,0,1,0,2,3,0,0,1,9,0,0,
28,0,0,2,131,0,4,1600,0,0,4,3,0,1,0,2,3,0,0,1,9,0,0,1,3,0,0,1,3,0,0,
59,0,0,3,70,0,4,1638,0,0,2,1,0,1,0,2,3,0,0,1,1,0,0,
82,0,0,4,68,0,4,1683,0,0,2,1,0,1,0,2,3,0,0,1,9,0,0,
105,0,0,5,76,0,4,1753,0,0,3,2,0,1,0,2,1,0,0,1,9,0,0,1,3,0,0,
132,0,0,6,71,0,4,1880,0,0,2,1,0,1,0,2,3,0,0,1,9,0,0,
};


/**********************************************************************/
/*                                                                    */
/*    Module Name   :  main (esembe0f.pc)                             */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. H. Mak                                      */
/*                                                                    */
/*    Callers       :  esemcn0f.c through 'system' statement          */  
/*                                                                    */
/*    Parameters    :  user id.                                       */
/*                  :  user password                                  */
/*                                                                    */
/*    Called Modules:  screen functions from 'screen.c'               */
/*                     (centre_msg, disp_err, disp_space)             */
/*                     screen functions from 'cursesX'                */
/*                     user_login and user_logout (login.pc)          */
/*                                                                    */
/*    Purpose       :  Accept user input EMC analysis data, validate  */
/*                     the input data, then write to EMC analysis     */
/*                     batch file.                                    */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/
/*   C. K. Wong     Dec-95                                            */
/*   Modify the grid input to 4 digits because 5th is always 0        */
/**********************************************************************/
/*  C. K. Wong (Eng Grad)  Dec-95                                     */
/*  ----------------------------------------------------------------- */
/*  Check Frequencies according to Band Plan requirements             */
/*            BTx(MHz)       BRx(MHz)         BRx-BTx(MHz)            */
/*  Band A    67.5-70        72.3-74.8        4.8                     */
/*  Band B    76.7-78.7      81.2-83.2        4.5                     */
/*  Band A'   138-142        150.75-154.75    12.75                   */
/*  Band B'   154.7625-156   148.6625-149.9   -6.1                    */
/*  Band C    169.5-172      165-167.5        -4.5                    */
/* ------------------------------------------------------------------ */
/*  Assumption : Tx always directly precedes Rx in item               */
/*               array.                                               */
/**********************************************************************/

#include <stdlib.h>
#include <curses.h>
#include <stdio.h>
#include <string.h>
#include <time.h>
#include <unistd.h>


#include "../include/emc.h"
#include "../include/define.h"
#include "../include/global.h"


#define  FREQ_MULTIPLE    10000
#define  CHANNEL_SEP      125         /* in the order of 100Hz */
#define  MAX_FREQ         99999.9875  /* in the order of MHz   */

#define  MAX_FLD_LEN  12              /* max. field length of input field */

#define  LOGIN_OK     0

#define  SFX                  0       /* tx mode is SFX */
#define  DFX                  1       /* tx mode is DFX */

#define  SFX_ERROR            2       /* error code for SFX mode */
#define  DFX_ERROR            3       /* error code for DFX mode */
#define  SFX_FILTER_ERROR     4       /* error code for having   */
                                      /* SFX filter in DFX mode  */

#define  SFX_ERROR_MSG        "Channel not in SFX mode"
#define  DFX_ERROR_MSG        "Channel not in DFX mode"
#define  FILTER_ERROR_MSG     "No SFX filter is allowed for DFX mode"

#define  IGNORE_ANGLE         "999"

#define  SUBDIST_MISMATCH     "Sub-district not match with input grid"

#define  LOCAL                'L'     /* local printer mode      */
#define  SYSTEM               'S'     /* system printer mode     */

#include "../include/screen.h"

#define WARNING               999


int user_login    (char *,char *,char *);                           /*20170707 Cyrus Add */
int strip_blank   (char *,char *);									/*20170707 Cyrus Add */
int get_sys_date_time(char *,char *,char *,char *);					/*20170707 Cyrus Add */
int disp_heading (char *,char *,char *);							/*20170707 Cyrus Add */
void disp_entry_scn(int,int,int);									/*20170707 Cyrus Add */
int clear_err();													/*20170707 Cyrus Add */
void refresh_screen(int,int);										/*20170707 Cyrus Add */
void init_field(FIELD *, int *);									/*20170707 Cyrus Add */
void init_field_by_init_str(FIELD *, int *);						/*20170707 Cyrus Add */
void disp_err(char *);												/*20170707 Cyrus Add */
void disp_space(int,int,int);										/*20170707 Cyrus Add */
int isalnum(int);													/*20170707 Cyrus Add */
int isdigit(int);													/*20170707 Cyrus Add */
int isalpha(int);													/*20170707 Cyrus Add */
int toupper();														/*20170707 Cyrus Add */
int chk_grid_sub_dist(int);											/*20170707 Cyrus Add */
int centre_msg(int, char *, int, int);								/*20170707 Cyrus Add */
int read_str(char *, char *, int, int, int, int *, int);			/*20170707 Cyrus Add */
int chk_tx_mode(int,int);											/*20170707 Cyrus Add */
int get_print_mode();												/*20170707 Cyrus Add */
void write_batch(FILE* , int );										/*20170707 Cyrus Add */
int user_logout();													/*20170707 Cyrus Add */
int is_space();														/*20170707 Cyrus Add */
void clear_msg();													/*20170707 Cyrus Add */

/* field validation function declarations */
int    chk_freq();
int    chk_sub_dist();
int    chk_stn_type();
int    chk_angle();
int    chk_ant_type();
int    chk_erp();
int    chk_intmod_att();
int    chk_desen_att();
int    chk_sfx_filter();

/* For field definitions, see ../include/screen.h */
/** the curr_len is set to 0 instead of 3, if not, the following will be
    an error case:

   7, 19,INTEGER,    FALSE,3, 0, NEW, "999",        "999",0,FALSE,chk_angle,

           If '12' is input to this field, then 'len' will be 2 while
           'item[i].curr_len is 3. Then the following test case will be
           'FALSE' and the outcome of curr_str is '129' instead of '12' !

                if ((item[i].curr_len < len) || is_delete(token))
                {
                    item[i].curr_len = len;
                    item[i].curr_str[item[i].curr_len] = '\0';
                }
*/

 

FIELD item[] = 
{
{   4, 19,FREQ,       TRUE, 11,5, NEW, "00000.00000","",0,FALSE,chk_freq},
{   4, 67,FREQ,       TRUE, 11,5, NEW, "00000.00000","",0,FALSE,chk_freq}, 
{   5, 19,GRID,       TRUE, 4, 0, NEW, "0000",      "",0,FALSE,(int(*)())NULL},
{   5, 36,GRID,       TRUE, 4, 0, NEW, "0000",      "",0,FALSE,(int(*)())NULL},
{   5, 67,STRING,     TRUE, 3, 0, NEW, "   ",        "",0,FALSE,chk_sub_dist},
{   6, 19,CHAR,       FALSE,1, 0, NEW, "B",          "B",1,FALSE,chk_stn_type},
{   6, 67,STRING,     TRUE, 2, 0, NEW, "GP",         "GP",2,FALSE,chk_ant_type},
{   7, 19,INTEGER,    FALSE,3, 0, NEW, "999",        "999",0,FALSE,chk_angle},
{   7, 67,INTEGER,    TRUE, 3, 0, NEW, "000",        "",0,FALSE,(int(*)())NULL},
{   8, 19,SIGN_FLOAT, FALSE,5, 1, NEW, "+14.0",      "+14",1,FALSE,chk_erp},
{   8, 67,INTEGER,    FALSE,3, 0, NEW, "000",        "",0,FALSE,(int(*)())NULL},
{   9, 19,SIGN_FLOAT, FALSE,5, 1, NEW, "+00.0",      "+",1,FALSE,chk_desen_att},
{   9, 67,SIGN_FLOAT, FALSE,5, 1, NEW, "+03.0",      "+3",1,FALSE,chk_intmod_att},
{   10,19,STRING,     FALSE,10,0, NEW, "          ", "",0,FALSE,chk_sfx_filter},
{   13,19,FREQ,       FALSE,11,5, NEW, "00000.00000","",0,FALSE,chk_freq},
{   13,67,FREQ,       FALSE,11,5, NEW, "00000.00000","",0,FALSE,chk_freq},
{   14,19,FREQ,       FALSE,11,5, NEW, "00000.00000","",0,FALSE,chk_freq},
{   14,67,FREQ,       FALSE,11,5, NEW, "00000.00000","",0,FALSE,chk_freq},
{   15,19,FREQ,       FALSE,11,5, NEW, "00000.00000","",0,FALSE,chk_freq},
{   15,67,FREQ,       FALSE,11,5, NEW, "00000.00000","",0,FALSE,chk_freq},
{   16,19,FREQ,       FALSE,11,5, NEW, "00000.00000","",0,FALSE,chk_freq},
{   16,67,FREQ,       FALSE,11,5, NEW, "00000.00000","",0,FALSE,chk_freq},
{   -1,-1,DUMMY,      TRUE, 0, 0, NEW, "",           "",0,FALSE,(int(*)())NULL}
};

/*
FIELD item[] = 
{
   4, 19,FREQ,       TRUE, 11,5, NEW, "00000.00000","",0,FALSE,chk_freq,
   4, 67,FREQ,       TRUE, 11,5, NEW, "00000.00000","",0,FALSE,chk_freq, 
   5, 19,GRID,       TRUE, 4, 0, NEW, "0000",      "",0,FALSE,(int(*)())NULL,
   5, 36,GRID,       TRUE, 4, 0, NEW, "0000",      "",0,FALSE,(int(*)())NULL,
   5, 67,STRING,     TRUE, 3, 0, NEW, "   ",        "",0,FALSE,chk_sub_dist,
   6, 19,CHAR,       FALSE,1, 0, NEW, "B",          "B",1,FALSE,chk_stn_type,
   6, 67,STRING,     TRUE, 2, 0, NEW, "GP",         "GP",2,FALSE,chk_ant_type,
   7, 19,INTEGER,    FALSE,3, 0, NEW, "999",        "999",0,FALSE,chk_angle,
   7, 67,INTEGER,    TRUE, 3, 0, NEW, "000",        "",0,FALSE,(int(*)())NULL,
   8, 19,SIGN_FLOAT, FALSE,5, 1, NEW, "+14.0",      "+14",1,FALSE,chk_erp,
   8, 67,INTEGER,    FALSE,3, 0, NEW, "000",        "",0,FALSE,(int(*)())NULL,
   9, 19,SIGN_FLOAT, FALSE,5, 1, NEW, "+00.0",      "+",1,FALSE,chk_desen_att,
   9, 67,SIGN_FLOAT, FALSE,5, 1, NEW, "+03.0",      "+3",1,FALSE,chk_intmod_att,
   10,19,STRING,     FALSE,10,0, NEW, "          ", "",0,FALSE,chk_sfx_filter,
   13,19,FREQ,       FALSE,11,5, NEW, "00000.00000","",0,FALSE,chk_freq,
   13,67,FREQ,       FALSE,11,5, NEW, "00000.00000","",0,FALSE,chk_freq,
   14,19,FREQ,       FALSE,11,5, NEW, "00000.00000","",0,FALSE,chk_freq,
   14,67,FREQ,       FALSE,11,5, NEW, "00000.00000","",0,FALSE,chk_freq,
   15,19,FREQ,       FALSE,11,5, NEW, "00000.00000","",0,FALSE,chk_freq,
   15,67,FREQ,       FALSE,11,5, NEW, "00000.00000","",0,FALSE,chk_freq,
   16,19,FREQ,       FALSE,11,5, NEW, "00000.00000","",0,FALSE,chk_freq,
   16,67,FREQ,       FALSE,11,5, NEW, "00000.00000","",0,FALSE,chk_freq,
   -1,-1,DUMMY,      TRUE, 0, 0, NEW, "",           "",0,FALSE,(int(*)())NULL
};
*/


/*
char   *item_err_msg[] = 
{
   "Freq. not in multiple of min. channel separation",
   "Freq. not in multiple of min. channel separation",
   "", "", 
   "Sub-district codeis not consistent with ease/north grid",
   "Invalid station type",
   "Invalid antenna type",
   "Invalid angle: must be between 0 and 360 degree",
   "", "", "", "", "",
   "Invalid sfx filter type",
   "Freq. not in multiple of min. channel separation",
   "Freq. not in multiple of min. channel separation",
   "Freq. not in multiple of min. channel separation",
   "Freq. not in multiple of min. channel separation",
   "Freq. not in multiple of min. channel separation",
   "Freq. not in multiple of min. channel separation",
   "Freq. not in multiple of min. channel separation",
   "Freq. not in multiple of min. channel separation",
   ""
};
*/
     
char   *src_dir;

char   *prog_id = "Aembe0f_01";
char   screen_head[50];

char   emc_uid[20];
int    tx_mode = DUMMY;

char            *getenv();

extern char msg[];

/* EXEC SQL BEGIN DECLARE SECTION; */ 


    int      o_grid_east;
    int      o_grid_north;
    int      o_degree;
    int      o_cnt;
    char     o_stn_type;
    char     o_dummy;
    /* VARCHAR  o_subdistrict[4]; */ 
struct { unsigned short len; unsigned char arr[4]; } o_subdistrict;

    /* VARCHAR  o_ant_type[3]; */ 
struct { unsigned short len; unsigned char arr[3]; } o_ant_type;

    /* VARCHAR  o_sfx_filter[11]; */ 
struct { unsigned short len; unsigned char arr[11]; } o_sfx_filter;


/* EXEC SQL END DECLARE SECTION; */ 


/* EXEC SQL INCLUDE SQLCA;
 */ 
/*
 * $Header: sqlca.h 24-apr-2003.12:50:58 mkandarp Exp $ sqlca.h 
 */

/* Copyright (c) 1985, 2003, Oracle Corporation.  All rights reserved.  */
 
/*
NAME
  SQLCA : SQL Communications Area.
FUNCTION
  Contains no code. Oracle fills in the SQLCA with status info
  during the execution of a SQL stmt.
NOTES
  **************************************************************
  ***                                                        ***
  *** This file is SOSD.  Porters must change the data types ***
  *** appropriately on their platform.  See notes/pcport.doc ***
  *** for more information.                                  ***
  ***                                                        ***
  **************************************************************

  If the symbol SQLCA_STORAGE_CLASS is defined, then the SQLCA
  will be defined to have this storage class. For example:
 
    #define SQLCA_STORAGE_CLASS extern
 
  will define the SQLCA as an extern.
 
  If the symbol SQLCA_INIT is defined, then the SQLCA will be
  statically initialized. Although this is not necessary in order
  to use the SQLCA, it is a good pgming practice not to have
  unitialized variables. However, some C compilers/OS's don't
  allow automatic variables to be init'd in this manner. Therefore,
  if you are INCLUDE'ing the SQLCA in a place where it would be
  an automatic AND your C compiler/OS doesn't allow this style
  of initialization, then SQLCA_INIT should be left undefined --
  all others can define SQLCA_INIT if they wish.

  If the symbol SQLCA_NONE is defined, then the SQLCA variable will
  not be defined at all.  The symbol SQLCA_NONE should not be defined
  in source modules that have embedded SQL.  However, source modules
  that have no embedded SQL, but need to manipulate a sqlca struct
  passed in as a parameter, can set the SQLCA_NONE symbol to avoid
  creation of an extraneous sqlca variable.
 
MODIFIED
    lvbcheng   07/31/98 -  long to int
    jbasu      12/12/94 -  Bug 217878: note this is an SOSD file
    losborne   08/11/92 -  No sqlca var if SQLCA_NONE macro set 
  Clare      12/06/84 - Ch SQLCA to not be an extern.
  Clare      10/21/85 - Add initialization.
  Bradbury   01/05/86 - Only initialize when SQLCA_INIT set
  Clare      06/12/86 - Add SQLCA_STORAGE_CLASS option.
*/
 
#ifndef SQLCA
#define SQLCA 1
 
struct   sqlca
         {
         /* ub1 */ char    sqlcaid[8];
         /* b4  */ int     sqlabc;
         /* b4  */ int     sqlcode;
         struct
           {
           /* ub2 */ unsigned short sqlerrml;
           /* ub1 */ char           sqlerrmc[70];
           } sqlerrm;
         /* ub1 */ char    sqlerrp[8];
         /* b4  */ int     sqlerrd[6];
         /* ub1 */ char    sqlwarn[8];
         /* ub1 */ char    sqlext[8];
         };

#ifndef SQLCA_NONE 
#ifdef   SQLCA_STORAGE_CLASS
SQLCA_STORAGE_CLASS struct sqlca sqlca
#else
         struct sqlca sqlca
#endif
 
#ifdef  SQLCA_INIT
         = {
         {'S', 'Q', 'L', 'C', 'A', ' ', ' ', ' '},
         sizeof(struct sqlca),
         0,
         { 0, {0}},
         {'N', 'O', 'T', ' ', 'S', 'E', 'T', ' '},
         {0, 0, 0, 0, 0, 0},
         {0, 0, 0, 0, 0, 0, 0, 0},
         {0, 0, 0, 0, 0, 0, 0, 0}
         }
#endif
         ;
#endif
 
#endif
 
/* end SQLCA */



int main(argc, argv)
int    argc;
char   **argv;
{
    char    emc_batch[150];
    char    interact_dir[100];
    char    printer_id[20];
    char    cmdline[150];
    char    *confirm_msg = "CONFIRM (Y/N)? ";
    char    err_msg[80];
    char    answer;
    FILE    *bfp;
    int     print_mode =0;                 /* print mode: LOCAL or SYSTEM      */
    int     term_code;
    int     item_cnt;                   /* total no. of items               */
    int     sfx_idx;                    /* index of sfx filter item         */
    int     subdist_idx;                /* index of sub-district item       */
    int     tx_idx, rx_idx;             /* indexes of 1st channel pair      */
    int     last_item;                  /* index of last filled-up item     */
    int     ref_channel_found = FALSE;  /* TRUE when reference channel pair */
                                        /* (1st channel pair) is found      */
    int     x_pos, y_pos;               /* current cursor position          */
    int     curr_pos;                   /* current position of input string */
    int     loop;                       /* TRUE if entry continue on        */
                                        /* current item                     */
    int     modified = FALSE;           /* TRUE when current item has been  */
                                        /* modified                         */
    int     token;                      /* user-input character             */
    int     status = !(QUIT);
    int     direction=0;                  /* field shuttle direction          */
    int     confirm = FALSE;
    int     err_flag = FALSE;
    int     warn_flag = FALSE;
    int     interactive = FALSE;
    int     file_open = FALSE;
    int     i;
    register int     j;
    struct tm  *tt;
char s[80];

    /* EXEC SQL WHENEVER NOT FOUND CONTINUE; */ 


    initscr();
    raw();
    noecho();
    clear();
    keypad(stdscr, TRUE); 

    if ((argc != 4) && (argc != 7))
    {
       sprintf(err_msg, "Usage: esembe0f user_id passwd emc_uid [-i -P printer_id]");
       goto force_exit;
    }

    if (argc == 7)
	{
        if (strcmp(argv[4], "-i") || strcmp(argv[5], "-P"))
        {
           sprintf(err_msg, "Usage: esembe0f user_id passwd emc_uid [-i -P printer_id]");
           goto force_exit;
        }
        else
        {
           strcpy(printer_id, argv[6]);
           interactive = TRUE;
        }
	}
    if (user_login(argv[1], argv[2], err_msg) != LOGIN_OK)
    {
        strip_blank(err_msg, TRAIL);
        strcat(err_msg, "  ");
        strcat(err_msg, argv[1]);
        strcat(err_msg, argv[2]);
        goto force_exit;
    }

    /* EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn; */ 


    strcpy(emc_uid, argv[3]);
    get_sys_date_time(sys_date, sys_time, yymmdd, hhmmss);

    for (i = 0; item[i].xpos != -1; i++)
    {
        /***********************************************************/
        /* the 1st encountered channel is taken as the reference   */
        /* channel, subsequent channels must have the same         */
        /* transmission mode (SFX or DFX) as the reference channel */
        /***********************************************************/
        if (!ref_channel_found)
            if (is_freq(&item[i]))
            {
                ref_channel_found = TRUE;
                tx_idx = i; rx_idx = i + 1;
            }

        if (item[i].validate == chk_sfx_filter)
            sfx_idx = i;

        if (item[i].validate == chk_sub_dist)
            subdist_idx = i;
    }

    item_cnt = i;

    emc_dir = getenv("EMC");
    src_dir = getenv("SRC");


    if (interactive == TRUE)
    {
        sprintf(interact_dir, "%s/interactive", emc_dir);
        sprintf(emc_batch, "%s/%s", interact_dir, emc_uid);
        strcpy(screen_head, "INTERACTIVE  EMC  ANALYSIS");
    }
    else
    {
        sprintf(emc_batch, "%s/batch/%s", emc_dir, yymmdd);

        if ((bfp = fopen(emc_batch, "a")) == (FILE *) NULL)
        {
            sprintf(err_msg, "Fail to open %s", emc_batch);
            goto force_exit;
        }
        strcpy(screen_head, "EMC  BATCH  ENTRY");
    }
    
    disp_heading(prog_id, screen_head, sys_date);
    disp_entry_scn(item_cnt, item_cnt, NEW_SCREEN);
 
    for (i = 0; status != QUIT; )
    {
        x_pos = item[i].xpos; 
        y_pos = item[i].ypos;

        /**********************************************************/
        /* if data type of current field is sign field, then skip */
        /* the 1st position of current string and increment       */
        /* screen x-coordinate by 1, else keep 1st position       */
        /**********************************************************/
        if (is_sign(&item[i]))
        {
            curr_pos = 1;
            x_pos++;
        }
        else
            curr_pos = 0;

        move(y_pos, x_pos);
        loop = TRUE;
        for (; loop == TRUE;)
        {
            refresh();
            token = getch();

            if (err_flag)
            {
                err_flag = FALSE;
                attrset(A_NORMAL);
                getyx(stdscr, y_pos, x_pos);
                clear_err();
                move(y_pos, x_pos);
                attrset(A_REVERSE);
            }
            if (warn_flag)
            {
                warn_flag=FALSE;
                clear_err();
                attrset(A_REVERSE);
            }
            /**************************************/
            /* Function key definitions:          */
            /*    KEY_F(1)  - quit session        */
            /*    KEY_F(2)  - confirm entry       */
            /*    KEY_F(3)  - refresh screen      */
            /*    KEY_F(4)  - clear field         */
            /*    TAB,                            */
            /*    NEWLINE,                        */
            /*    KEY_DOWN  - next field          */
            /*    CTRL_B,                         */
            /*    KEY_UP    - next field          */
            /*    KEY_LEFT  - next char position  */
            /*    KEY_RIGHT - next char position  */
            /*    DELETE,                         */
            /*    BACKSPACE - delete current char */
            /*                and back 1 position */
            /**************************************/

            switch (token)
            {
                case KEY_F(1):
                    status = QUIT;
                    loop = FALSE;
                    break;

                case KEY_F(2):
                    confirm = TRUE;
                    loop = FALSE;
                    break;

                case KEY_F(3):
                    attroff(A_REVERSE);
                    refresh_screen(item_cnt, i);
                    move(y_pos, x_pos);
                    break; 

                case KEY_F(4):
                    init_field(&item[i], &curr_pos);
                    loop = FALSE; 
                    direction = RESTART; 
                    break; 

                case KEY_UP:
                case CTRL_B:
                    if (i == 0)
                        beep();
                    else
                    {
                        loop = FALSE;
                        direction = BACKWARD;
                    }
                    break;

                case KEY_DOWN:
                case NEWLINE:
                case TAB:
/*
sprintf(s," i len req: %d %d %d", i, item[i].curr_len, item[i].required);
mvaddstr(23,0,s);
                    if ((item[i].state == NEW) && (item[i].required))
                    if ((is_sign(&item[i]) && (item[i].curr_len == 1))
                     || (!is_sign(&item[i]) && empty(&item[i]))
                    && (item[i].required))
*/
                    /*********************************************************/
                    /* beep user if he wants to skip an input-required field */
                    /*********************************************************/
                    if (is_sign(&item[i]))
                    {
                        if ((item[i].curr_len == 1) && (item[i].required))
                        {
                            err_flag = TRUE;
                            disp_err(INPUT_REQ_MSG);
                            attron(A_REVERSE);
                            break;
                        }
                    }
                    else
                        if (empty(&item[i]) && (item[i].required))
                        {
                            err_flag = TRUE;
                            disp_err(INPUT_REQ_MSG);
                            attron(A_REVERSE);
                            break;
                        }

                    if (empty(&item[i]) 
                    ||  (is_sign(&item[i]) && (item[i].curr_len == 1)))
                        strcpy(item[i].curr_str, item[i].init_str);

                    loop = FALSE;
                    direction = FORWARD;
                    break;

                case KEY_LEFT:
                    /************************************/
                    /* beep user if he has reached the  */
                    /* left-most position of a field    */
                    /************************************/
                    if (is_sign(&item[i]))
                        if (x_pos == item[i].xpos + 1)
                        {
                            beep();
                            break;
                        }

                    if (x_pos == item[i].xpos)
                        beep();
                    else
                    {
                        x_pos--; curr_pos--;
                    }
                    break;

                case KEY_RIGHT:
                    /************************************/
                    /* beep user if he has reached the  */
                    /* right boundary of current field  */
                    /************************************/
                    if (x_pos - item[i].xpos == item[i].curr_len)
                        beep();
                    else
                    {
                        x_pos++; curr_pos++;
                    }
                    break;

                case BACKSPACE:
                case DELETE:
                    /************************************/
                    /* beep user if he has reached the  */
                    /* left-most position of a field    */
                    /************************************/
                    if (is_sign(&item[i]))
                        if (x_pos == item[i].xpos + 1)
                        {
                            beep();
                            break;
                        }

                    if (x_pos == item[i].xpos)
                        beep();
                    else
                    {
                        x_pos--; curr_pos--;
                        if (is_float(&item[i]) 
                        && (item[i].curr_str[curr_pos] == DOT))
                            item[i].has_dot = FALSE;
                        move(y_pos, x_pos);
                        addch(BLANK);
                        modified = TRUE;
/*
                        if (x_pos == item[i].xpos)
                            item[i].state = NEW;
                        else
                            item[i].state = MODIFIED;
*/
                    }
                    break;

                case DOT:
                    /******************************************************/
                    /* beep user if data type of current field is integer */
                    /******************************************************/
                    if (is_int(&item[i]))
                    {
                        beep();
                        break;
                    }

                    /********************************************************/
                    /* beep user if data type of current field is float and */
                    /* user has already input one dot                       */
                    /********************************************************/
                    if (is_float(&item[i]))
					{	
                        if (item[i].has_dot)
                        {
                            beep();
                            break;
                        }
                        else
                            item[i].has_dot = TRUE;
					}
                    /* if  */
                    if (item[i].state == NEW)
					{
                        if (is_sign(&item[i]))
                        {
                            if (curr_pos == 1)
                            {
                                disp_space(y_pos, x_pos, item[i].fld_len - 1);
                                move(y_pos, x_pos);
                            }
                        }
                        else
                            if (curr_pos == 0)
                            {
                                disp_space(y_pos, x_pos, item[i].fld_len);
                                move(y_pos, x_pos);
                            }
					}
                    addch(token);
                    item[i].curr_str[curr_pos] = token;
                    x_pos++; curr_pos++;
                    modified = TRUE;
                    item[i].state = MODIFIED;

                    if (x_pos - item[i].xpos == item[i].fld_len)
                    {
                        item[i].curr_str[item[i].fld_len] = '\0';
                        item[i].curr_len = item[i].fld_len;
                        loop = FALSE;
                        direction = FORWARD;
                    }

                    break;

                default:
                    if ((!isalnum(token)) && (token != MINUS) 
                    &&  (token != PLUS))
                    {
                        beep();
                        break;
                    }

                    if (!isdigit(token))
                    {
/*
mvaddstr(23, 0, "not digit");
*/
                        if (is_sign(&item[i]))
                        {
                            if ((token != MINUS) && (token != PLUS))
                            {
                                beep();
                                break;
                            }
                            else
                                if (curr_pos > 1)
                                {
                                    beep();
                                    break;
                                }
                        }
                        else
/*
sprintf(s, "should not be integer: %d %d %s", i, item[i].type, item[i].init_str);
mvaddstr(22, 0, s);
*/
                            if ((item[i].type != CHAR) 
                            &&  (item[i].type != STRING))
                            {
                                beep();
                                break;
                            }
                            else
                                if (isalpha(token))
                                    token = toupper(token); 
                    }
                    else
						
                        if (item[i].state == NEW)
						{
                            if (is_sign(&item[i]))
                            {
                                if (curr_pos == 1)
                                {
                                    disp_space(y_pos, x_pos, item[i].fld_len-1);
                                    move(y_pos, x_pos);
                                }
                            }
                            else
                                if (curr_pos == 0)
                                {
                                    disp_space(y_pos, x_pos, item[i].fld_len);
                                    move(y_pos, x_pos);
                                }
						}
                    if (((token == MINUS) || (token == PLUS)) 
                    &&  is_sign(&item[i]))
                    {
                        x_pos--; curr_pos--;  /* because we don't want to  */
                                              /* move cursor to 1 position */
                                              /* this statement is used    */
                                              /* to complement the         */
                                              /* 'x_pos++; curr_pos++'     */
                                              /* a few lines below         */
                        if (token == MINUS)
                        {
                            item[i].curr_str[0] = MINUS;
                            move(y_pos, x_pos);
                            addch(MINUS);
                        }
                            
                    }
                    else
                    {
                        if (item[i].state == MODIFIED)
                            if (item[i].curr_str[curr_pos] == DOT)
                                item[i].has_dot = FALSE;
                        item[i].curr_str[curr_pos] = token;
                        addch(token);
                    }

/*
sprintf(s, "modified");
mvaddstr(22, 0, s);
*/
                    if ((token != MINUS) && (token != PLUS))
                        item[i].state = MODIFIED;

                    modified = TRUE;
                    x_pos++; curr_pos++;

                    if (x_pos - item[i].xpos == item[i].fld_len)
                    {
                        item[i].curr_str[item[i].fld_len] = '\0';
                        item[i].curr_len = item[i].fld_len;
                        loop = FALSE;
                        direction = FORWARD;
                    }
                
                    break;
            }

            refresh();
            if (loop == FALSE)
            {
                int    (*check_item)();

                check_item = item[i].validate;
                if (check_item != (int(*)())NULL)
                {
                    if ( (direction != BACKWARD) && (!empty(&item[i])))
                    {
                        switch((*check_item)(&i, err_msg))
                        {
                            case ERROR:
                                err_flag = TRUE;
                                disp_err(err_msg);
                                attron(A_REVERSE);
                                loop = FALSE;
                                direction = RESTART;
                                if (confirm)
                                    confirm = FALSE;
                                break;

                            case WARNING:
                                warn_flag=TRUE;
                                break;
                        }

                        if ((check_item == chk_sub_dist) && !(err_flag))
						{	
                            if (chk_grid_sub_dist(i) == ERROR)
                            {
                                err_flag = TRUE;
                                disp_err(SUBDIST_MISMATCH);
                                attron(A_REVERSE);
                                loop = FALSE;
                                direction = RESTART;
                                if (confirm)
                                    confirm = FALSE;
                            }
						}
                    }
                }

                if (confirm)
                {
                    for (j = 0; j < item_cnt; j++)
                        if (is_sign(&item[j]))
                        {
                            if ((item[j].curr_len == 1) 
                            &&  (item[j].required == TRUE))
                                {
                                    confirm = FALSE;
                                    direction = RESTART;
                                    err_flag = TRUE;
                                    disp_err(INPUT_REQ_MSG);
                                    attron(A_REVERSE);
                                    i = j;
                                    break;
                                }
                        }
                        else
                            if (empty(&item[j]) && (item[j].required == TRUE))
                            {
                                confirm = FALSE;
                                direction = RESTART;
                                err_flag = TRUE;
                                disp_err(INPUT_REQ_MSG);
                                attron(A_REVERSE);
                                i = j;
                                break;
                            }
                }

                switch (direction)
                {
                    case FORWARD:
                        i++;
                        break;
                    case BACKWARD:
                        i--;
                        break;
                    case RESTART:
                        break;
                }

                continue;
            }
            
            if (modified && (!err_flag))
            {
                int    len = x_pos - item[i].xpos;
   
                if ((item[i].curr_len < len) || is_delete(token))
                {
                    item[i].curr_len = len;
                    item[i].curr_str[item[i].curr_len] = '\0';
                }

                /********************************************************/
                /* determine the tx mode of the reference channel pair  */
                /********************************************************/
                if ((i == tx_idx) && !empty(&item[rx_idx]))
                {
                    double  tx_freq, rx_freq;

                    tx_mode = SFX;
                    tx_freq = atof(item[i].curr_str);
                    rx_freq = atof(item[rx_idx].curr_str);
                    if (abs(tx_freq - rx_freq) > FREQ_EPSILON)
                        tx_mode = DFX;
                }
                if (i == rx_idx)
                {
                    double  tx_freq, rx_freq;

                    tx_mode = SFX;
                    tx_freq = atof(item[tx_idx].curr_str);
                    rx_freq = atof(item[i].curr_str);
                    if (abs(tx_freq - rx_freq) > FREQ_EPSILON)
                        tx_mode = DFX;
                }
/*
disp_space(22,0,80);
sprintf(s,"i mode: %d %d", i, tx_mode);
mvaddstr(22,0,s);
*/

                modified = FALSE;
            }
  
            move(y_pos, x_pos);
        }

		
		
		

		
		
        if (((i == item_cnt) || confirm) && (!err_flag))
        {
			
			
			
            i = 0;
            confirm = FALSE;
            centre_msg(19, confirm_msg, A_NORMAL, A_REVERSE);
			
			
			
            getyx(stdscr, y_pos, x_pos);
            x_pos += 2;
            disp_space(y_pos, x_pos, 1);
	    attroff(A_REVERSE);
            move(y_pos, x_pos);
            refresh();
	    read_str(&answer, "", 1, 0, 0, &term_code, A_REVERSE);
            disp_space(19, 0, 80);
            attron(A_REVERSE);
			
		//printf( "**In 3 centre_msg(19, \n\r"  ); /*Cyrus Debug*/	
			
		//-----------------------------------------------------------			
		//-----------------------------------------------------------	
	
            if (toupper(answer) == 'Y')
            {
                int             status;
                register int    k;

                /**************************************/
                /* determine the last filled-up field */
                /**************************************/
                for (k = item_cnt-1; is_freq(&item[k]) && empty(&item[k]); k--)
                        ;
                last_item = k;
				
				
			
                for (j = 0; j <= last_item; )
                {
                    if (is_freq(&item[j]))
                        if ((status = chk_tx_mode(j, sfx_idx)) != OK)
                        {
                            err_flag = TRUE;
                            switch (status)
                            {
                                case SFX_ERROR:
									printf( "**ERROR SFX_ERROR , \n\r"  ); /*Cyrus Debug*/	
                                    disp_err(SFX_ERROR_MSG);
                                    break;
                                case DFX_ERROR:
									printf( "**ERROR DFX_ERROR , \n\r"  ); /*Cyrus Debug*/	
                                    disp_err(DFX_ERROR_MSG);
                                    break;
                                case SFX_FILTER_ERROR:								
									printf( "**ERROR SFX_FILTER_ERROR , \n\r"  ); /*Cyrus Debug*/
                                    disp_err(FILTER_ERROR_MSG);
                                    j = sfx_idx;
                                    break;
                            }
                            i = j;
/*
disp_space(21,0,80);
sprintf(s,"i error: %d %d", i, status);
mvaddstr(21,0,s);
*/
                            break;
                        }
                        else
                            j += 2;
                    else
                        j++;
                }
                   
				   
				   
			//--Cut3-----------------------------------	   
				  
				 
				   
                if (!err_flag)
                {
                    int dummy;    /* dummy variable for label next_step */

					
					
					
					
					
					
					//Cut4-------------------------------------------------------------
					
					
                    /*************************************/
                    /* for interactive EMC analysis only */
                    /*************************************/
                    if (interactive == TRUE)
                    {
						
						
						
                        if (file_open == FALSE)
                        {
                            if(    (   bfp = fopen(emc_batch, "r")   ) != (FILE *) NULL)
                            {
								//printf( "emc_uid%s\n\r", emc_uid  ); /*Cyrus Debug*/	
								//printf( "emc_batch%s\n\r", emc_batch  ); /*Cyrus Debug*/
								
                               sprintf(err_msg, 
										"EMC analysis for '%s' is running, press any key to continue", emc_uid);

                                       //"EMC analysis for '%s' is running, press any key to continue|%s", bfp,emc_batch);
									   
									   
									   
                               disp_err(err_msg);
                               err_flag = TRUE;
                               fclose(bfp);
                               goto next_step;
                            }
                            else
                              if((bfp = fopen(emc_batch, "w")) == (FILE *) NULL)
                              {
                                 sprintf(err_msg, "Fail to open %s", emc_batch);
                                 goto force_exit;
                              }

							  
							  
                            file_open = TRUE;
							
						
							
                            print_mode = get_print_mode();
							
							//printf( "After print_mode|%c\n\r", print_mode  ); /*Cyrus Debug*/
							
                            attrset(A_REVERSE);
							
							//printf( "After %lu \n\r",A_REVERSE  ); /*Cyrus Debug*/
							//printf( "After attrset\n\r"  ); /*Cyrus Debug*/
                        }
						
//printf( "Exit get_print_mode 0010 \n\r"  ); /*Cyrus Debug*/						
						
		/*Cyrus Debug*/
system("echo \"esembe0f After get_print_mode \" >> /tmp/debug");
sprintf(msg, "echo \"%d\" >> /tmp/debug", print_mode);
system(msg);
									
						
                    }

//printf( "Exit get_print_mode 0011 \n\r"  ); /*Cyrus Debug*/	
					
//Cut4-------------------------------------------------------------					
					
					
					
/*   C. K. Wong     Dec-95                                            */
strcat(item[2].curr_str,"0");
//printf( "After strcat1\n\r"  ); /*Cyrus Debug*/
strcat(item[3].curr_str,"0");
//printf( "After strcat2\n\r"  ); /*Cyrus Debug*/

                    write_batch(bfp, last_item);  //write into /interactive/ELSO
					
					
					printf( "**After write_batch 013\n\r"  ); /*Cyrus Debug*/
					
					int cot1=0;
					int cot2=0;
					
					printf( "Before 014 For item_cnt=%d\n\r", item_cnt  ); /*Cyrus Debug*/
					
                    for (j = 0; j < item_cnt; j++)
                        init_field_by_init_str(&item[j], &curr_pos);					
                    tx_mode = DUMMY;

					//printf( "In For Loop%d\n\r" , j ); /*Cyrus Debug*/
					
					printf( "Before 015 exit For %d\n\r", interactive  ); /*Cyrus Debug*/
					
                    if (interactive == TRUE)
                    {
						
printf( "Before 015 a interactive = true\n\r"  ); /*Cyrus Debug*/					
					
                      fclose(bfp);
                      if (print_mode == LOCAL)
                      {
						  
						  printf( "Before 015 b print_mode == LOCAL\n\r"  ); /*Cyrus Debug*/		
						  
                        sprintf(cmdline,
                       "%s/esemba0x %s %s -P %s %s/%s -i -l %s &",
                       src_dir, argv[1], argv[2], printer_id, interact_dir,
                       "printout", emc_uid);
                      }
                      else
                      {
						  printf( "Before 015 c print_mode <> LOCAL\n\r"  ); /*Cyrus Debug*/
						  
                        sprintf(cmdline,
                       "%s/esemba0x %s %s -P %s %s/%s -i -s %s &",
                       src_dir, argv[1], argv[2], printer_id, interact_dir,
                       "printout", emc_uid);
                      }
					  
					  
					  printf( "Before 016 out printmode \n\r"  ); /*Cyrus Debug*/
						  
					  
					  /*  Show cmdline detail
					   printf( "**important cmdline=%s\n\r", cmdline  ); 
					  
					   sprintf(cmdline,"%s/esemba0x %s %s -P %s %s/%s -i -s %s &",
                       src_dir,  =/export/home/<USER>/emc_cyrus
					   argv[1],  =emc
					   argv[2],  =emc
					   printer_id,   =printer1
					   interact_dir, =/export/home/<USER>/interactive
                       "printout",   =printout   
					   emc_uid       = ELSO
					  */
					  //**important cmdline=/export/home/<USER>/emc_cyrus/esemba0x emc emc -P printer1 /export/home/<USER>/interactive/printout -i -l ELSO &
					  
					   printf( "esembe0f 017 cmdline=%s \n\r", cmdline  ); /*Cyrus Debug*/

					   sprintf(msg, "echo \"esembe0f%s%s 017 cmdline=%s\" >> /tmp/debug", sys_date ,sys_time, cmdline  );	
					   system(msg);
					   
					   
						system(cmdline); //*****important***** direct to esemba0x
					   
					  printf( "Before 018 end  cmdline=%s \n\r", cmdline  ); /*Cyrus Debug*/
					   sprintf(msg, "echo \"esembe0f%s%s 018 cmdline=%s\" >> /tmp/debug", sys_date ,sys_time, cmdline  );	
					   system(msg);
					  
                      file_open = FALSE;
					  
                    }
next_step:
                    dummy = 0;    /* dummy statement for label next_step */
					
					




					
				//printf( "Exit for\n\r"  ); /*Cyrus Debug*/
					
					
                }
				//printf( "Exit 1 () \n\r"  ); /*Cyrus Debug*/
				
				
				
				//--Cut3-----------------------------------
				
				
				//printf( "*****in esemba0f FINISH esemba0x () after next_step:  cut3 *****\n\r"  ); /*Cyrus Debug*/
				
				
            }
			//printf( "Exit 2 () \n\r"  ); /*Cyrus Debug*/
					
					
					
			
 //----------------------------------------------------
//-----------------------------------------------------------			

			
        }
		//printf( "Exit 3 () \n\r"  ); /*Cyrus Debug*/
    }

	//printf( "Exit ALL () \n\r"  ); /*Cyrus Debug*/
	
    attroff(A_BOLD);
    clear();
    endwin();
    fclose(bfp);
    user_logout();
   // exit(0);


sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);

	sprintf(msg, "echo \"**ERROR** %s main %s\" >> /tmp/debug", sys_date , sqlca.sqlerrm.sqlerrmc  );	
	system(msg);
	
force_exit:
    strip_blank(err_msg, TRAIL);
	printf( "**end2 , \n\r"  ); /*Cyrus Debug*/
    disp_err(err_msg);
    getch(); clear(); refresh();
    endwin();
    exit(1);
}


/**************************************************************/
/* Display new entry screen or refresh current screen to user */
/**************************************************************/

void disp_entry_scn(item_cnt, curr_cnt, mode)
int    item_cnt;
int    curr_cnt;
int    mode;
{
    char    tmp_str[80];
    register int    i;

    attrset(A_NORMAL);
    mvaddstr(4,  0, "TX FREQ (MHz)");
    mvaddstr(4, 48, "RX FREQ (MHz)");
    mvaddstr(5,  0, "GRID    (EAST)              (NORTH)");
    mvaddstr(5, 48, "SUB-DISTRICT");
    mvaddstr(6,  0, "STATION TYPE");
    mvaddstr(6, 48, "ANTENNA TYPE");
    mvaddstr(7,  0, "AZIMUTH OF MAX RAD");
    mvaddstr(7, 48, "HALT (M)");
    mvaddstr(8,  0, "ERP (DBW)");
    mvaddstr(8, 48, "FEEDER LOSS (dB)");
    mvaddstr(9,  0, "DESENSIT ADJ (dB)");
    mvaddstr(9, 48, "INTERMOD ADJ (dB)");
    mvaddstr(10, 0, "SFX FILTER TYPE");
 
    mvaddstr(12, 22, "(MHz)");
    mvaddstr(12, 70, "(MHz)");
    for (i = 13; i < 17; i++)
    {
        mvaddstr(i,  0, "TX FREQ");
        mvaddstr(i, 48, "RX FREQ");
    }
 
    sprintf(tmp_str, "%s",
"F1-Quit   F2-Confirm   F3-Refresh   F4-Clear field   UP-Prev   DOWN/<Tab>-next");
    centre_msg(21, tmp_str, A_BOLD, A_REVERSE);

    if (mode == REFRESH)
    {
        for (i = 0; i < curr_cnt; i++)
        {
            disp_space(item[i].ypos, item[i].xpos, item[i].fld_len);
            mvaddstr(item[i].ypos, item[i].xpos, item[i].curr_str);
        }
        for (i = curr_cnt; i < item_cnt; i++)
            if (item[i].state == NEW)
                mvaddstr(item[i].ypos, item[i].xpos, item[i].init_str);
            else
            {
                disp_space(item[i].ypos, item[i].xpos, item[i].fld_len);
                mvaddstr(item[i].ypos, item[i].xpos, item[i].curr_str);
            }
    }
    else
        for (i = 0; i < item_cnt; i++)
            mvaddstr(item[i].ypos, item[i].xpos, item[i].init_str);

    refresh();

}


/**************************************************************/
/* call 'disp_entry_scn' to refresh current screen to user    */
/**************************************************************/

void refresh_screen(item_cnt, curr_cnt)
int item_cnt;
int curr_cnt;
{
    clear();
    disp_heading(prog_id, screen_head, sys_date);
    disp_entry_scn(item_cnt, curr_cnt, REFRESH);
}


/*************************************************/
/* if current field is numeric field, initialise */
/* it to its initial value, if it is an alpha-   */
/* numeric value, initialise it with NULL char   */
/*************************************************/

void init_field(p_item, curr_pos)
FIELD *p_item;
int   *curr_pos;
{
    if (is_num(p_item))
        init_field_by_init_str(p_item, curr_pos);
    else
    {
        disp_space(p_item->ypos, p_item->xpos, p_item->fld_len);
        memset(p_item->curr_str, 0, p_item->fld_len);
        p_item->curr_len = *curr_pos = 0;

        if (!disp_only(p_item))
            p_item->state = NEW;
    }
}


/*************************************************/
/* initialise current field to its initial value */
/*************************************************/

void init_field_by_init_str(p_item, curr_pos)
FIELD *p_item;
int   *curr_pos;
{
char s[40];
    mvaddstr(p_item->ypos, p_item->xpos, p_item->init_str);
    if (is_sign(p_item))
        p_item->curr_len = *curr_pos = 1;
    else
    {
        p_item->curr_len = *curr_pos = 0;
        if (is_string(p_item))
        {
            /***********************************************/
            /* if there is no default value for this field */
            /* (init_str is filled with spaces),           */
            /* set curr_len to 0, else set it to string    */
            /* length of init_str                          */
            /***********************************************/
            if (is_space(p_item->init_str))
                p_item->curr_len = 0;
            else
                p_item->curr_len = strlen(p_item->init_str);
        }
        else
            p_item->curr_len = 0;
    }

    strcpy(p_item->curr_str, p_item->init_str);
    
    if (!disp_only(p_item))
    {
        p_item->state = NEW;
        if (is_float(p_item))
            p_item->has_dot = FALSE;
    }
}


/**************************************************************
* check that input frequency is of multiple of CHANNEL_SEP   *
**************************************************************

int    chk_freq(curr_cnt)
int    *curr_cnt;
{
    double  freq;
    long    i_freq;

    freq = atof(item[*curr_cnt].curr_str) * FREQ_MULTIPLE;
    i_freq = (long)freq;
    if ((freq - i_freq) > FREQ_EPSILON)
        return ERROR;

    if (i_freq % CHANNEL_SEP)
        return ERROR;

    return OK;
}
*/


/**************************************************************/
/* check input frequency                                      */
/**************************************************************/

int    chk_freq(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{
    double  freq;
    long    i_freq;
    double  Rx_freq,Tx_freq;

    freq = atof(item[*curr_cnt].curr_str);
    if ((freq - MAX_FREQ) > FREQ_EPSILON)
    {
        sprintf(err_msg, "Frequency must be between 0 and %-11.5lf", MAX_FREQ);
        return ERROR;
    }

    freq = freq * FREQ_MULTIPLE;
    i_freq = (long)(freq + .5);

    if (abs(freq - i_freq) > FREQ_EPSILON)
    {
        sprintf(err_msg, 
                "Frequency not in multiple of min. channel separation");
        return ERROR;
    }

    if (i_freq % CHANNEL_SEP)
    {
        sprintf(err_msg, 
                "Frequency not in multiple of min. channel separation");
        return ERROR;
    }
/*  ----------------------------------------------------- */
/*  Date modification : Dec 1995                          */
/*  Programmer        : CK Wong (Eng Grad)                */
/*  ----------------------------------------------------- */
/*  Check Frequencies according to Band Plan requirements */
/*            BTx(MHz)       BRx(MHz)         BRx-BTx(MHz)*/
/*  Band A    67.5-70        72.3-74.8        4.8         */
/*  Band B    76.7-78.7      81.2-83.2        4.5         */
/*  Band A'   138-142        150.75-154.75    12.75       */
/*  Band B'   154.7625-156   148.6625-149.9   -6.1        */
/*  Band C    169.5-172      165-167.5        -4.5        */
/* ------------------------------------------------------ */
/*  Assumption : Tx always directly precedes Rx in item   */
/*               array.                                   */
/* ------------------------------------------------------ */

    if(item[*curr_cnt].validate==chk_freq && 
       item[*curr_cnt-1].validate==chk_freq )
    {
        Rx_freq = atof(item[*curr_cnt].curr_str);
        Tx_freq = atof(item[*curr_cnt-1].curr_str);
        
        if(Tx_freq>(double)67.5 && Tx_freq<=(double)70)
        {
            if( (Rx_freq-Tx_freq-4.8)>1E-9 || (Rx_freq-Tx_freq-4.8)<-1E-9 )
            {
                sprintf(err_msg, 
                        "RX-TX=4.8 for TX in Band A (67.5 to 70)");
                disp_err(err_msg);
                return WARNING;
            } 
        }

        if(Tx_freq>(double)76.7 && Tx_freq<=(double)78.7)
        {
            if( (Rx_freq-Tx_freq-4.5)>1E-9 || (Rx_freq-Tx_freq-4.5)<-1E-9 )
            {
                sprintf(err_msg, 
                        "RX-TX=4.5 for TX in Band B (76.7 to 78.7)"); 
                disp_err(err_msg);
                return WARNING;
            }
        }

        if(Tx_freq>(double)138 && Tx_freq<=(double)142)
        {
            if( (Rx_freq-Tx_freq-12.75)>1E-9 || (Rx_freq-Tx_freq-12.75)<-1E-9 )
            {
                sprintf(err_msg, 
                        "RX-TX=12.75 for TX in Band A' (138 to 142)"); 
                disp_err(err_msg);
                return WARNING;
            }
        }

        if(Tx_freq>(double)154.7625 && Tx_freq<=(double)156)
        {
            if( (Rx_freq-Tx_freq+6.1)>1E-9 || (Rx_freq-Tx_freq+6.1)<-1E-9 )
            { 
                sprintf(err_msg, 
                        "TX-RX=6.1 for TX in Band B' (154.7625 to 156)"); 
                disp_err(err_msg);
                return WARNING;
            }
        }

        if(Tx_freq>(double)169.5 && Tx_freq<=(double)172)
        {
            if( (Rx_freq-Tx_freq+4.5)>1E-9 || (Rx_freq-Tx_freq+4.5)<-1E-9 )
            {
                sprintf(err_msg, 
                        "TX-RX=4.5 for TX in Band C (169.5 to 172)"); 
                disp_err(err_msg);
                return WARNING;
            }
        }
    }

    return OK;
}


/****************************************************
* check that input grid can be found in TERRAIN or *
* (ieast/inorth arrays)                            *
****************************************************

int    chk_grid(curr_cnt)
int    *curr_cnt;
{
    char   err_msg[80];
    int    i;


    o_grid_east = atoi(item[*curr_cnt - 1].curr_str); 
    o_grid_north = atoi(item[*curr_cnt].curr_str); 

    EXEC SQL
         SELECT COUNT(ROWID)
         INTO   :o_cnt
         FROM   TERRAIN
         WHERE  EAST = :o_grid_east * 10
         AND    NORTH = :o_grid_north * 10;

    if (o_cnt == 0)
    {
        for (i = 0; ieast[i] != DUMMY; i++)
            if (ieast[i] >= o_grid_east)
                break;
        if (ieast[i] == DUMMY)
        {
            (*curr_cnt)--;
            return ERROR;
        }

        for (i = 0; inorth[i] != DUMMY; i++)
            if (inorth[i] >= o_grid_north) 
                break;
        if (inorth[i] == DUMMY)
        {
            (*curr_cnt)--;
            return ERROR;
        }

    }

    return OK;

sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    refresh(); beep();
    sleep(1); clear(); refresh();
    endwin();
    exit(1);
}
*/


/**********************************************************************/
/* check that input sub-district code can be found in SUBDISTRICT_TAB */
/**********************************************************************/

int    chk_sub_dist(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{
/*    strcpy(o_subdistrict.arr, item[*curr_cnt].curr_str);
    o_subdistrict.len = strlen(o_subdistrict.arr); */
	strcpy((char *)o_subdistrict.arr, item[*curr_cnt].curr_str);
    o_subdistrict.len = strlen((char *)o_subdistrict.arr);

    /* EXEC SQL
         SELECT COUNT(ROWID)
         INTO   :o_cnt
         FROM   SUBDISTRICT_TAB
         WHERE  SUBDISTRICT = :o_subdistrict; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 2;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "select count(ROWID) into :b0  from SUBDISTRICT_TAB where \
SUBDISTRICT=:b1";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )5;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_cnt;
    sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_subdistrict;
    sqlstm.sqhstl[1] = (unsigned long )6;
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    if (o_cnt == 0)
    {
        sprintf(err_msg, "Invalid Sub-district code");
        return ERROR;
    }

    return OK;

sqlerr_rtn:

	sprintf(msg, "echo \"**ERROR** %s chk_sub_dist %s\" >> /tmp/debug", sys_date , sqlca.sqlerrm.sqlerrmc  );	
	system(msg);

    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    refresh(); beep();
    sleep(1); clear(); refresh();
    endwin();
    exit(1);
}

    
/******************************************************************/
/* check that input sub-district code matches with the input grid */
/******************************************************************/

int    chk_grid_sub_dist(curr_cnt)
int    curr_cnt;
{
    char   err_msg[80];
char s[80];

/*    strcpy(o_subdistrict.arr, item[curr_cnt].curr_str);
    o_subdistrict.len = strlen(o_subdistrict.arr); */
    strcpy((char *)o_subdistrict.arr, item[curr_cnt].curr_str);
    o_subdistrict.len = strlen((char *)o_subdistrict.arr);
    
/*   C. K. Wong     Dec-95                                            */
/*   grids in Table AREA is 5 digit                                   */
    o_grid_east  = atoi(item[curr_cnt - 2].curr_str)*10;
    o_grid_north = atoi(item[curr_cnt - 1].curr_str)*10;

    /* EXEC SQL
         SELECT COUNT(ROWID)
         INTO   :o_cnt
         FROM   AREA
         WHERE  SUBDISTRICT = :o_subdistrict
         AND    (:o_grid_east BETWEEN EAST1 AND EAST2)
         AND    (:o_grid_north BETWEEN NORTH2 AND NORTH1); */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 4;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "select count(ROWID) into :b0  from AREA where ((SUBDISTRI\
CT=:b1 and :b2 between EAST1 and EAST2) and :b3 between NORTH2 and NORTH1)";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )28;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_cnt;
    sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_subdistrict;
    sqlstm.sqhstl[1] = (unsigned long )6;
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqhstv[2] = (unsigned char  *)&o_grid_east;
    sqlstm.sqhstl[2] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[2] = (         int  )0;
    sqlstm.sqindv[2] = (         short *)0;
    sqlstm.sqinds[2] = (         int  )0;
    sqlstm.sqharm[2] = (unsigned long )0;
    sqlstm.sqadto[2] = (unsigned short )0;
    sqlstm.sqtdso[2] = (unsigned short )0;
    sqlstm.sqhstv[3] = (unsigned char  *)&o_grid_north;
    sqlstm.sqhstl[3] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[3] = (         int  )0;
    sqlstm.sqindv[3] = (         short *)0;
    sqlstm.sqinds[3] = (         int  )0;
    sqlstm.sqharm[3] = (unsigned long )0;
    sqlstm.sqadto[3] = (unsigned short )0;
    sqlstm.sqtdso[3] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    if (o_cnt == 0)
        return ERROR;

    return OK;

sqlerr_rtn:

	sprintf(msg, "echo \"**ERROR** %s chk_grid_sub_dist %s\" >> /tmp/debug", sys_date , sqlca.sqlerrm.sqlerrmc  );	
	system(msg);

    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    refresh(); beep();
    sleep(1); clear(); refresh();
    endwin();
    exit(1);
}


/**************************************************************/
/* check that input station code can be found in STATION_TYPE */
/**************************************************************/

int    chk_stn_type(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{
    o_stn_type = item[*curr_cnt].curr_str[0];

    /* EXEC SQL
         SELECT COUNT(ROWID)
         INTO   :o_cnt
         FROM   STATION_TYPE
         WHERE  STATION_TYPE = :o_stn_type; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 4;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "select count(ROWID) into :b0  from STATION_TYPE where STA\
TION_TYPE=:b1";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )59;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_cnt;
    sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_stn_type;
    sqlstm.sqhstl[1] = (unsigned long )1;
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    if (o_cnt == 0)
    {
        sprintf(err_msg, "Invalid station type");
        return ERROR;
    }

    return OK;

sqlerr_rtn:

	sprintf(msg, "echo \"**ERROR** %s chk_stn_type %s\" >> /tmp/debug", sys_date , sqlca.sqlerrm.sqlerrmc  );	
	system(msg);

    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    refresh(); beep();
    sleep(1); clear(); refresh();
    endwin();
    exit(1);
}


/*****************************************************************/
/* check that input antenna type can be found in HZ_ANTENNA_GAIN */
/* if only 1 record is found, then this antenna is considered to */
/* have no horizontal antenna, and the azimuth of max. radiation */
/* field is intialised to be IGNORE_ANGLE                        */
/*****************************************************************/

int    chk_ant_type(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{
/*    strcpy(o_ant_type.arr, item[*curr_cnt].curr_str);
    o_ant_type.len = strlen(o_ant_type.arr); */
    strcpy((char *)o_ant_type.arr, item[*curr_cnt].curr_str);
    o_ant_type.len = strlen((char *)o_ant_type.arr);

    /* EXEC SQL
         SELECT COUNT(ROWID)
         INTO   :o_cnt
         FROM   HZ_ANTENNA_GAIN
         WHERE  ANTENNA = :o_ant_type; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 4;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "select count(ROWID) into :b0  from HZ_ANTENNA_GAIN where \
ANTENNA=:b1";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )82;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_cnt;
    sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_ant_type;
    sqlstm.sqhstl[1] = (unsigned long )5;
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    if (o_cnt == 0)
    {
        sprintf(err_msg, "Invalid antenna type");
        return ERROR;
    }

    if (o_cnt == 1)
    {
        strcpy(item[++(*curr_cnt)].curr_str, IGNORE_ANGLE);
        mvaddstr(item[*curr_cnt].ypos, 
                 item[*curr_cnt].xpos, 
                 item[*curr_cnt].curr_str); 
        item[*curr_cnt].curr_len = item[*curr_cnt].fld_len ;
    }

    return OK;

sqlerr_rtn:

	sprintf(msg, "echo \"**ERROR** %s chk_ant_type %s\" >> /tmp/debug", sys_date , sqlca.sqlerrm.sqlerrmc  );	
	system(msg);

    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    refresh(); beep();
    sleep(1); clear(); refresh();
    endwin();
    exit(1);
}  
    

/**************************************************/
/* check that input angle falls in 0 - 360 degree */
/**************************************************/

int    chk_angle(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{
    int    angle;

    if (strcmp(item[*curr_cnt].curr_str, IGNORE_ANGLE))
    {
        angle = atoi(item[*curr_cnt].curr_str);
        if ((angle > 359) || (angle < 0))
        {
            sprintf(err_msg, "Must be 999,  or between 0 and 360 degree");
            return ERROR;
        }
    }
 
/*    strcpy(o_ant_type.arr, item[(*curr_cnt) - 1].curr_str);
    o_ant_type.len = strlen(o_ant_type.arr); */
    strcpy((char *)o_ant_type.arr, item[(*curr_cnt) - 1].curr_str);
    o_ant_type.len = strlen((char *)o_ant_type.arr);
    o_degree = atoi(item[*curr_cnt].curr_str);

/*
system("echo \"esembe0f 1\" >> /tmp/debug");
sprintf(msg, "echo \"%s %d\" >> /tmp/debug", o_ant_type.arr, o_degree);
system(msg);
*/

    /* EXEC SQL
         SELECT 'X'
         INTO   :o_dummy
         FROM   HZ_ANTENNA_GAIN
         WHERE  ANTENNA = :o_ant_type
         AND    DEGREE = :o_degree; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 4;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "select 'X' into :b0  from HZ_ANTENNA_GAIN where (ANTENNA=\
:b1 and DEGREE=:b2)";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )105;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_dummy;
    sqlstm.sqhstl[0] = (unsigned long )1;
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_ant_type;
    sqlstm.sqhstl[1] = (unsigned long )5;
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqhstv[2] = (unsigned char  *)&o_degree;
    sqlstm.sqhstl[2] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[2] = (         int  )0;
    sqlstm.sqindv[2] = (         short *)0;
    sqlstm.sqinds[2] = (         int  )0;
    sqlstm.sqharm[2] = (unsigned long )0;
    sqlstm.sqadto[2] = (unsigned short )0;
    sqlstm.sqtdso[2] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    if (sqlca.sqlcode == NOT_FOUND)
    {
        sprintf(err_msg, "Invalid angle for antenna '%s'", o_ant_type.arr);
        return ERROR;
    }

    return OK;


sqlerr_rtn:

	sprintf(msg, "echo \"**ERROR** %s chk_angle %s\" >> /tmp/debug", sys_date , sqlca.sqlerrm.sqlerrmc  );	
	system(msg);

    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    refresh(); beep();
    sleep(1); clear(); refresh();
    endwin();
    exit(1);
}


/*********************************************************/
/* check that input ERP falls between -99.9 and 99.9 dBW */
/*********************************************************/

int    chk_erp(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{
    float   erp;

    erp = (float)atof(item[*curr_cnt].curr_str);
/*
    if ((abs(erp - 99.9) > EPSILON) || (abs(erp - -99.9) > EPSILON))
*/
    if ((erp > 99.9) || (erp < -99.9))
    {
        sprintf(err_msg, "ERP must between -99.9 and 99.9");
        return ERROR;
    }

    return OK;
}

     
/*********************************************/
/* check that input intermod ajustment level */
/* falls between -99.9 and 99.9 dB           */
/*********************************************/

int    chk_intmod_att(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{
    float   intmod_att;

    intmod_att = (float)atof(item[*curr_cnt].curr_str);
/*
    if ((abs(intmod_att - 99.9) > EPSILON) || (abs(intmod_att - -99.9) > EPSILON))
*/
    if ((intmod_att > 99.9) || (intmod_att < -99.9))
    {
        sprintf(err_msg, "intermod adjustment must between -99.9 and 99.9 dB");
        return ERROR;
    }

    return OK;
}

     
/****************************************************/
/* check that input desensitisation ajustment level */
/* falls between -99.9 and 99.9 dB                  */
/****************************************************/

int    chk_desen_att(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{
    float   desen_att;

    desen_att = (float)atof(item[*curr_cnt].curr_str);
/*
    if ((abs(desen_att - 99.9) > EPSILON) || (abs(desen_att - -99.9) > EPSILON))
*/
    if ((desen_att > 99.9) || (desen_att < -99.9))
    {
        sprintf(err_msg, "desensitisation adjustment must between -99.9 and 99.9 dB");
        return ERROR;
    }

    return OK;
}

     
/***********************************************************************/
/* check that the input SFX filter type can be found in SFX_FILTER_ATT */
/***********************************************************************/

int    chk_sfx_filter(curr_cnt, err_msg) 
int    *curr_cnt;
char   *err_msg;
{

/********************************************************************/
/* This error will be checked and alarmed when user response 'Y' to */
/* ask-for-confirm message. Therefore, DUMMY is passed back to      */
/* to reflect that this error case will be dealt with later         */
/********************************************************************/
    if (tx_mode == DFX)
        return DUMMY;

/*    strcpy(o_sfx_filter.arr, item[*curr_cnt].curr_str);
    o_sfx_filter.len = strlen(o_sfx_filter.arr); */
    strcpy((char *)o_sfx_filter.arr, item[*curr_cnt].curr_str);
    o_sfx_filter.len = strlen((char *)o_sfx_filter.arr);

    /* EXEC SQL
         SELECT COUNT(ROWID)
         INTO   :o_cnt
         FROM   SFX_FILTER_ATT
         WHERE  FILTER_TYPE = :o_sfx_filter; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 4;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "select count(ROWID) into :b0  from SFX_FILTER_ATT where F\
ILTER_TYPE=:b1";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )132;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_cnt;
    sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_sfx_filter;
    sqlstm.sqhstl[1] = (unsigned long )13;
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    if (o_cnt == 0)
    {
        sprintf(err_msg, "Invalid sfx filter type");
        return ERROR;
    }

    return OK;

sqlerr_rtn:

	sprintf(msg, "echo \"**ERROR** %s chk_sfx_filter %s\" >> /tmp/debug", sys_date , sqlca.sqlerrm.sqlerrmc  );	
	system(msg);

    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    refresh(); beep();
    sleep(1); clear(); refresh();
    endwin();
    exit(1);
}  


/***************************************************************/
/* check that the input channel is of the same tx mode as the  */
/* reference channel, also if the tx mode of the reference     */
/* channel is DFX, check that SFX FILTER field should be empty */
/***************************************************************/

int chk_tx_mode(curr_cnt, sfx_idx)
int    curr_cnt;
int    sfx_idx;
{
    double  tx_freq, rx_freq;
    int     channel;

    tx_freq = atof(item[curr_cnt].curr_str);
    rx_freq = atof(item[curr_cnt+1].curr_str);
    channel = (abs(tx_freq - rx_freq) > FREQ_EPSILON)? DFX : SFX;

    if ((tx_mode == SFX) && (channel == DFX))
            return SFX_ERROR;
        
    if (tx_mode == DFX)
	{
        if (channel == SFX)
            return DFX_ERROR;
        else
            if (!empty(&item[sfx_idx]))
                return SFX_FILTER_ERROR;
	}

    return OK;
}


int get_print_mode()
{
    char   print_mode;
    int    term_code;
    int    y_pos, x_pos;

    clear_msg(19);
    centre_msg(19, "PRINT MODE ('L'ocal or 'S'ystem) :", A_NORMAL, A_REVERSE);
    refresh();
    getyx(stdscr, y_pos, x_pos);
    x_pos += 2;
    attrset(A_REVERSE);
    disp_space(y_pos, x_pos, 1);
    move(y_pos, x_pos);
    refresh();

	
	
	
	
    read_str(&print_mode, "", 1, TRM_CVTLOW, 0, &term_code, A_REVERSE);

    while ((print_mode != LOCAL) && (print_mode != SYSTEM))
    {
        disp_err("Invalid print mode");
        attrset(A_REVERSE);
        move(y_pos, x_pos);
        refresh();
        read_str(&print_mode, "", 1, TRM_CVTLOW, 0, &term_code, A_REVERSE);
    }

    clear_msg(19);
    return(print_mode);
}


/*****************************************************************/
/* write input batch EMC data to EMC batch file which is located */
/* in $EMC/batch and the system date is used as the name of the  */
/* batch file                                                    */
/*****************************************************************/

void write_batch(bfp, last_item)
FILE   *bfp;
int    last_item;
{
    char   fmt[15];
    register int    i;


    fprintf(bfp, "%-19s", emc_uid);

    for (i = 0; i <= last_item; i++)
        if (!is_freq(&item[i]))
            switch (item[i].type)
            {
		case GRID:
                    sprintf(fmt, "%%0%dd", item[i].fld_len+1);
                    fprintf(bfp, fmt, atoi(item[i].curr_str));
                    break;

                case INTEGER:
                case SIGN_INTEGER:
                    sprintf(fmt, "%%0%dd", item[i].fld_len);
                    fprintf(bfp, fmt, atoi(item[i].curr_str));
                    break;

                case FLOAT:
                case SIGN_FLOAT:
                    sprintf(fmt, "%%0%d.%dlf", item[i].fld_len, 
                                               item[i].dec_place);
                    fprintf(bfp, fmt, atof(item[i].curr_str));
                    break;
         
                case CHAR:
                    fprintf(bfp, "%s", item[i].curr_str);
                    break;
         
                case STRING:
                    sprintf(fmt, "%%-%ds", item[i].fld_len);
                    fprintf(bfp, fmt, item[i].curr_str);
                    break;
            }

    for (i = 0; i <= last_item; i++)
        if (is_freq(&item[i]))
        {
            sprintf(fmt, "%%0%d.%dlf", item[i].fld_len, item[i].dec_place);
            fprintf(bfp, fmt, atof(item[i].curr_str));
        }

    fprintf(bfp, "\n");
}
