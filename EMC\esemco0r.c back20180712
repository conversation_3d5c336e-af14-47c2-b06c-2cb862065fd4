
/* Result Sets Interface */
#ifndef SQL_CRSR
#  define SQL_CRSR
  struct sql_cursor
  {
    unsigned int curocn;
    void *ptr1;
    void *ptr2;
    unsigned int magic;
  };
  typedef struct sql_cursor sql_cursor;
  typedef struct sql_cursor SQL_CURSOR;
#endif /* SQL_CRSR */

/* Thread Safety */
typedef void * sql_context;
typedef void * SQL_CONTEXT;

/* Object support */
struct sqltvn
{
  unsigned char *tvnvsn; 
  unsigned short tvnvsnl; 
  unsigned char *tvnnm;
  unsigned short tvnnml; 
  unsigned char *tvnsnm;
  unsigned short tvnsnml;
};
typedef struct sqltvn sqltvn;

struct sqladts
{
  unsigned int adtvsn; 
  unsigned short adtmode; 
  unsigned short adtnum;  
  sqltvn adttvn[1];       
};
typedef struct sqladts sqladts;

static struct sqladts sqladt = {
  1,0,0,
};

/* Binding to PL/SQL Records */
struct sqltdss
{
  unsigned int tdsvsn; 
  unsigned short tdsnum; 
  unsigned char *tdsval[1]; 
};
typedef struct sqltdss sqltdss;
static struct sqltdss sqltds =
{
  1,
  0,
};

/* File name & Package Name */
struct sqlcxp
{
  unsigned short fillen;
           char  filnam[12];
};
static const struct sqlcxp sqlfpn =
{
    11,
    "esemco0r.pc"
};


static unsigned int sqlctx = 149323;


static struct sqlexd {
   unsigned long  sqlvsn;
   unsigned int   arrsiz;
   unsigned int   iters;
   unsigned int   offset;
   unsigned short selerr;
   unsigned short sqlety;
   unsigned int   occurs;
      const short *cud;
   unsigned char  *sqlest;
      const char  *stmt;
   sqladts *sqladtp;
   sqltdss *sqltdsp;
   unsigned char  **sqphsv;
   unsigned long  *sqphsl;
            int   *sqphss;
            short **sqpind;
            int   *sqpins;
   unsigned long  *sqparm;
   unsigned long  **sqparc;
   unsigned short  *sqpadto;
   unsigned short  *sqptdso;
   unsigned int   sqlcmax;
   unsigned int   sqlcmin;
   unsigned int   sqlcincr;
   unsigned int   sqlctimeout;
   unsigned int   sqlcnowait;
            int   sqfoff;
   unsigned int   sqcmod;
   unsigned int   sqfmod;
   unsigned int   sqlpfmem;
   unsigned char  *sqhstv[10];
   unsigned long  sqhstl[10];
            int   sqhsts[10];
            short *sqindv[10];
            int   sqinds[10];
   unsigned long  sqharm[10];
   unsigned long  *sqharc[10];
   unsigned short  sqadto[10];
   unsigned short  sqtdso[10];
} sqlstm = {13,10};

/* SQLLIB Prototypes */
extern void sqlcxt (void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlcx2t(void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlbuft(void **, char *);
extern void sqlgs2t(void **, char *);
extern void sqlorat(void **, unsigned int *, void *);

/* Forms Interface */
static const int IAPSUCC = 0;
static const int IAPFAIL = 1403;
static const int IAPFTL  = 535;
extern void sqliem(unsigned char *, signed int *);

 static const char *sq0001 = 
"select SYS_CATEGORY ,SYS_TYPE ,SYS_NO ,SYS_SUFFIX ,BASE_NO ,TX_FREQ ,RX_FREQ\
 ,NVL(TONE_FREQ,0.0)  from BASE_CH where (TX_FREQ between :b0 and :b1 or RX_FR\
EQ between :b2 and :b3) order by SYS_CATEGORY,SYS_TYPE,SYS_NO,SYS_SUFFIX,TX_FR\
EQ,RX_FREQ            ";

 static const char *sq0002 = 
"select SYS_CATEGORY ,SYS_TYPE ,SYS_NO ,SYS_SUFFIX ,BASE_NO ,TX_FREQ ,RX_FREQ\
 ,NVL(TONE_FREQ,0.0)  from BASE_CH where RX_FREQ between :b0 and :b1 order by \
SYS_CATEGORY,SYS_TYPE,SYS_NO,SYS_SUFFIX,BASE_NO,CHANNEL_NO            ";

 static const char *sq0003 = 
"select SYS_CATEGORY ,SYS_TYPE ,SYS_NO ,SYS_SUFFIX ,BASE_NO ,TX_FREQ ,RX_FREQ\
 ,NVL(TONE_FREQ,0.0)  from BASE_CH where TX_FREQ between :b0 and :b1 order by \
SYS_CATEGORY,SYS_TYPE,SYS_NO,SYS_SUFFIX,BASE_NO,CHANNEL_NO            ";

 static const char *sq0007 = 
"select distinct SYS_SUFFIX  from ESMS_MAP where (SYS_ID=:b0 and SYS_SUFFIX<>\
:b1) order by SYS_SUFFIX            ";

 static const char *sq0011 = 
"select distinct SYS_SUFFIX  from ESMS_MAP where (SYS_ID=:b0 and SYS_SUFFIX<>\
:b1) order by SYS_SUFFIX            ";

 static const char *sq0015 = 
"select distinct SYS_SUFFIX  from ESMS_MAP where (SYS_ID=:b0 and SYS_SUFFIX<>\
:b1) order by SYS_SUFFIX            ";

typedef struct { unsigned short len; unsigned char arr[1]; } VARCHAR;
typedef struct { unsigned short len; unsigned char arr[1]; } varchar;

/* cud (compilation unit data) array */
static const short sqlcud0[] =
{13,4242,1,0,0,
5,0,0,1,254,0,9,240,0,0,4,4,0,1,0,1,4,0,0,1,4,0,0,1,4,0,0,1,4,0,0,
36,0,0,1,0,0,13,242,0,0,8,0,0,1,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,3,0,0,2,4,
0,0,2,4,0,0,2,4,0,0,
83,0,0,2,224,0,9,250,0,0,2,2,0,1,0,1,4,0,0,1,4,0,0,
106,0,0,2,0,0,13,252,0,0,8,0,0,1,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,3,0,0,2,4,
0,0,2,4,0,0,2,4,0,0,
153,0,0,3,224,0,9,260,0,0,2,2,0,1,0,1,4,0,0,1,4,0,0,
176,0,0,3,0,0,13,262,0,0,8,0,0,1,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,3,0,0,2,4,
0,0,2,4,0,0,2,4,0,0,
223,0,0,1,0,0,13,311,0,0,8,0,0,1,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,3,0,0,2,4,
0,0,2,4,0,0,2,4,0,0,
270,0,0,2,0,0,13,319,0,0,8,0,0,1,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,3,0,0,2,4,
0,0,2,4,0,0,2,4,0,0,
317,0,0,3,0,0,13,327,0,0,8,0,0,1,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,3,0,0,2,4,
0,0,2,4,0,0,2,4,0,0,
364,0,0,4,245,0,4,351,0,0,10,5,0,1,0,2,9,0,0,2,3,0,0,2,3,0,0,2,1,0,0,2,9,0,0,1,
1,0,0,1,9,0,0,1,9,0,0,1,9,0,0,1,3,0,0,
419,0,0,1,0,0,13,387,0,0,8,0,0,1,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,3,0,0,2,4,
0,0,2,4,0,0,2,4,0,0,
466,0,0,2,0,0,13,395,0,0,8,0,0,1,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,3,0,0,2,4,
0,0,2,4,0,0,2,4,0,0,
513,0,0,3,0,0,13,403,0,0,8,0,0,1,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,3,0,0,2,4,
0,0,2,4,0,0,2,4,0,0,
560,0,0,5,439,0,4,478,0,0,9,8,0,1,0,2,3,0,0,1,4,0,0,1,4,0,0,1,4,0,0,1,4,0,0,1,
1,0,0,1,9,0,0,1,9,0,0,1,9,0,0,
611,0,0,6,145,0,4,505,0,0,5,4,0,1,0,2,3,0,0,1,1,0,0,1,9,0,0,1,9,0,0,1,9,0,0,
646,0,0,7,112,0,9,533,0,0,2,2,0,1,0,1,3,0,0,1,9,0,0,
669,0,0,7,0,0,13,542,0,0,1,0,0,1,0,2,9,0,0,
688,0,0,7,0,0,15,547,0,0,0,0,0,1,0,
703,0,0,8,439,0,4,556,0,0,9,8,0,1,0,2,3,0,0,1,4,0,0,1,4,0,0,1,4,0,0,1,4,0,0,1,
1,0,0,1,9,0,0,1,9,0,0,1,9,0,0,
754,0,0,9,428,0,4,611,0,0,7,6,0,1,0,2,3,0,0,1,4,0,0,1,4,0,0,1,1,0,0,1,9,0,0,1,
9,0,0,1,9,0,0,
797,0,0,10,145,0,4,639,0,0,5,4,0,1,0,2,3,0,0,1,1,0,0,1,9,0,0,1,9,0,0,1,9,0,0,
832,0,0,11,112,0,9,662,0,0,2,2,0,1,0,1,3,0,0,1,9,0,0,
855,0,0,11,0,0,13,666,0,0,1,0,0,1,0,2,9,0,0,
874,0,0,11,0,0,15,671,0,0,0,0,0,1,0,
889,0,0,12,428,0,4,681,0,0,7,6,0,1,0,2,3,0,0,1,4,0,0,1,4,0,0,1,1,0,0,1,9,0,0,1,
9,0,0,1,9,0,0,
932,0,0,13,428,0,4,736,0,0,7,6,0,1,0,2,3,0,0,1,4,0,0,1,4,0,0,1,1,0,0,1,9,0,0,1,
9,0,0,1,9,0,0,
975,0,0,14,145,0,4,764,0,0,5,4,0,1,0,2,3,0,0,1,1,0,0,1,9,0,0,1,9,0,0,1,9,0,0,
1010,0,0,15,112,0,9,787,0,0,2,2,0,1,0,1,3,0,0,1,9,0,0,
1033,0,0,15,0,0,13,791,0,0,1,0,0,1,0,2,9,0,0,
1052,0,0,15,0,0,15,796,0,0,0,0,0,1,0,
1067,0,0,16,428,0,4,806,0,0,7,6,0,1,0,2,3,0,0,1,4,0,0,1,4,0,0,1,1,0,0,1,9,0,0,
1,9,0,0,1,9,0,0,
1110,0,0,17,130,0,4,853,0,0,6,4,0,1,0,2,9,0,0,2,9,0,0,1,1,0,0,1,9,0,0,1,9,0,0,
1,9,0,0,
1149,0,0,18,62,0,4,894,0,0,2,1,0,1,0,2,9,0,0,1,9,0,0,
1172,0,0,1,0,0,13,936,0,0,8,0,0,1,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,3,0,0,2,
4,0,0,2,4,0,0,2,4,0,0,
1219,0,0,2,0,0,13,944,0,0,8,0,0,1,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,3,0,0,2,
4,0,0,2,4,0,0,2,4,0,0,
1266,0,0,3,0,0,13,952,0,0,8,0,0,1,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,3,0,0,2,
4,0,0,2,4,0,0,2,4,0,0,
1313,0,0,1,0,0,15,1036,0,0,0,0,0,1,0,
1328,0,0,2,0,0,15,1040,0,0,0,0,0,1,0,
1343,0,0,3,0,0,15,1044,0,0,0,0,0,1,0,
};


#line 1 "esemco0r.pc"
/**********************************************************************/
/*                                                                    */
/*    Module Name   :  cochaninf (esemco0r.pc)                        */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. H. Mak                                      */
/*                                                                    */
/*    Callers       :  main (esemba0x.c)                              */  
/*                                                                    */
/*    Parameters    :                                                 */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  Print all stations that share the same channel */
/*                     as the proposed station.                       */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/
/*   C. K. Wong     Dec-95                                            */
/*   Modify to screen the pager cochannel report because the pager    */
/*   channel is used by one system only.                              */
/**********************************************************************/



void print_cochaninf_line();
void print_cochaninf_head();

#include <unistd.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include "../include/define.h"
#include "../include/emcext.h"
#include "../include/propext.h"
/* this is the new code */
#include "../include/existext.h"

#define  COCHANINF_LINES    35

#define  TX_RX      0
#define  RX_ONLY    1
#define  TX_ONLY    2

extern char msg[];

/* EXEC SQL BEGIN DECLARE SECTION; */ 
#line 50 "esemco0r.pc"


    double  o_tx_freq;
    double  o_rx_freq;
    double  o_tone_freq;
    double  o_tx_freq_lo;
    double  o_rx_freq_lo;
    double  o_tx_freq_hi;
    double  o_rx_freq_hi;
    char    o_sys_category;
    /* VARCHAR o_sys_type[3]; */ 
struct { unsigned short len; unsigned char arr[3]; } o_sys_type;
#line 60 "esemco0r.pc"

    /* VARCHAR o_sys_no[8]; */ 
struct { unsigned short len; unsigned char arr[8]; } o_sys_no;
#line 61 "esemco0r.pc"

    /* VARCHAR o_sys_suffix[4]; */ 
struct { unsigned short len; unsigned char arr[4]; } o_sys_suffix;
#line 62 "esemco0r.pc"

    char    o_station_type;
    int     o_base_no;
    int     o_mobile_cnt;
    /* VARCHAR o_sub_district[4]; */ 
struct { unsigned short len; unsigned char arr[4]; } o_sub_district;
#line 66 "esemco0r.pc"

    /* VARCHAR o_cancel_date[10]; */ 
struct { unsigned short len; unsigned char arr[10]; } o_cancel_date;
#line 67 "esemco0r.pc"

    int     o_grid_east;
    int     o_grid_north;
    /* VARCHAR o_client_name[49]; */ 
struct { unsigned short len; unsigned char arr[49]; } o_client_name;
#line 70 "esemco0r.pc"

    /* VARCHAR o_buss_code[5]; */ 
struct { unsigned short len; unsigned char arr[5]; } o_buss_code;
#line 71 "esemco0r.pc"

    /* VARCHAR o_buss_desc[31]; */ 
struct { unsigned short len; unsigned char arr[31]; } o_buss_desc;
#line 72 "esemco0r.pc"

    /* VARCHAR o_sys_suffix_2[4]; */ 
struct { unsigned short len; unsigned char arr[4]; } o_sys_suffix_2;
#line 73 "esemco0r.pc"

    int     o_sys_id;

/* EXEC SQL END DECLARE SECTION; */ 
#line 76 "esemco0r.pc"


/* EXEC SQL INCLUDE SQLCA;
 */ 
#line 1 "/u01/app/oracle/product/12.2.0/dbhome_1/precomp/public/SQLCA.H"
/*
 * $Header: sqlca.h 24-apr-2003.12:50:58 mkandarp Exp $ sqlca.h 
 */

/* Copyright (c) 1985, 2003, Oracle Corporation.  All rights reserved.  */
 
/*
NAME
  SQLCA : SQL Communications Area.
FUNCTION
  Contains no code. Oracle fills in the SQLCA with status info
  during the execution of a SQL stmt.
NOTES
  **************************************************************
  ***                                                        ***
  *** This file is SOSD.  Porters must change the data types ***
  *** appropriately on their platform.  See notes/pcport.doc ***
  *** for more information.                                  ***
  ***                                                        ***
  **************************************************************

  If the symbol SQLCA_STORAGE_CLASS is defined, then the SQLCA
  will be defined to have this storage class. For example:
 
    #define SQLCA_STORAGE_CLASS extern
 
  will define the SQLCA as an extern.
 
  If the symbol SQLCA_INIT is defined, then the SQLCA will be
  statically initialized. Although this is not necessary in order
  to use the SQLCA, it is a good pgming practice not to have
  unitialized variables. However, some C compilers/OS's don't
  allow automatic variables to be init'd in this manner. Therefore,
  if you are INCLUDE'ing the SQLCA in a place where it would be
  an automatic AND your C compiler/OS doesn't allow this style
  of initialization, then SQLCA_INIT should be left undefined --
  all others can define SQLCA_INIT if they wish.

  If the symbol SQLCA_NONE is defined, then the SQLCA variable will
  not be defined at all.  The symbol SQLCA_NONE should not be defined
  in source modules that have embedded SQL.  However, source modules
  that have no embedded SQL, but need to manipulate a sqlca struct
  passed in as a parameter, can set the SQLCA_NONE symbol to avoid
  creation of an extraneous sqlca variable.
 
MODIFIED
    lvbcheng   07/31/98 -  long to int
    jbasu      12/12/94 -  Bug 217878: note this is an SOSD file
    losborne   08/11/92 -  No sqlca var if SQLCA_NONE macro set 
  Clare      12/06/84 - Ch SQLCA to not be an extern.
  Clare      10/21/85 - Add initialization.
  Bradbury   01/05/86 - Only initialize when SQLCA_INIT set
  Clare      06/12/86 - Add SQLCA_STORAGE_CLASS option.
*/
 
#ifndef SQLCA
#define SQLCA 1
 
struct   sqlca
         {
         /* ub1 */ char    sqlcaid[8];
         /* b4  */ int     sqlabc;
         /* b4  */ int     sqlcode;
         struct
           {
           /* ub2 */ unsigned short sqlerrml;
           /* ub1 */ char           sqlerrmc[70];
           } sqlerrm;
         /* ub1 */ char    sqlerrp[8];
         /* b4  */ int     sqlerrd[6];
         /* ub1 */ char    sqlwarn[8];
         /* ub1 */ char    sqlext[8];
         };

#ifndef SQLCA_NONE 
#ifdef   SQLCA_STORAGE_CLASS
SQLCA_STORAGE_CLASS struct sqlca sqlca
#else
         struct sqlca sqlca
#endif
 
#ifdef  SQLCA_INIT
         = {
         {'S', 'Q', 'L', 'C', 'A', ' ', ' ', ' '},
         sizeof(struct sqlca),
         0,
         { 0, {0}},
         {'N', 'O', 'T', ' ', 'S', 'E', 'T', ' '},
         {0, 0, 0, 0, 0, 0},
         {0, 0, 0, 0, 0, 0, 0, 0},
         {0, 0, 0, 0, 0, 0, 0, 0}
         }
#endif
         ;
#endif
 
#endif
 
/* end SQLCA */

#line 79 "esemco0r.pc"


int cochaninf()
{
    char   co_fname[120];
    char   cmdline[140];
    char   curr_sys[20], prev_sys[20];
    double prev_tx_freq = -1.0;
    double prev_rx_freq = -1.0;
    double prev_tone_freq = -1.0;
    FILE   *cfp;
    int    prev_base_no = -1;
    int    line_cnt = 0;
    int    page_cnt = 1;
    int    skip_lines;
    int    tx_mode;
    int    i;
    int    tot_mobile_cnt = 0;
    int	   tmp_mobile_cnt;


printf( "esemco0r 051 start cochaninf()   \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s  051 start cochaninf() \" >> /tmp/debug", sys_date ,sys_time );	
system(msg);	
	
	
	
	
    /* EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn; */ 
#line 107 "esemco0r.pc"

    /* EXEC SQL WHENEVER NOT FOUND CONTINUE; */ 
#line 108 "esemco0r.pc"

	
printf( "esemco0r 052 start cochaninf()   \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s  052 start cochaninf() \" >> /tmp/debug", sys_date ,sys_time );	
system(msg);		
	

/*
#ifdef DEBUG
    printf("cochaninf\n");
fflush(stdout);
#endif
*/

    prev_sys[0] = '\0';
    o_tx_freq = prop_tx_freq; 
    o_rx_freq = prop_rx_freq; 

    fprintf(afp, "\n** Co-channel Stations Report\n");

	
	
printf( "esemco0r 053 o_tx_freq=%f,o_rx_freq=%f   \n\r",o_tx_freq,o_rx_freq  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s  053 o_tx_freq=%f,o_rx_freq=%f \" >> /tmp/debug", sys_date ,sys_time,o_tx_freq,o_rx_freq );	
system(msg);

printf( "esemco0r 054 FREQ_EPSILON=%f   \n\r",FREQ_EPSILON  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s  054 FREQ_EPSILON=%f \" >> /tmp/debug", sys_date ,sys_time,FREQ_EPSILON);	
system(msg);		
	
	
    if ((o_tx_freq > FREQ_EPSILON) && (o_rx_freq > FREQ_EPSILON))
    {
        tx_mode = TX_RX;

        o_tx_freq_lo = o_tx_freq - FREQ_EPSILON;
        o_tx_freq_hi = o_tx_freq + FREQ_EPSILON;
        o_rx_freq_lo = o_rx_freq - FREQ_EPSILON;
        o_rx_freq_hi = o_rx_freq + FREQ_EPSILON;

printf( "esemco0r 054a if CO01 FREQ_EPSILON=%f   \n\r",FREQ_EPSILON  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s  054a if CO01 FREQ_EPSILON=%f \" >> /tmp/debug", sys_date ,sys_time,FREQ_EPSILON);

sprintf(msg, "echo \"esemco0r%s %s  054a-- EXEC SQL   o_rx_freq_lo=%lf o_rx_freq_hi=%lf  o_tx_freq_lo=%lf o_tx_freq_hi=%lf  \" >> /tmp/debug", sys_date ,sys_time, o_rx_freq_lo ,o_rx_freq_hi,o_tx_freq_lo,o_tx_freq_hi );	
system(msg);			
		
        /* EXEC SQL DECLARE CO01 CURSOR FOR
             SELECT SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO,
                    TX_FREQ, RX_FREQ, NVL(TONE_FREQ, 0.0)
             FROM   BASE_CH
             WHERE  TX_FREQ between :o_tx_freq_lo and :o_tx_freq_hi
             OR     RX_FREQ between :o_rx_freq_lo and :o_rx_freq_hi
             ORDER  BY
                    SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, TX_FREQ,
                    RX_FREQ; */ 
#line 160 "esemco0r.pc"

					
printf( "esemco0r 054a2 if CO01 FREQ_EPSILON=%f   \n\r",FREQ_EPSILON  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s  054a2 if CO01 FREQ_EPSILON=%f \" >> /tmp/debug", sys_date ,sys_time,FREQ_EPSILON);	
system(msg);						
					
    }
    else
    {
        if (o_tx_freq <= FREQ_EPSILON)    /* Rx-only channel */
        {
            tx_mode = RX_ONLY;

            o_rx_freq_lo = o_rx_freq - FREQ_EPSILON;
            o_rx_freq_hi = o_rx_freq + FREQ_EPSILON;

			
printf( "esemco0r 054b if CO02 FREQ_EPSILON=%f   \n\r",FREQ_EPSILON  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s  054b if CO02 FREQ_EPSILON=%f \" >> /tmp/debug", sys_date ,sys_time,FREQ_EPSILON);	
system(msg);

sprintf(msg, "echo \"esemco0r%s %s  054b-- EXEC SQL   o_rx_freq_lo=%lf o_rx_freq_hi=%lf  o_tx_freq_lo=%lf o_tx_freq_hi=%lf  \" >> /tmp/debug", sys_date ,sys_time, o_rx_freq_lo ,o_rx_freq_hi,o_tx_freq_lo,o_tx_freq_hi );	
system(msg);	

			
            /* EXEC SQL DECLARE CO02 CURSOR FOR
                 SELECT SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO,
                        TX_FREQ, RX_FREQ, NVL(TONE_FREQ, 0.0)
                 FROM   BASE_CH
                 WHERE  RX_FREQ between :o_rx_freq_lo and :o_rx_freq_hi
                 ORDER  BY
                        SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO, 
                        CHANNEL_NO; */ 
#line 188 "esemco0r.pc"

						
printf( "esemco0r 054b2 if CO02 FREQ_EPSILON=%f   \n\r",FREQ_EPSILON  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s  054b2 if CO02 FREQ_EPSILON=%f \" >> /tmp/debug", sys_date ,sys_time,FREQ_EPSILON);	
system(msg);

						
        }
        else    /* Tx-only channel */
        {
            tx_mode = TX_ONLY;

            o_tx_freq_lo = o_tx_freq - FREQ_EPSILON;
            o_tx_freq_hi = o_tx_freq + FREQ_EPSILON;

			
printf( "esemco0r 054c if CO02 FREQ_EPSILON=%f   \n\r",FREQ_EPSILON  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s  054c if CO02 FREQ_EPSILON=%f \" >> /tmp/debug", sys_date ,sys_time,FREQ_EPSILON);	
system(msg);

			
            /* EXEC SQL DECLARE CO03 CURSOR FOR
                 SELECT SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO,
                        TX_FREQ, RX_FREQ, NVL(TONE_FREQ, 0.0)
                 FROM   BASE_CH
                 WHERE  TX_FREQ between :o_tx_freq_lo and :o_tx_freq_hi
                 ORDER  BY
                        SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO, 
                        CHANNEL_NO; */ 
#line 216 "esemco0r.pc"

						
		
					
		
printf( "esemco0r 054c2 if CO02 o_tx_freq_lo=%f  o_tx_freq_lo=%f  \n\r",o_tx_freq_lo, o_tx_freq_lo ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s  054c2 if CO02 o_tx_freq_lo=%f  o_tx_freq_lo=%f \" >> /tmp/debug", sys_date ,sys_time,o_tx_freq_lo,o_tx_freq_lo);	
system(msg);			
						
						
        }
		
		
    }

	
printf( "esemco0r 055 if start switch tx_mode=%d  TX_RX is %d |RX_ONLY is %d |TX_ONLY is %d  \n\r",tx_mode,TX_RX,RX_ONLY,TX_ONLY  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   055 if start switch tx_mode=%d \" >> /tmp/debug", sys_date ,sys_time,tx_mode);	
system(msg);	
	
	
	
    switch (tx_mode)
    {
        case TX_RX:
		
printf( "esemco0r 055a switch (tx_mode) case TX_RX:  \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s  055a switch (tx_mode) case TX_RX:  \" >> /tmp/debug", sys_date ,sys_time);	
system(msg);		
		
            /* EXEC SQL OPEN CO01; */ 
#line 240 "esemco0r.pc"

{
#line 240 "esemco0r.pc"
            struct sqlexd sqlstm;
#line 240 "esemco0r.pc"
            sqlstm.sqlvsn = 13;
#line 240 "esemco0r.pc"
            sqlstm.arrsiz = 4;
#line 240 "esemco0r.pc"
            sqlstm.sqladtp = &sqladt;
#line 240 "esemco0r.pc"
            sqlstm.sqltdsp = &sqltds;
#line 240 "esemco0r.pc"
            sqlstm.stmt = sq0001;
#line 240 "esemco0r.pc"
            sqlstm.iters = (unsigned int  )1;
#line 240 "esemco0r.pc"
            sqlstm.offset = (unsigned int  )5;
#line 240 "esemco0r.pc"
            sqlstm.selerr = (unsigned short)1;
#line 240 "esemco0r.pc"
            sqlstm.sqlpfmem = (unsigned int  )0;
#line 240 "esemco0r.pc"
            sqlstm.cud = sqlcud0;
#line 240 "esemco0r.pc"
            sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 240 "esemco0r.pc"
            sqlstm.sqlety = (unsigned short)4352;
#line 240 "esemco0r.pc"
            sqlstm.occurs = (unsigned int  )0;
#line 240 "esemco0r.pc"
            sqlstm.sqcmod = (unsigned int )0;
#line 240 "esemco0r.pc"
            sqlstm.sqhstv[0] = (unsigned char  *)&o_tx_freq_lo;
#line 240 "esemco0r.pc"
            sqlstm.sqhstl[0] = (unsigned long )sizeof(double);
#line 240 "esemco0r.pc"
            sqlstm.sqhsts[0] = (         int  )0;
#line 240 "esemco0r.pc"
            sqlstm.sqindv[0] = (         short *)0;
#line 240 "esemco0r.pc"
            sqlstm.sqinds[0] = (         int  )0;
#line 240 "esemco0r.pc"
            sqlstm.sqharm[0] = (unsigned long )0;
#line 240 "esemco0r.pc"
            sqlstm.sqadto[0] = (unsigned short )0;
#line 240 "esemco0r.pc"
            sqlstm.sqtdso[0] = (unsigned short )0;
#line 240 "esemco0r.pc"
            sqlstm.sqhstv[1] = (unsigned char  *)&o_tx_freq_hi;
#line 240 "esemco0r.pc"
            sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
#line 240 "esemco0r.pc"
            sqlstm.sqhsts[1] = (         int  )0;
#line 240 "esemco0r.pc"
            sqlstm.sqindv[1] = (         short *)0;
#line 240 "esemco0r.pc"
            sqlstm.sqinds[1] = (         int  )0;
#line 240 "esemco0r.pc"
            sqlstm.sqharm[1] = (unsigned long )0;
#line 240 "esemco0r.pc"
            sqlstm.sqadto[1] = (unsigned short )0;
#line 240 "esemco0r.pc"
            sqlstm.sqtdso[1] = (unsigned short )0;
#line 240 "esemco0r.pc"
            sqlstm.sqhstv[2] = (unsigned char  *)&o_rx_freq_lo;
#line 240 "esemco0r.pc"
            sqlstm.sqhstl[2] = (unsigned long )sizeof(double);
#line 240 "esemco0r.pc"
            sqlstm.sqhsts[2] = (         int  )0;
#line 240 "esemco0r.pc"
            sqlstm.sqindv[2] = (         short *)0;
#line 240 "esemco0r.pc"
            sqlstm.sqinds[2] = (         int  )0;
#line 240 "esemco0r.pc"
            sqlstm.sqharm[2] = (unsigned long )0;
#line 240 "esemco0r.pc"
            sqlstm.sqadto[2] = (unsigned short )0;
#line 240 "esemco0r.pc"
            sqlstm.sqtdso[2] = (unsigned short )0;
#line 240 "esemco0r.pc"
            sqlstm.sqhstv[3] = (unsigned char  *)&o_rx_freq_hi;
#line 240 "esemco0r.pc"
            sqlstm.sqhstl[3] = (unsigned long )sizeof(double);
#line 240 "esemco0r.pc"
            sqlstm.sqhsts[3] = (         int  )0;
#line 240 "esemco0r.pc"
            sqlstm.sqindv[3] = (         short *)0;
#line 240 "esemco0r.pc"
            sqlstm.sqinds[3] = (         int  )0;
#line 240 "esemco0r.pc"
            sqlstm.sqharm[3] = (unsigned long )0;
#line 240 "esemco0r.pc"
            sqlstm.sqadto[3] = (unsigned short )0;
#line 240 "esemco0r.pc"
            sqlstm.sqtdso[3] = (unsigned short )0;
#line 240 "esemco0r.pc"
            sqlstm.sqphsv = sqlstm.sqhstv;
#line 240 "esemco0r.pc"
            sqlstm.sqphsl = sqlstm.sqhstl;
#line 240 "esemco0r.pc"
            sqlstm.sqphss = sqlstm.sqhsts;
#line 240 "esemco0r.pc"
            sqlstm.sqpind = sqlstm.sqindv;
#line 240 "esemco0r.pc"
            sqlstm.sqpins = sqlstm.sqinds;
#line 240 "esemco0r.pc"
            sqlstm.sqparm = sqlstm.sqharm;
#line 240 "esemco0r.pc"
            sqlstm.sqparc = sqlstm.sqharc;
#line 240 "esemco0r.pc"
            sqlstm.sqpadto = sqlstm.sqadto;
#line 240 "esemco0r.pc"
            sqlstm.sqptdso = sqlstm.sqtdso;
#line 240 "esemco0r.pc"
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 240 "esemco0r.pc"
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 240 "esemco0r.pc"
}

#line 240 "esemco0r.pc"


            /* EXEC SQL
                 FETCH CO01
                 INTO  :o_sys_category, :o_sys_type, :o_sys_no, 
                       :o_sys_suffix, :o_base_no, :o_tx_freq, :o_rx_freq,
                       :o_tone_freq; */ 
#line 246 "esemco0r.pc"

{
#line 242 "esemco0r.pc"
            struct sqlexd sqlstm;
#line 242 "esemco0r.pc"
            sqlstm.sqlvsn = 13;
#line 242 "esemco0r.pc"
            sqlstm.arrsiz = 8;
#line 242 "esemco0r.pc"
            sqlstm.sqladtp = &sqladt;
#line 242 "esemco0r.pc"
            sqlstm.sqltdsp = &sqltds;
#line 242 "esemco0r.pc"
            sqlstm.iters = (unsigned int  )1;
#line 242 "esemco0r.pc"
            sqlstm.offset = (unsigned int  )36;
#line 242 "esemco0r.pc"
            sqlstm.selerr = (unsigned short)1;
#line 242 "esemco0r.pc"
            sqlstm.sqlpfmem = (unsigned int  )0;
#line 242 "esemco0r.pc"
            sqlstm.cud = sqlcud0;
#line 242 "esemco0r.pc"
            sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 242 "esemco0r.pc"
            sqlstm.sqlety = (unsigned short)4352;
#line 242 "esemco0r.pc"
            sqlstm.occurs = (unsigned int  )0;
#line 242 "esemco0r.pc"
            sqlstm.sqfoff = (         int )0;
#line 242 "esemco0r.pc"
            sqlstm.sqfmod = (unsigned int )2;
#line 242 "esemco0r.pc"
            sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
#line 242 "esemco0r.pc"
            sqlstm.sqhstl[0] = (unsigned long )1;
#line 242 "esemco0r.pc"
            sqlstm.sqhsts[0] = (         int  )0;
#line 242 "esemco0r.pc"
            sqlstm.sqindv[0] = (         short *)0;
#line 242 "esemco0r.pc"
            sqlstm.sqinds[0] = (         int  )0;
#line 242 "esemco0r.pc"
            sqlstm.sqharm[0] = (unsigned long )0;
#line 242 "esemco0r.pc"
            sqlstm.sqadto[0] = (unsigned short )0;
#line 242 "esemco0r.pc"
            sqlstm.sqtdso[0] = (unsigned short )0;
#line 242 "esemco0r.pc"
            sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
#line 242 "esemco0r.pc"
            sqlstm.sqhstl[1] = (unsigned long )5;
#line 242 "esemco0r.pc"
            sqlstm.sqhsts[1] = (         int  )0;
#line 242 "esemco0r.pc"
            sqlstm.sqindv[1] = (         short *)0;
#line 242 "esemco0r.pc"
            sqlstm.sqinds[1] = (         int  )0;
#line 242 "esemco0r.pc"
            sqlstm.sqharm[1] = (unsigned long )0;
#line 242 "esemco0r.pc"
            sqlstm.sqadto[1] = (unsigned short )0;
#line 242 "esemco0r.pc"
            sqlstm.sqtdso[1] = (unsigned short )0;
#line 242 "esemco0r.pc"
            sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
#line 242 "esemco0r.pc"
            sqlstm.sqhstl[2] = (unsigned long )10;
#line 242 "esemco0r.pc"
            sqlstm.sqhsts[2] = (         int  )0;
#line 242 "esemco0r.pc"
            sqlstm.sqindv[2] = (         short *)0;
#line 242 "esemco0r.pc"
            sqlstm.sqinds[2] = (         int  )0;
#line 242 "esemco0r.pc"
            sqlstm.sqharm[2] = (unsigned long )0;
#line 242 "esemco0r.pc"
            sqlstm.sqadto[2] = (unsigned short )0;
#line 242 "esemco0r.pc"
            sqlstm.sqtdso[2] = (unsigned short )0;
#line 242 "esemco0r.pc"
            sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
#line 242 "esemco0r.pc"
            sqlstm.sqhstl[3] = (unsigned long )6;
#line 242 "esemco0r.pc"
            sqlstm.sqhsts[3] = (         int  )0;
#line 242 "esemco0r.pc"
            sqlstm.sqindv[3] = (         short *)0;
#line 242 "esemco0r.pc"
            sqlstm.sqinds[3] = (         int  )0;
#line 242 "esemco0r.pc"
            sqlstm.sqharm[3] = (unsigned long )0;
#line 242 "esemco0r.pc"
            sqlstm.sqadto[3] = (unsigned short )0;
#line 242 "esemco0r.pc"
            sqlstm.sqtdso[3] = (unsigned short )0;
#line 242 "esemco0r.pc"
            sqlstm.sqhstv[4] = (unsigned char  *)&o_base_no;
#line 242 "esemco0r.pc"
            sqlstm.sqhstl[4] = (unsigned long )sizeof(int);
#line 242 "esemco0r.pc"
            sqlstm.sqhsts[4] = (         int  )0;
#line 242 "esemco0r.pc"
            sqlstm.sqindv[4] = (         short *)0;
#line 242 "esemco0r.pc"
            sqlstm.sqinds[4] = (         int  )0;
#line 242 "esemco0r.pc"
            sqlstm.sqharm[4] = (unsigned long )0;
#line 242 "esemco0r.pc"
            sqlstm.sqadto[4] = (unsigned short )0;
#line 242 "esemco0r.pc"
            sqlstm.sqtdso[4] = (unsigned short )0;
#line 242 "esemco0r.pc"
            sqlstm.sqhstv[5] = (unsigned char  *)&o_tx_freq;
#line 242 "esemco0r.pc"
            sqlstm.sqhstl[5] = (unsigned long )sizeof(double);
#line 242 "esemco0r.pc"
            sqlstm.sqhsts[5] = (         int  )0;
#line 242 "esemco0r.pc"
            sqlstm.sqindv[5] = (         short *)0;
#line 242 "esemco0r.pc"
            sqlstm.sqinds[5] = (         int  )0;
#line 242 "esemco0r.pc"
            sqlstm.sqharm[5] = (unsigned long )0;
#line 242 "esemco0r.pc"
            sqlstm.sqadto[5] = (unsigned short )0;
#line 242 "esemco0r.pc"
            sqlstm.sqtdso[5] = (unsigned short )0;
#line 242 "esemco0r.pc"
            sqlstm.sqhstv[6] = (unsigned char  *)&o_rx_freq;
#line 242 "esemco0r.pc"
            sqlstm.sqhstl[6] = (unsigned long )sizeof(double);
#line 242 "esemco0r.pc"
            sqlstm.sqhsts[6] = (         int  )0;
#line 242 "esemco0r.pc"
            sqlstm.sqindv[6] = (         short *)0;
#line 242 "esemco0r.pc"
            sqlstm.sqinds[6] = (         int  )0;
#line 242 "esemco0r.pc"
            sqlstm.sqharm[6] = (unsigned long )0;
#line 242 "esemco0r.pc"
            sqlstm.sqadto[6] = (unsigned short )0;
#line 242 "esemco0r.pc"
            sqlstm.sqtdso[6] = (unsigned short )0;
#line 242 "esemco0r.pc"
            sqlstm.sqhstv[7] = (unsigned char  *)&o_tone_freq;
#line 242 "esemco0r.pc"
            sqlstm.sqhstl[7] = (unsigned long )sizeof(double);
#line 242 "esemco0r.pc"
            sqlstm.sqhsts[7] = (         int  )0;
#line 242 "esemco0r.pc"
            sqlstm.sqindv[7] = (         short *)0;
#line 242 "esemco0r.pc"
            sqlstm.sqinds[7] = (         int  )0;
#line 242 "esemco0r.pc"
            sqlstm.sqharm[7] = (unsigned long )0;
#line 242 "esemco0r.pc"
            sqlstm.sqadto[7] = (unsigned short )0;
#line 242 "esemco0r.pc"
            sqlstm.sqtdso[7] = (unsigned short )0;
#line 242 "esemco0r.pc"
            sqlstm.sqphsv = sqlstm.sqhstv;
#line 242 "esemco0r.pc"
            sqlstm.sqphsl = sqlstm.sqhstl;
#line 242 "esemco0r.pc"
            sqlstm.sqphss = sqlstm.sqhsts;
#line 242 "esemco0r.pc"
            sqlstm.sqpind = sqlstm.sqindv;
#line 242 "esemco0r.pc"
            sqlstm.sqpins = sqlstm.sqinds;
#line 242 "esemco0r.pc"
            sqlstm.sqparm = sqlstm.sqharm;
#line 242 "esemco0r.pc"
            sqlstm.sqparc = sqlstm.sqharc;
#line 242 "esemco0r.pc"
            sqlstm.sqpadto = sqlstm.sqadto;
#line 242 "esemco0r.pc"
            sqlstm.sqptdso = sqlstm.sqtdso;
#line 242 "esemco0r.pc"
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 242 "esemco0r.pc"
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 242 "esemco0r.pc"
}

#line 246 "esemco0r.pc"

            break;

        case RX_ONLY:
            /* EXEC SQL OPEN CO02; */ 
#line 250 "esemco0r.pc"

printf( "esemco0r 055b switch (tx_mode) case RX_ONLY:  \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s  055b switch (tx_mode) case RX_ONLY:  \" >> /tmp/debug", sys_date ,sys_time);	
system(msg);


{
#line 250 "esemco0r.pc"
            struct sqlexd sqlstm;
#line 250 "esemco0r.pc"
            sqlstm.sqlvsn = 13;
#line 250 "esemco0r.pc"
            sqlstm.arrsiz = 8;
#line 250 "esemco0r.pc"
            sqlstm.sqladtp = &sqladt;
#line 250 "esemco0r.pc"
            sqlstm.sqltdsp = &sqltds;
#line 250 "esemco0r.pc"
            sqlstm.stmt = sq0002;
#line 250 "esemco0r.pc"
            sqlstm.iters = (unsigned int  )1;
#line 250 "esemco0r.pc"
            sqlstm.offset = (unsigned int  )83;
#line 250 "esemco0r.pc"
            sqlstm.selerr = (unsigned short)1;
#line 250 "esemco0r.pc"
            sqlstm.sqlpfmem = (unsigned int  )0;
#line 250 "esemco0r.pc"
            sqlstm.cud = sqlcud0;
#line 250 "esemco0r.pc"
            sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 250 "esemco0r.pc"
            sqlstm.sqlety = (unsigned short)4352;
#line 250 "esemco0r.pc"
            sqlstm.occurs = (unsigned int  )0;
#line 250 "esemco0r.pc"
            sqlstm.sqcmod = (unsigned int )0;
#line 250 "esemco0r.pc"
            sqlstm.sqhstv[0] = (unsigned char  *)&o_rx_freq_lo;
#line 250 "esemco0r.pc"
            sqlstm.sqhstl[0] = (unsigned long )sizeof(double);
#line 250 "esemco0r.pc"
            sqlstm.sqhsts[0] = (         int  )0;
#line 250 "esemco0r.pc"
            sqlstm.sqindv[0] = (         short *)0;
#line 250 "esemco0r.pc"
            sqlstm.sqinds[0] = (         int  )0;
#line 250 "esemco0r.pc"
            sqlstm.sqharm[0] = (unsigned long )0;
#line 250 "esemco0r.pc"
            sqlstm.sqadto[0] = (unsigned short )0;
#line 250 "esemco0r.pc"
            sqlstm.sqtdso[0] = (unsigned short )0;
#line 250 "esemco0r.pc"
            sqlstm.sqhstv[1] = (unsigned char  *)&o_rx_freq_hi;
#line 250 "esemco0r.pc"
            sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
#line 250 "esemco0r.pc"
            sqlstm.sqhsts[1] = (         int  )0;
#line 250 "esemco0r.pc"
            sqlstm.sqindv[1] = (         short *)0;
#line 250 "esemco0r.pc"
            sqlstm.sqinds[1] = (         int  )0;
#line 250 "esemco0r.pc"
            sqlstm.sqharm[1] = (unsigned long )0;
#line 250 "esemco0r.pc"
            sqlstm.sqadto[1] = (unsigned short )0;
#line 250 "esemco0r.pc"
            sqlstm.sqtdso[1] = (unsigned short )0;
#line 250 "esemco0r.pc"
            sqlstm.sqphsv = sqlstm.sqhstv;
#line 250 "esemco0r.pc"
            sqlstm.sqphsl = sqlstm.sqhstl;
#line 250 "esemco0r.pc"
            sqlstm.sqphss = sqlstm.sqhsts;
#line 250 "esemco0r.pc"
            sqlstm.sqpind = sqlstm.sqindv;
#line 250 "esemco0r.pc"
            sqlstm.sqpins = sqlstm.sqinds;
#line 250 "esemco0r.pc"
            sqlstm.sqparm = sqlstm.sqharm;
#line 250 "esemco0r.pc"
            sqlstm.sqparc = sqlstm.sqharc;
#line 250 "esemco0r.pc"
            sqlstm.sqpadto = sqlstm.sqadto;
#line 250 "esemco0r.pc"
            sqlstm.sqptdso = sqlstm.sqtdso;
#line 250 "esemco0r.pc"
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 250 "esemco0r.pc"
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 250 "esemco0r.pc"
}

#line 250 "esemco0r.pc"


            /* EXEC SQL
                 FETCH CO02
                 INTO  :o_sys_category, :o_sys_type, :o_sys_no, 
                       :o_sys_suffix, :o_base_no, :o_tx_freq, :o_rx_freq,
                       :o_tone_freq; */ 
#line 256 "esemco0r.pc"

{
#line 252 "esemco0r.pc"
            struct sqlexd sqlstm;
#line 252 "esemco0r.pc"
            sqlstm.sqlvsn = 13;
#line 252 "esemco0r.pc"
            sqlstm.arrsiz = 8;
#line 252 "esemco0r.pc"
            sqlstm.sqladtp = &sqladt;
#line 252 "esemco0r.pc"
            sqlstm.sqltdsp = &sqltds;
#line 252 "esemco0r.pc"
            sqlstm.iters = (unsigned int  )1;
#line 252 "esemco0r.pc"
            sqlstm.offset = (unsigned int  )106;
#line 252 "esemco0r.pc"
            sqlstm.selerr = (unsigned short)1;
#line 252 "esemco0r.pc"
            sqlstm.sqlpfmem = (unsigned int  )0;
#line 252 "esemco0r.pc"
            sqlstm.cud = sqlcud0;
#line 252 "esemco0r.pc"
            sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 252 "esemco0r.pc"
            sqlstm.sqlety = (unsigned short)4352;
#line 252 "esemco0r.pc"
            sqlstm.occurs = (unsigned int  )0;
#line 252 "esemco0r.pc"
            sqlstm.sqfoff = (         int )0;
#line 252 "esemco0r.pc"
            sqlstm.sqfmod = (unsigned int )2;
#line 252 "esemco0r.pc"
            sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
#line 252 "esemco0r.pc"
            sqlstm.sqhstl[0] = (unsigned long )1;
#line 252 "esemco0r.pc"
            sqlstm.sqhsts[0] = (         int  )0;
#line 252 "esemco0r.pc"
            sqlstm.sqindv[0] = (         short *)0;
#line 252 "esemco0r.pc"
            sqlstm.sqinds[0] = (         int  )0;
#line 252 "esemco0r.pc"
            sqlstm.sqharm[0] = (unsigned long )0;
#line 252 "esemco0r.pc"
            sqlstm.sqadto[0] = (unsigned short )0;
#line 252 "esemco0r.pc"
            sqlstm.sqtdso[0] = (unsigned short )0;
#line 252 "esemco0r.pc"
            sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
#line 252 "esemco0r.pc"
            sqlstm.sqhstl[1] = (unsigned long )5;
#line 252 "esemco0r.pc"
            sqlstm.sqhsts[1] = (         int  )0;
#line 252 "esemco0r.pc"
            sqlstm.sqindv[1] = (         short *)0;
#line 252 "esemco0r.pc"
            sqlstm.sqinds[1] = (         int  )0;
#line 252 "esemco0r.pc"
            sqlstm.sqharm[1] = (unsigned long )0;
#line 252 "esemco0r.pc"
            sqlstm.sqadto[1] = (unsigned short )0;
#line 252 "esemco0r.pc"
            sqlstm.sqtdso[1] = (unsigned short )0;
#line 252 "esemco0r.pc"
            sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
#line 252 "esemco0r.pc"
            sqlstm.sqhstl[2] = (unsigned long )10;
#line 252 "esemco0r.pc"
            sqlstm.sqhsts[2] = (         int  )0;
#line 252 "esemco0r.pc"
            sqlstm.sqindv[2] = (         short *)0;
#line 252 "esemco0r.pc"
            sqlstm.sqinds[2] = (         int  )0;
#line 252 "esemco0r.pc"
            sqlstm.sqharm[2] = (unsigned long )0;
#line 252 "esemco0r.pc"
            sqlstm.sqadto[2] = (unsigned short )0;
#line 252 "esemco0r.pc"
            sqlstm.sqtdso[2] = (unsigned short )0;
#line 252 "esemco0r.pc"
            sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
#line 252 "esemco0r.pc"
            sqlstm.sqhstl[3] = (unsigned long )6;
#line 252 "esemco0r.pc"
            sqlstm.sqhsts[3] = (         int  )0;
#line 252 "esemco0r.pc"
            sqlstm.sqindv[3] = (         short *)0;
#line 252 "esemco0r.pc"
            sqlstm.sqinds[3] = (         int  )0;
#line 252 "esemco0r.pc"
            sqlstm.sqharm[3] = (unsigned long )0;
#line 252 "esemco0r.pc"
            sqlstm.sqadto[3] = (unsigned short )0;
#line 252 "esemco0r.pc"
            sqlstm.sqtdso[3] = (unsigned short )0;
#line 252 "esemco0r.pc"
            sqlstm.sqhstv[4] = (unsigned char  *)&o_base_no;
#line 252 "esemco0r.pc"
            sqlstm.sqhstl[4] = (unsigned long )sizeof(int);
#line 252 "esemco0r.pc"
            sqlstm.sqhsts[4] = (         int  )0;
#line 252 "esemco0r.pc"
            sqlstm.sqindv[4] = (         short *)0;
#line 252 "esemco0r.pc"
            sqlstm.sqinds[4] = (         int  )0;
#line 252 "esemco0r.pc"
            sqlstm.sqharm[4] = (unsigned long )0;
#line 252 "esemco0r.pc"
            sqlstm.sqadto[4] = (unsigned short )0;
#line 252 "esemco0r.pc"
            sqlstm.sqtdso[4] = (unsigned short )0;
#line 252 "esemco0r.pc"
            sqlstm.sqhstv[5] = (unsigned char  *)&o_tx_freq;
#line 252 "esemco0r.pc"
            sqlstm.sqhstl[5] = (unsigned long )sizeof(double);
#line 252 "esemco0r.pc"
            sqlstm.sqhsts[5] = (         int  )0;
#line 252 "esemco0r.pc"
            sqlstm.sqindv[5] = (         short *)0;
#line 252 "esemco0r.pc"
            sqlstm.sqinds[5] = (         int  )0;
#line 252 "esemco0r.pc"
            sqlstm.sqharm[5] = (unsigned long )0;
#line 252 "esemco0r.pc"
            sqlstm.sqadto[5] = (unsigned short )0;
#line 252 "esemco0r.pc"
            sqlstm.sqtdso[5] = (unsigned short )0;
#line 252 "esemco0r.pc"
            sqlstm.sqhstv[6] = (unsigned char  *)&o_rx_freq;
#line 252 "esemco0r.pc"
            sqlstm.sqhstl[6] = (unsigned long )sizeof(double);
#line 252 "esemco0r.pc"
            sqlstm.sqhsts[6] = (         int  )0;
#line 252 "esemco0r.pc"
            sqlstm.sqindv[6] = (         short *)0;
#line 252 "esemco0r.pc"
            sqlstm.sqinds[6] = (         int  )0;
#line 252 "esemco0r.pc"
            sqlstm.sqharm[6] = (unsigned long )0;
#line 252 "esemco0r.pc"
            sqlstm.sqadto[6] = (unsigned short )0;
#line 252 "esemco0r.pc"
            sqlstm.sqtdso[6] = (unsigned short )0;
#line 252 "esemco0r.pc"
            sqlstm.sqhstv[7] = (unsigned char  *)&o_tone_freq;
#line 252 "esemco0r.pc"
            sqlstm.sqhstl[7] = (unsigned long )sizeof(double);
#line 252 "esemco0r.pc"
            sqlstm.sqhsts[7] = (         int  )0;
#line 252 "esemco0r.pc"
            sqlstm.sqindv[7] = (         short *)0;
#line 252 "esemco0r.pc"
            sqlstm.sqinds[7] = (         int  )0;
#line 252 "esemco0r.pc"
            sqlstm.sqharm[7] = (unsigned long )0;
#line 252 "esemco0r.pc"
            sqlstm.sqadto[7] = (unsigned short )0;
#line 252 "esemco0r.pc"
            sqlstm.sqtdso[7] = (unsigned short )0;
#line 252 "esemco0r.pc"
            sqlstm.sqphsv = sqlstm.sqhstv;
#line 252 "esemco0r.pc"
            sqlstm.sqphsl = sqlstm.sqhstl;
#line 252 "esemco0r.pc"
            sqlstm.sqphss = sqlstm.sqhsts;
#line 252 "esemco0r.pc"
            sqlstm.sqpind = sqlstm.sqindv;
#line 252 "esemco0r.pc"
            sqlstm.sqpins = sqlstm.sqinds;
#line 252 "esemco0r.pc"
            sqlstm.sqparm = sqlstm.sqharm;
#line 252 "esemco0r.pc"
            sqlstm.sqparc = sqlstm.sqharc;
#line 252 "esemco0r.pc"
            sqlstm.sqpadto = sqlstm.sqadto;
#line 252 "esemco0r.pc"
            sqlstm.sqptdso = sqlstm.sqtdso;
#line 252 "esemco0r.pc"
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 252 "esemco0r.pc"
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 252 "esemco0r.pc"
}

#line 256 "esemco0r.pc"

            break;

        case TX_ONLY:
            /* EXEC SQL OPEN CO03; */ 
#line 260 "esemco0r.pc"

printf( "esemco0r 055c switch (tx_mode) case TX_ONLY:  \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s  055c switch (tx_mode) case TX_ONLY:  \" >> /tmp/debug", sys_date ,sys_time);	
system(msg);




{
#line 260 "esemco0r.pc"
            struct sqlexd sqlstm;
#line 260 "esemco0r.pc"
            sqlstm.sqlvsn = 13;
#line 260 "esemco0r.pc"
            sqlstm.arrsiz = 8;
#line 260 "esemco0r.pc"
            sqlstm.sqladtp = &sqladt;
#line 260 "esemco0r.pc"
            sqlstm.sqltdsp = &sqltds;
#line 260 "esemco0r.pc"
            sqlstm.stmt = sq0003;
#line 260 "esemco0r.pc"
            sqlstm.iters = (unsigned int  )1;
#line 260 "esemco0r.pc"
            sqlstm.offset = (unsigned int  )153;
#line 260 "esemco0r.pc"
            sqlstm.selerr = (unsigned short)1;
#line 260 "esemco0r.pc"
            sqlstm.sqlpfmem = (unsigned int  )0;
#line 260 "esemco0r.pc"
            sqlstm.cud = sqlcud0;
#line 260 "esemco0r.pc"
            sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 260 "esemco0r.pc"
            sqlstm.sqlety = (unsigned short)4352;
#line 260 "esemco0r.pc"
            sqlstm.occurs = (unsigned int  )0;
#line 260 "esemco0r.pc"
            sqlstm.sqcmod = (unsigned int )0;
#line 260 "esemco0r.pc"
            sqlstm.sqhstv[0] = (unsigned char  *)&o_tx_freq_lo;
#line 260 "esemco0r.pc"
            sqlstm.sqhstl[0] = (unsigned long )sizeof(double);
#line 260 "esemco0r.pc"
            sqlstm.sqhsts[0] = (         int  )0;
#line 260 "esemco0r.pc"
            sqlstm.sqindv[0] = (         short *)0;
#line 260 "esemco0r.pc"
            sqlstm.sqinds[0] = (         int  )0;
#line 260 "esemco0r.pc"
            sqlstm.sqharm[0] = (unsigned long )0;
#line 260 "esemco0r.pc"
            sqlstm.sqadto[0] = (unsigned short )0;
#line 260 "esemco0r.pc"
            sqlstm.sqtdso[0] = (unsigned short )0;
#line 260 "esemco0r.pc"
            sqlstm.sqhstv[1] = (unsigned char  *)&o_tx_freq_hi;
#line 260 "esemco0r.pc"
            sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
#line 260 "esemco0r.pc"
            sqlstm.sqhsts[1] = (         int  )0;
#line 260 "esemco0r.pc"
            sqlstm.sqindv[1] = (         short *)0;
#line 260 "esemco0r.pc"
            sqlstm.sqinds[1] = (         int  )0;
#line 260 "esemco0r.pc"
            sqlstm.sqharm[1] = (unsigned long )0;
#line 260 "esemco0r.pc"
            sqlstm.sqadto[1] = (unsigned short )0;
#line 260 "esemco0r.pc"
            sqlstm.sqtdso[1] = (unsigned short )0;
#line 260 "esemco0r.pc"
            sqlstm.sqphsv = sqlstm.sqhstv;
#line 260 "esemco0r.pc"
            sqlstm.sqphsl = sqlstm.sqhstl;
#line 260 "esemco0r.pc"
            sqlstm.sqphss = sqlstm.sqhsts;
#line 260 "esemco0r.pc"
            sqlstm.sqpind = sqlstm.sqindv;
#line 260 "esemco0r.pc"
            sqlstm.sqpins = sqlstm.sqinds;
#line 260 "esemco0r.pc"
            sqlstm.sqparm = sqlstm.sqharm;
#line 260 "esemco0r.pc"
            sqlstm.sqparc = sqlstm.sqharc;
#line 260 "esemco0r.pc"
            sqlstm.sqpadto = sqlstm.sqadto;
#line 260 "esemco0r.pc"
            sqlstm.sqptdso = sqlstm.sqtdso;
#line 260 "esemco0r.pc"
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 260 "esemco0r.pc"
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 260 "esemco0r.pc"
}

#line 260 "esemco0r.pc"


            /* EXEC SQL
                 FETCH CO03
                 INTO  :o_sys_category, :o_sys_type, :o_sys_no, 
                       :o_sys_suffix, :o_base_no, :o_tx_freq, :o_rx_freq,
                       :o_tone_freq; */ 
#line 266 "esemco0r.pc"

{
#line 262 "esemco0r.pc"
            struct sqlexd sqlstm;
#line 262 "esemco0r.pc"
            sqlstm.sqlvsn = 13;
#line 262 "esemco0r.pc"
            sqlstm.arrsiz = 8;
#line 262 "esemco0r.pc"
            sqlstm.sqladtp = &sqladt;
#line 262 "esemco0r.pc"
            sqlstm.sqltdsp = &sqltds;
#line 262 "esemco0r.pc"
            sqlstm.iters = (unsigned int  )1;
#line 262 "esemco0r.pc"
            sqlstm.offset = (unsigned int  )176;
#line 262 "esemco0r.pc"
            sqlstm.selerr = (unsigned short)1;
#line 262 "esemco0r.pc"
            sqlstm.sqlpfmem = (unsigned int  )0;
#line 262 "esemco0r.pc"
            sqlstm.cud = sqlcud0;
#line 262 "esemco0r.pc"
            sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 262 "esemco0r.pc"
            sqlstm.sqlety = (unsigned short)4352;
#line 262 "esemco0r.pc"
            sqlstm.occurs = (unsigned int  )0;
#line 262 "esemco0r.pc"
            sqlstm.sqfoff = (         int )0;
#line 262 "esemco0r.pc"
            sqlstm.sqfmod = (unsigned int )2;
#line 262 "esemco0r.pc"
            sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
#line 262 "esemco0r.pc"
            sqlstm.sqhstl[0] = (unsigned long )1;
#line 262 "esemco0r.pc"
            sqlstm.sqhsts[0] = (         int  )0;
#line 262 "esemco0r.pc"
            sqlstm.sqindv[0] = (         short *)0;
#line 262 "esemco0r.pc"
            sqlstm.sqinds[0] = (         int  )0;
#line 262 "esemco0r.pc"
            sqlstm.sqharm[0] = (unsigned long )0;
#line 262 "esemco0r.pc"
            sqlstm.sqadto[0] = (unsigned short )0;
#line 262 "esemco0r.pc"
            sqlstm.sqtdso[0] = (unsigned short )0;
#line 262 "esemco0r.pc"
            sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
#line 262 "esemco0r.pc"
            sqlstm.sqhstl[1] = (unsigned long )5;
#line 262 "esemco0r.pc"
            sqlstm.sqhsts[1] = (         int  )0;
#line 262 "esemco0r.pc"
            sqlstm.sqindv[1] = (         short *)0;
#line 262 "esemco0r.pc"
            sqlstm.sqinds[1] = (         int  )0;
#line 262 "esemco0r.pc"
            sqlstm.sqharm[1] = (unsigned long )0;
#line 262 "esemco0r.pc"
            sqlstm.sqadto[1] = (unsigned short )0;
#line 262 "esemco0r.pc"
            sqlstm.sqtdso[1] = (unsigned short )0;
#line 262 "esemco0r.pc"
            sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
#line 262 "esemco0r.pc"
            sqlstm.sqhstl[2] = (unsigned long )10;
#line 262 "esemco0r.pc"
            sqlstm.sqhsts[2] = (         int  )0;
#line 262 "esemco0r.pc"
            sqlstm.sqindv[2] = (         short *)0;
#line 262 "esemco0r.pc"
            sqlstm.sqinds[2] = (         int  )0;
#line 262 "esemco0r.pc"
            sqlstm.sqharm[2] = (unsigned long )0;
#line 262 "esemco0r.pc"
            sqlstm.sqadto[2] = (unsigned short )0;
#line 262 "esemco0r.pc"
            sqlstm.sqtdso[2] = (unsigned short )0;
#line 262 "esemco0r.pc"
            sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
#line 262 "esemco0r.pc"
            sqlstm.sqhstl[3] = (unsigned long )6;
#line 262 "esemco0r.pc"
            sqlstm.sqhsts[3] = (         int  )0;
#line 262 "esemco0r.pc"
            sqlstm.sqindv[3] = (         short *)0;
#line 262 "esemco0r.pc"
            sqlstm.sqinds[3] = (         int  )0;
#line 262 "esemco0r.pc"
            sqlstm.sqharm[3] = (unsigned long )0;
#line 262 "esemco0r.pc"
            sqlstm.sqadto[3] = (unsigned short )0;
#line 262 "esemco0r.pc"
            sqlstm.sqtdso[3] = (unsigned short )0;
#line 262 "esemco0r.pc"
            sqlstm.sqhstv[4] = (unsigned char  *)&o_base_no;
#line 262 "esemco0r.pc"
            sqlstm.sqhstl[4] = (unsigned long )sizeof(int);
#line 262 "esemco0r.pc"
            sqlstm.sqhsts[4] = (         int  )0;
#line 262 "esemco0r.pc"
            sqlstm.sqindv[4] = (         short *)0;
#line 262 "esemco0r.pc"
            sqlstm.sqinds[4] = (         int  )0;
#line 262 "esemco0r.pc"
            sqlstm.sqharm[4] = (unsigned long )0;
#line 262 "esemco0r.pc"
            sqlstm.sqadto[4] = (unsigned short )0;
#line 262 "esemco0r.pc"
            sqlstm.sqtdso[4] = (unsigned short )0;
#line 262 "esemco0r.pc"
            sqlstm.sqhstv[5] = (unsigned char  *)&o_tx_freq;
#line 262 "esemco0r.pc"
            sqlstm.sqhstl[5] = (unsigned long )sizeof(double);
#line 262 "esemco0r.pc"
            sqlstm.sqhsts[5] = (         int  )0;
#line 262 "esemco0r.pc"
            sqlstm.sqindv[5] = (         short *)0;
#line 262 "esemco0r.pc"
            sqlstm.sqinds[5] = (         int  )0;
#line 262 "esemco0r.pc"
            sqlstm.sqharm[5] = (unsigned long )0;
#line 262 "esemco0r.pc"
            sqlstm.sqadto[5] = (unsigned short )0;
#line 262 "esemco0r.pc"
            sqlstm.sqtdso[5] = (unsigned short )0;
#line 262 "esemco0r.pc"
            sqlstm.sqhstv[6] = (unsigned char  *)&o_rx_freq;
#line 262 "esemco0r.pc"
            sqlstm.sqhstl[6] = (unsigned long )sizeof(double);
#line 262 "esemco0r.pc"
            sqlstm.sqhsts[6] = (         int  )0;
#line 262 "esemco0r.pc"
            sqlstm.sqindv[6] = (         short *)0;
#line 262 "esemco0r.pc"
            sqlstm.sqinds[6] = (         int  )0;
#line 262 "esemco0r.pc"
            sqlstm.sqharm[6] = (unsigned long )0;
#line 262 "esemco0r.pc"
            sqlstm.sqadto[6] = (unsigned short )0;
#line 262 "esemco0r.pc"
            sqlstm.sqtdso[6] = (unsigned short )0;
#line 262 "esemco0r.pc"
            sqlstm.sqhstv[7] = (unsigned char  *)&o_tone_freq;
#line 262 "esemco0r.pc"
            sqlstm.sqhstl[7] = (unsigned long )sizeof(double);
#line 262 "esemco0r.pc"
            sqlstm.sqhsts[7] = (         int  )0;
#line 262 "esemco0r.pc"
            sqlstm.sqindv[7] = (         short *)0;
#line 262 "esemco0r.pc"
            sqlstm.sqinds[7] = (         int  )0;
#line 262 "esemco0r.pc"
            sqlstm.sqharm[7] = (unsigned long )0;
#line 262 "esemco0r.pc"
            sqlstm.sqadto[7] = (unsigned short )0;
#line 262 "esemco0r.pc"
            sqlstm.sqtdso[7] = (unsigned short )0;
#line 262 "esemco0r.pc"
            sqlstm.sqphsv = sqlstm.sqhstv;
#line 262 "esemco0r.pc"
            sqlstm.sqphsl = sqlstm.sqhstl;
#line 262 "esemco0r.pc"
            sqlstm.sqphss = sqlstm.sqhsts;
#line 262 "esemco0r.pc"
            sqlstm.sqpind = sqlstm.sqindv;
#line 262 "esemco0r.pc"
            sqlstm.sqpins = sqlstm.sqinds;
#line 262 "esemco0r.pc"
            sqlstm.sqparm = sqlstm.sqharm;
#line 262 "esemco0r.pc"
            sqlstm.sqparc = sqlstm.sqharc;
#line 262 "esemco0r.pc"
            sqlstm.sqpadto = sqlstm.sqadto;
#line 262 "esemco0r.pc"
            sqlstm.sqptdso = sqlstm.sqtdso;
#line 262 "esemco0r.pc"
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 262 "esemco0r.pc"
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 262 "esemco0r.pc"
}

#line 266 "esemco0r.pc"

            break;
    }

	
printf( "esemco0r 056 end switch tx_mode=%d  \n\r",tx_mode  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   056 end switch tx_mode=%d \" >> /tmp/debug", sys_date ,sys_time,tx_mode);	
system(msg);	
	
	
	
	
	
    if (sqlca.sqlcode == NOT_FOUND)
    {
        fprintf(afp, "No co-channel stations found\n");
		
printf( "esemco0r 056-A1  No co-channel stations found  \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   056-A1  No co-channel stations found  \" >> /tmp/debug", sys_date ,sys_time);	
system(msg);	
		
        goto close_cursor;
    }


sprintf(msg, "echo \"esemco0r%s%s   056-A1**  No co-channel stations found  co_fname=%s emc_dir=%s prop_tx_freq=%lf \" >> /tmp/debug", sys_date ,sys_time ,co_fname,emc_dir ,prop_tx_freq);	
system(msg);	
	
	
    sprintf(co_fname, "%s/cochannel/%.5lf.%s", emc_dir, prop_tx_freq, 
            hhmmss);
    if (   (cfp = fopen(co_fname, "w"))   == (FILE *) NULL)
    {
        printf("Fatal error: fail to open co-channel station report : %s\n",
               co_fname);
			   
printf( "esemco0r 056-A2  Fatal error: fail to open co-channel  %s/cochannel/%.5lf.%s \n\r" ,emc_dir, prop_tx_freq, hhmmss  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   Fatal error: fail to open co-channel  %s/cochannel/%.5lf.%s  \" >> /tmp/debug", sys_date ,sys_time, emc_dir, prop_tx_freq, hhmmss);	
system(msg);
			   
			   
			   
        exit(1);
    }
    

			   
printf( "esemco0r 056-END **** before for  \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s  056-END **** before for  \" >> /tmp/debug", sys_date ,sys_time);	
system(msg);
	
	
    for ( ; ; )
    {
		
        o_sys_type.arr[o_sys_type.len] = '\0';
        o_sys_no.arr[o_sys_no.len] = '\0';
        o_sys_suffix.arr[o_sys_suffix.len] = '\0';

        sprintf(curr_sys, "%c%s%s-%s", o_sys_category, o_sys_type.arr,
                o_sys_no.arr, o_sys_suffix.arr);

printf( "esemco0r 057 For begin  %c | %s | %s - %s | %s \n\r" ,o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr,curr_sys ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  057 For begin %c | %s | %s - %s | %s  \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr,curr_sys);	
system(msg);	

//printf( "esemco0r 057-a1 FREQ_EPSILON=5%lf, (abs(o_tx_freq - prev_tx_freq)=%lf,(abs(o_rx_freq - prev_rx_freq)=%lf, (abs(o_tone_freq - prev_tone_freq)=%lf  \n\r" ,FREQ_EPSILON,(abs(o_tx_freq - prev_tx_freq)),(abs(o_rx_freq - prev_rx_freq)),(abs(o_tone_freq - prev_tone_freq)) ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  057-a1 FREQ_EPSILON=5%lf, (abs(o_tx_freq - prev_tx_freq)=%d,(abs(o_rx_freq - prev_rx_freq)=%d, (abs(o_tone_freq - prev_tone_freq)=%d  \" >> /tmp/debug", sys_date ,sys_time ,FREQ_EPSILON,(abs(o_tx_freq - prev_tx_freq)),(abs(o_rx_freq - prev_rx_freq)),(abs(o_tone_freq - prev_tone_freq)));	

system(msg);
						
				
        if ((abs(o_tx_freq - prev_tx_freq) <= FREQ_EPSILON)
        &&  (abs(o_rx_freq - prev_rx_freq) <= FREQ_EPSILON)
        &&  (abs(o_tone_freq - prev_tone_freq) <= FREQ_EPSILON))
            if (!strcmp(curr_sys, prev_sys) && (o_base_no == prev_base_no))
            {
                switch (tx_mode)
                {
                    case TX_RX:
                        /* EXEC SQL
                             FETCH CO01
                             INTO  :o_sys_category, :o_sys_type, :o_sys_no, 
                                   :o_sys_suffix, :o_base_no, :o_tx_freq,
                                   :o_rx_freq, :o_tone_freq; */ 
#line 315 "esemco0r.pc"

{
#line 311 "esemco0r.pc"
                        struct sqlexd sqlstm;
#line 311 "esemco0r.pc"
                        sqlstm.sqlvsn = 13;
#line 311 "esemco0r.pc"
                        sqlstm.arrsiz = 8;
#line 311 "esemco0r.pc"
                        sqlstm.sqladtp = &sqladt;
#line 311 "esemco0r.pc"
                        sqlstm.sqltdsp = &sqltds;
#line 311 "esemco0r.pc"
                        sqlstm.iters = (unsigned int  )1;
#line 311 "esemco0r.pc"
                        sqlstm.offset = (unsigned int  )223;
#line 311 "esemco0r.pc"
                        sqlstm.selerr = (unsigned short)1;
#line 311 "esemco0r.pc"
                        sqlstm.sqlpfmem = (unsigned int  )0;
#line 311 "esemco0r.pc"
                        sqlstm.cud = sqlcud0;
#line 311 "esemco0r.pc"
                        sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 311 "esemco0r.pc"
                        sqlstm.sqlety = (unsigned short)4352;
#line 311 "esemco0r.pc"
                        sqlstm.occurs = (unsigned int  )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqfoff = (         int )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqfmod = (unsigned int )2;
#line 311 "esemco0r.pc"
                        sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
#line 311 "esemco0r.pc"
                        sqlstm.sqhstl[0] = (unsigned long )1;
#line 311 "esemco0r.pc"
                        sqlstm.sqhsts[0] = (         int  )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqindv[0] = (         short *)0;
#line 311 "esemco0r.pc"
                        sqlstm.sqinds[0] = (         int  )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqharm[0] = (unsigned long )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqadto[0] = (unsigned short )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqtdso[0] = (unsigned short )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
#line 311 "esemco0r.pc"
                        sqlstm.sqhstl[1] = (unsigned long )5;
#line 311 "esemco0r.pc"
                        sqlstm.sqhsts[1] = (         int  )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqindv[1] = (         short *)0;
#line 311 "esemco0r.pc"
                        sqlstm.sqinds[1] = (         int  )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqharm[1] = (unsigned long )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqadto[1] = (unsigned short )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqtdso[1] = (unsigned short )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
#line 311 "esemco0r.pc"
                        sqlstm.sqhstl[2] = (unsigned long )10;
#line 311 "esemco0r.pc"
                        sqlstm.sqhsts[2] = (         int  )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqindv[2] = (         short *)0;
#line 311 "esemco0r.pc"
                        sqlstm.sqinds[2] = (         int  )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqharm[2] = (unsigned long )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqadto[2] = (unsigned short )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqtdso[2] = (unsigned short )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
#line 311 "esemco0r.pc"
                        sqlstm.sqhstl[3] = (unsigned long )6;
#line 311 "esemco0r.pc"
                        sqlstm.sqhsts[3] = (         int  )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqindv[3] = (         short *)0;
#line 311 "esemco0r.pc"
                        sqlstm.sqinds[3] = (         int  )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqharm[3] = (unsigned long )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqadto[3] = (unsigned short )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqtdso[3] = (unsigned short )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqhstv[4] = (unsigned char  *)&o_base_no;
#line 311 "esemco0r.pc"
                        sqlstm.sqhstl[4] = (unsigned long )sizeof(int);
#line 311 "esemco0r.pc"
                        sqlstm.sqhsts[4] = (         int  )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqindv[4] = (         short *)0;
#line 311 "esemco0r.pc"
                        sqlstm.sqinds[4] = (         int  )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqharm[4] = (unsigned long )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqadto[4] = (unsigned short )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqtdso[4] = (unsigned short )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqhstv[5] = (unsigned char  *)&o_tx_freq;
#line 311 "esemco0r.pc"
                        sqlstm.sqhstl[5] = (unsigned long )sizeof(double);
#line 311 "esemco0r.pc"
                        sqlstm.sqhsts[5] = (         int  )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqindv[5] = (         short *)0;
#line 311 "esemco0r.pc"
                        sqlstm.sqinds[5] = (         int  )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqharm[5] = (unsigned long )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqadto[5] = (unsigned short )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqtdso[5] = (unsigned short )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqhstv[6] = (unsigned char  *)&o_rx_freq;
#line 311 "esemco0r.pc"
                        sqlstm.sqhstl[6] = (unsigned long )sizeof(double);
#line 311 "esemco0r.pc"
                        sqlstm.sqhsts[6] = (         int  )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqindv[6] = (         short *)0;
#line 311 "esemco0r.pc"
                        sqlstm.sqinds[6] = (         int  )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqharm[6] = (unsigned long )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqadto[6] = (unsigned short )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqtdso[6] = (unsigned short )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqhstv[7] = (unsigned char  *)&o_tone_freq;
#line 311 "esemco0r.pc"
                        sqlstm.sqhstl[7] = (unsigned long )sizeof(double);
#line 311 "esemco0r.pc"
                        sqlstm.sqhsts[7] = (         int  )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqindv[7] = (         short *)0;
#line 311 "esemco0r.pc"
                        sqlstm.sqinds[7] = (         int  )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqharm[7] = (unsigned long )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqadto[7] = (unsigned short )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqtdso[7] = (unsigned short )0;
#line 311 "esemco0r.pc"
                        sqlstm.sqphsv = sqlstm.sqhstv;
#line 311 "esemco0r.pc"
                        sqlstm.sqphsl = sqlstm.sqhstl;
#line 311 "esemco0r.pc"
                        sqlstm.sqphss = sqlstm.sqhsts;
#line 311 "esemco0r.pc"
                        sqlstm.sqpind = sqlstm.sqindv;
#line 311 "esemco0r.pc"
                        sqlstm.sqpins = sqlstm.sqinds;
#line 311 "esemco0r.pc"
                        sqlstm.sqparm = sqlstm.sqharm;
#line 311 "esemco0r.pc"
                        sqlstm.sqparc = sqlstm.sqharc;
#line 311 "esemco0r.pc"
                        sqlstm.sqpadto = sqlstm.sqadto;
#line 311 "esemco0r.pc"
                        sqlstm.sqptdso = sqlstm.sqtdso;
#line 311 "esemco0r.pc"
                        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 311 "esemco0r.pc"
                        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 311 "esemco0r.pc"
}

#line 315 "esemco0r.pc"

                        break;

                    case RX_ONLY:
                        /* EXEC SQL
                             FETCH CO02
                             INTO  :o_sys_category, :o_sys_type, :o_sys_no, 
                                   :o_sys_suffix, :o_base_no, :o_tx_freq,
                                   :o_rx_freq, :o_tone_freq; */ 
#line 323 "esemco0r.pc"

{
#line 319 "esemco0r.pc"
                        struct sqlexd sqlstm;
#line 319 "esemco0r.pc"
                        sqlstm.sqlvsn = 13;
#line 319 "esemco0r.pc"
                        sqlstm.arrsiz = 8;
#line 319 "esemco0r.pc"
                        sqlstm.sqladtp = &sqladt;
#line 319 "esemco0r.pc"
                        sqlstm.sqltdsp = &sqltds;
#line 319 "esemco0r.pc"
                        sqlstm.iters = (unsigned int  )1;
#line 319 "esemco0r.pc"
                        sqlstm.offset = (unsigned int  )270;
#line 319 "esemco0r.pc"
                        sqlstm.selerr = (unsigned short)1;
#line 319 "esemco0r.pc"
                        sqlstm.sqlpfmem = (unsigned int  )0;
#line 319 "esemco0r.pc"
                        sqlstm.cud = sqlcud0;
#line 319 "esemco0r.pc"
                        sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 319 "esemco0r.pc"
                        sqlstm.sqlety = (unsigned short)4352;
#line 319 "esemco0r.pc"
                        sqlstm.occurs = (unsigned int  )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqfoff = (         int )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqfmod = (unsigned int )2;
#line 319 "esemco0r.pc"
                        sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
#line 319 "esemco0r.pc"
                        sqlstm.sqhstl[0] = (unsigned long )1;
#line 319 "esemco0r.pc"
                        sqlstm.sqhsts[0] = (         int  )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqindv[0] = (         short *)0;
#line 319 "esemco0r.pc"
                        sqlstm.sqinds[0] = (         int  )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqharm[0] = (unsigned long )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqadto[0] = (unsigned short )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqtdso[0] = (unsigned short )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
#line 319 "esemco0r.pc"
                        sqlstm.sqhstl[1] = (unsigned long )5;
#line 319 "esemco0r.pc"
                        sqlstm.sqhsts[1] = (         int  )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqindv[1] = (         short *)0;
#line 319 "esemco0r.pc"
                        sqlstm.sqinds[1] = (         int  )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqharm[1] = (unsigned long )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqadto[1] = (unsigned short )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqtdso[1] = (unsigned short )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
#line 319 "esemco0r.pc"
                        sqlstm.sqhstl[2] = (unsigned long )10;
#line 319 "esemco0r.pc"
                        sqlstm.sqhsts[2] = (         int  )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqindv[2] = (         short *)0;
#line 319 "esemco0r.pc"
                        sqlstm.sqinds[2] = (         int  )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqharm[2] = (unsigned long )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqadto[2] = (unsigned short )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqtdso[2] = (unsigned short )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
#line 319 "esemco0r.pc"
                        sqlstm.sqhstl[3] = (unsigned long )6;
#line 319 "esemco0r.pc"
                        sqlstm.sqhsts[3] = (         int  )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqindv[3] = (         short *)0;
#line 319 "esemco0r.pc"
                        sqlstm.sqinds[3] = (         int  )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqharm[3] = (unsigned long )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqadto[3] = (unsigned short )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqtdso[3] = (unsigned short )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqhstv[4] = (unsigned char  *)&o_base_no;
#line 319 "esemco0r.pc"
                        sqlstm.sqhstl[4] = (unsigned long )sizeof(int);
#line 319 "esemco0r.pc"
                        sqlstm.sqhsts[4] = (         int  )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqindv[4] = (         short *)0;
#line 319 "esemco0r.pc"
                        sqlstm.sqinds[4] = (         int  )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqharm[4] = (unsigned long )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqadto[4] = (unsigned short )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqtdso[4] = (unsigned short )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqhstv[5] = (unsigned char  *)&o_tx_freq;
#line 319 "esemco0r.pc"
                        sqlstm.sqhstl[5] = (unsigned long )sizeof(double);
#line 319 "esemco0r.pc"
                        sqlstm.sqhsts[5] = (         int  )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqindv[5] = (         short *)0;
#line 319 "esemco0r.pc"
                        sqlstm.sqinds[5] = (         int  )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqharm[5] = (unsigned long )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqadto[5] = (unsigned short )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqtdso[5] = (unsigned short )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqhstv[6] = (unsigned char  *)&o_rx_freq;
#line 319 "esemco0r.pc"
                        sqlstm.sqhstl[6] = (unsigned long )sizeof(double);
#line 319 "esemco0r.pc"
                        sqlstm.sqhsts[6] = (         int  )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqindv[6] = (         short *)0;
#line 319 "esemco0r.pc"
                        sqlstm.sqinds[6] = (         int  )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqharm[6] = (unsigned long )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqadto[6] = (unsigned short )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqtdso[6] = (unsigned short )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqhstv[7] = (unsigned char  *)&o_tone_freq;
#line 319 "esemco0r.pc"
                        sqlstm.sqhstl[7] = (unsigned long )sizeof(double);
#line 319 "esemco0r.pc"
                        sqlstm.sqhsts[7] = (         int  )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqindv[7] = (         short *)0;
#line 319 "esemco0r.pc"
                        sqlstm.sqinds[7] = (         int  )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqharm[7] = (unsigned long )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqadto[7] = (unsigned short )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqtdso[7] = (unsigned short )0;
#line 319 "esemco0r.pc"
                        sqlstm.sqphsv = sqlstm.sqhstv;
#line 319 "esemco0r.pc"
                        sqlstm.sqphsl = sqlstm.sqhstl;
#line 319 "esemco0r.pc"
                        sqlstm.sqphss = sqlstm.sqhsts;
#line 319 "esemco0r.pc"
                        sqlstm.sqpind = sqlstm.sqindv;
#line 319 "esemco0r.pc"
                        sqlstm.sqpins = sqlstm.sqinds;
#line 319 "esemco0r.pc"
                        sqlstm.sqparm = sqlstm.sqharm;
#line 319 "esemco0r.pc"
                        sqlstm.sqparc = sqlstm.sqharc;
#line 319 "esemco0r.pc"
                        sqlstm.sqpadto = sqlstm.sqadto;
#line 319 "esemco0r.pc"
                        sqlstm.sqptdso = sqlstm.sqtdso;
#line 319 "esemco0r.pc"
                        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 319 "esemco0r.pc"
                        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 319 "esemco0r.pc"
}

#line 323 "esemco0r.pc"

                        break;

                    case TX_ONLY:
                        /* EXEC SQL
                             FETCH CO03
                             INTO  :o_sys_category, :o_sys_type, :o_sys_no, 
                                   :o_sys_suffix, :o_base_no, :o_tx_freq,
                                   :o_rx_freq, :o_tone_freq; */ 
#line 331 "esemco0r.pc"

{
#line 327 "esemco0r.pc"
                        struct sqlexd sqlstm;
#line 327 "esemco0r.pc"
                        sqlstm.sqlvsn = 13;
#line 327 "esemco0r.pc"
                        sqlstm.arrsiz = 8;
#line 327 "esemco0r.pc"
                        sqlstm.sqladtp = &sqladt;
#line 327 "esemco0r.pc"
                        sqlstm.sqltdsp = &sqltds;
#line 327 "esemco0r.pc"
                        sqlstm.iters = (unsigned int  )1;
#line 327 "esemco0r.pc"
                        sqlstm.offset = (unsigned int  )317;
#line 327 "esemco0r.pc"
                        sqlstm.selerr = (unsigned short)1;
#line 327 "esemco0r.pc"
                        sqlstm.sqlpfmem = (unsigned int  )0;
#line 327 "esemco0r.pc"
                        sqlstm.cud = sqlcud0;
#line 327 "esemco0r.pc"
                        sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 327 "esemco0r.pc"
                        sqlstm.sqlety = (unsigned short)4352;
#line 327 "esemco0r.pc"
                        sqlstm.occurs = (unsigned int  )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqfoff = (         int )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqfmod = (unsigned int )2;
#line 327 "esemco0r.pc"
                        sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
#line 327 "esemco0r.pc"
                        sqlstm.sqhstl[0] = (unsigned long )1;
#line 327 "esemco0r.pc"
                        sqlstm.sqhsts[0] = (         int  )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqindv[0] = (         short *)0;
#line 327 "esemco0r.pc"
                        sqlstm.sqinds[0] = (         int  )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqharm[0] = (unsigned long )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqadto[0] = (unsigned short )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqtdso[0] = (unsigned short )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
#line 327 "esemco0r.pc"
                        sqlstm.sqhstl[1] = (unsigned long )5;
#line 327 "esemco0r.pc"
                        sqlstm.sqhsts[1] = (         int  )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqindv[1] = (         short *)0;
#line 327 "esemco0r.pc"
                        sqlstm.sqinds[1] = (         int  )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqharm[1] = (unsigned long )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqadto[1] = (unsigned short )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqtdso[1] = (unsigned short )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
#line 327 "esemco0r.pc"
                        sqlstm.sqhstl[2] = (unsigned long )10;
#line 327 "esemco0r.pc"
                        sqlstm.sqhsts[2] = (         int  )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqindv[2] = (         short *)0;
#line 327 "esemco0r.pc"
                        sqlstm.sqinds[2] = (         int  )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqharm[2] = (unsigned long )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqadto[2] = (unsigned short )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqtdso[2] = (unsigned short )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
#line 327 "esemco0r.pc"
                        sqlstm.sqhstl[3] = (unsigned long )6;
#line 327 "esemco0r.pc"
                        sqlstm.sqhsts[3] = (         int  )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqindv[3] = (         short *)0;
#line 327 "esemco0r.pc"
                        sqlstm.sqinds[3] = (         int  )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqharm[3] = (unsigned long )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqadto[3] = (unsigned short )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqtdso[3] = (unsigned short )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqhstv[4] = (unsigned char  *)&o_base_no;
#line 327 "esemco0r.pc"
                        sqlstm.sqhstl[4] = (unsigned long )sizeof(int);
#line 327 "esemco0r.pc"
                        sqlstm.sqhsts[4] = (         int  )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqindv[4] = (         short *)0;
#line 327 "esemco0r.pc"
                        sqlstm.sqinds[4] = (         int  )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqharm[4] = (unsigned long )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqadto[4] = (unsigned short )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqtdso[4] = (unsigned short )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqhstv[5] = (unsigned char  *)&o_tx_freq;
#line 327 "esemco0r.pc"
                        sqlstm.sqhstl[5] = (unsigned long )sizeof(double);
#line 327 "esemco0r.pc"
                        sqlstm.sqhsts[5] = (         int  )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqindv[5] = (         short *)0;
#line 327 "esemco0r.pc"
                        sqlstm.sqinds[5] = (         int  )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqharm[5] = (unsigned long )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqadto[5] = (unsigned short )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqtdso[5] = (unsigned short )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqhstv[6] = (unsigned char  *)&o_rx_freq;
#line 327 "esemco0r.pc"
                        sqlstm.sqhstl[6] = (unsigned long )sizeof(double);
#line 327 "esemco0r.pc"
                        sqlstm.sqhsts[6] = (         int  )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqindv[6] = (         short *)0;
#line 327 "esemco0r.pc"
                        sqlstm.sqinds[6] = (         int  )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqharm[6] = (unsigned long )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqadto[6] = (unsigned short )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqtdso[6] = (unsigned short )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqhstv[7] = (unsigned char  *)&o_tone_freq;
#line 327 "esemco0r.pc"
                        sqlstm.sqhstl[7] = (unsigned long )sizeof(double);
#line 327 "esemco0r.pc"
                        sqlstm.sqhsts[7] = (         int  )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqindv[7] = (         short *)0;
#line 327 "esemco0r.pc"
                        sqlstm.sqinds[7] = (         int  )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqharm[7] = (unsigned long )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqadto[7] = (unsigned short )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqtdso[7] = (unsigned short )0;
#line 327 "esemco0r.pc"
                        sqlstm.sqphsv = sqlstm.sqhstv;
#line 327 "esemco0r.pc"
                        sqlstm.sqphsl = sqlstm.sqhstl;
#line 327 "esemco0r.pc"
                        sqlstm.sqphss = sqlstm.sqhsts;
#line 327 "esemco0r.pc"
                        sqlstm.sqpind = sqlstm.sqindv;
#line 327 "esemco0r.pc"
                        sqlstm.sqpins = sqlstm.sqinds;
#line 327 "esemco0r.pc"
                        sqlstm.sqparm = sqlstm.sqharm;
#line 327 "esemco0r.pc"
                        sqlstm.sqparc = sqlstm.sqharc;
#line 327 "esemco0r.pc"
                        sqlstm.sqpadto = sqlstm.sqadto;
#line 327 "esemco0r.pc"
                        sqlstm.sqptdso = sqlstm.sqtdso;
#line 327 "esemco0r.pc"
                        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 327 "esemco0r.pc"
                        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 327 "esemco0r.pc"
}

#line 331 "esemco0r.pc"

                        break;
                }
                if (sqlca.sqlcode == NOT_FOUND)
                    break;

                continue;
            }

/*
system("echo \"esemco0r 1\" >> /tmp/debug");
sprintf(msg, "echo \"%c %s %s %s %d\" >> /tmp/debug",
o_sys_category, o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr, o_base_no );
system(msg);
*/

printf( "esemco0r 058 EXEC SQL \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   058 EXEC SQL \" >> /tmp/debug", sys_date ,sys_time);	
system(msg);	

sprintf(msg, "echo \"esemco0r%s%s   058a o_sys_category=%c o_sys_type.arr=%s o_sys_no.arr=%s  o_sys_suffix.arr=%s o_base_no=%d \" >> /tmp/debug", sys_date ,sys_time,o_sys_category, o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr, o_base_no);	
system(msg);	

// :o_sys_category, :o_sys_type, :o_sys_no, :o_sys_suffix, :o_base_no, :o_tx_freq,:o_rx_freq, :o_tone_freq; */ 

 
 
        /* EXEC SQL
             SELECT SUBDISTRICT, EAST, NORTH, STATION_TYPE,
                    NVL(TO_CHAR(CANCEL_DATE), '-')
             INTO   :o_sub_district, :o_grid_east, :o_grid_north,
                    :o_station_type, :o_cancel_date
             FROM   STATION
             WHERE  SYS_CATEGORY = :o_sys_category
             AND    SYS_TYPE = :o_sys_type
             AND    SYS_NO = :o_sys_no
             AND    SYS_SUFFIX = :o_sys_suffix
             AND    BASE_NO = :o_base_no

             AND    STATION_FLAG IN ('P', 'A'); */ 
#line 363 "esemco0r.pc"

{
#line 351 "esemco0r.pc"
        struct sqlexd sqlstm;
#line 351 "esemco0r.pc"
        sqlstm.sqlvsn = 13;
#line 351 "esemco0r.pc"
        sqlstm.arrsiz = 10;
#line 351 "esemco0r.pc"
        sqlstm.sqladtp = &sqladt;
#line 351 "esemco0r.pc"
        sqlstm.sqltdsp = &sqltds;
#line 351 "esemco0r.pc"
        sqlstm.stmt = "select SUBDISTRICT ,EAST ,NORTH ,STATION_TYPE ,NVL(TO\
_CHAR(CANCEL_DATE),'-') into :b0,:b1,:b2,:b3,:b4  from STATION where (((((SYS_\
CATEGORY=:b5 and SYS_TYPE=:b6) and SYS_NO=:b7) and SYS_SUFFIX=:b8) and BASE_NO\
=:b9) and STATION_FLAG in ('P','A'))";
#line 351 "esemco0r.pc"
        sqlstm.iters = (unsigned int  )1;
#line 351 "esemco0r.pc"
        sqlstm.offset = (unsigned int  )364;
#line 351 "esemco0r.pc"
        sqlstm.selerr = (unsigned short)1;
#line 351 "esemco0r.pc"
        sqlstm.sqlpfmem = (unsigned int  )0;
#line 351 "esemco0r.pc"
        sqlstm.cud = sqlcud0;
#line 351 "esemco0r.pc"
        sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 351 "esemco0r.pc"
        sqlstm.sqlety = (unsigned short)4352;
#line 351 "esemco0r.pc"
        sqlstm.occurs = (unsigned int  )0;
#line 351 "esemco0r.pc"
        sqlstm.sqhstv[0] = (unsigned char  *)&o_sub_district;
#line 351 "esemco0r.pc"
        sqlstm.sqhstl[0] = (unsigned long )6;
#line 351 "esemco0r.pc"
        sqlstm.sqhsts[0] = (         int  )0;
#line 351 "esemco0r.pc"
        sqlstm.sqindv[0] = (         short *)0;
#line 351 "esemco0r.pc"
        sqlstm.sqinds[0] = (         int  )0;
#line 351 "esemco0r.pc"
        sqlstm.sqharm[0] = (unsigned long )0;
#line 351 "esemco0r.pc"
        sqlstm.sqadto[0] = (unsigned short )0;
#line 351 "esemco0r.pc"
        sqlstm.sqtdso[0] = (unsigned short )0;
#line 351 "esemco0r.pc"
        sqlstm.sqhstv[1] = (unsigned char  *)&o_grid_east;
#line 351 "esemco0r.pc"
        sqlstm.sqhstl[1] = (unsigned long )sizeof(int);
#line 351 "esemco0r.pc"
        sqlstm.sqhsts[1] = (         int  )0;
#line 351 "esemco0r.pc"
        sqlstm.sqindv[1] = (         short *)0;
#line 351 "esemco0r.pc"
        sqlstm.sqinds[1] = (         int  )0;
#line 351 "esemco0r.pc"
        sqlstm.sqharm[1] = (unsigned long )0;
#line 351 "esemco0r.pc"
        sqlstm.sqadto[1] = (unsigned short )0;
#line 351 "esemco0r.pc"
        sqlstm.sqtdso[1] = (unsigned short )0;
#line 351 "esemco0r.pc"
        sqlstm.sqhstv[2] = (unsigned char  *)&o_grid_north;
#line 351 "esemco0r.pc"
        sqlstm.sqhstl[2] = (unsigned long )sizeof(int);
#line 351 "esemco0r.pc"
        sqlstm.sqhsts[2] = (         int  )0;
#line 351 "esemco0r.pc"
        sqlstm.sqindv[2] = (         short *)0;
#line 351 "esemco0r.pc"
        sqlstm.sqinds[2] = (         int  )0;
#line 351 "esemco0r.pc"
        sqlstm.sqharm[2] = (unsigned long )0;
#line 351 "esemco0r.pc"
        sqlstm.sqadto[2] = (unsigned short )0;
#line 351 "esemco0r.pc"
        sqlstm.sqtdso[2] = (unsigned short )0;
#line 351 "esemco0r.pc"
        sqlstm.sqhstv[3] = (unsigned char  *)&o_station_type;
#line 351 "esemco0r.pc"
        sqlstm.sqhstl[3] = (unsigned long )1;
#line 351 "esemco0r.pc"
        sqlstm.sqhsts[3] = (         int  )0;
#line 351 "esemco0r.pc"
        sqlstm.sqindv[3] = (         short *)0;
#line 351 "esemco0r.pc"
        sqlstm.sqinds[3] = (         int  )0;
#line 351 "esemco0r.pc"
        sqlstm.sqharm[3] = (unsigned long )0;
#line 351 "esemco0r.pc"
        sqlstm.sqadto[3] = (unsigned short )0;
#line 351 "esemco0r.pc"
        sqlstm.sqtdso[3] = (unsigned short )0;
#line 351 "esemco0r.pc"
        sqlstm.sqhstv[4] = (unsigned char  *)&o_cancel_date;
#line 351 "esemco0r.pc"
        sqlstm.sqhstl[4] = (unsigned long )12;
#line 351 "esemco0r.pc"
        sqlstm.sqhsts[4] = (         int  )0;
#line 351 "esemco0r.pc"
        sqlstm.sqindv[4] = (         short *)0;
#line 351 "esemco0r.pc"
        sqlstm.sqinds[4] = (         int  )0;
#line 351 "esemco0r.pc"
        sqlstm.sqharm[4] = (unsigned long )0;
#line 351 "esemco0r.pc"
        sqlstm.sqadto[4] = (unsigned short )0;
#line 351 "esemco0r.pc"
        sqlstm.sqtdso[4] = (unsigned short )0;
#line 351 "esemco0r.pc"
        sqlstm.sqhstv[5] = (unsigned char  *)&o_sys_category;
#line 351 "esemco0r.pc"
        sqlstm.sqhstl[5] = (unsigned long )1;
#line 351 "esemco0r.pc"
        sqlstm.sqhsts[5] = (         int  )0;
#line 351 "esemco0r.pc"
        sqlstm.sqindv[5] = (         short *)0;
#line 351 "esemco0r.pc"
        sqlstm.sqinds[5] = (         int  )0;
#line 351 "esemco0r.pc"
        sqlstm.sqharm[5] = (unsigned long )0;
#line 351 "esemco0r.pc"
        sqlstm.sqadto[5] = (unsigned short )0;
#line 351 "esemco0r.pc"
        sqlstm.sqtdso[5] = (unsigned short )0;
#line 351 "esemco0r.pc"
        sqlstm.sqhstv[6] = (unsigned char  *)&o_sys_type;
#line 351 "esemco0r.pc"
        sqlstm.sqhstl[6] = (unsigned long )5;
#line 351 "esemco0r.pc"
        sqlstm.sqhsts[6] = (         int  )0;
#line 351 "esemco0r.pc"
        sqlstm.sqindv[6] = (         short *)0;
#line 351 "esemco0r.pc"
        sqlstm.sqinds[6] = (         int  )0;
#line 351 "esemco0r.pc"
        sqlstm.sqharm[6] = (unsigned long )0;
#line 351 "esemco0r.pc"
        sqlstm.sqadto[6] = (unsigned short )0;
#line 351 "esemco0r.pc"
        sqlstm.sqtdso[6] = (unsigned short )0;
#line 351 "esemco0r.pc"
        sqlstm.sqhstv[7] = (unsigned char  *)&o_sys_no;
#line 351 "esemco0r.pc"
        sqlstm.sqhstl[7] = (unsigned long )10;
#line 351 "esemco0r.pc"
        sqlstm.sqhsts[7] = (         int  )0;
#line 351 "esemco0r.pc"
        sqlstm.sqindv[7] = (         short *)0;
#line 351 "esemco0r.pc"
        sqlstm.sqinds[7] = (         int  )0;
#line 351 "esemco0r.pc"
        sqlstm.sqharm[7] = (unsigned long )0;
#line 351 "esemco0r.pc"
        sqlstm.sqadto[7] = (unsigned short )0;
#line 351 "esemco0r.pc"
        sqlstm.sqtdso[7] = (unsigned short )0;
#line 351 "esemco0r.pc"
        sqlstm.sqhstv[8] = (unsigned char  *)&o_sys_suffix;
#line 351 "esemco0r.pc"
        sqlstm.sqhstl[8] = (unsigned long )6;
#line 351 "esemco0r.pc"
        sqlstm.sqhsts[8] = (         int  )0;
#line 351 "esemco0r.pc"
        sqlstm.sqindv[8] = (         short *)0;
#line 351 "esemco0r.pc"
        sqlstm.sqinds[8] = (         int  )0;
#line 351 "esemco0r.pc"
        sqlstm.sqharm[8] = (unsigned long )0;
#line 351 "esemco0r.pc"
        sqlstm.sqadto[8] = (unsigned short )0;
#line 351 "esemco0r.pc"
        sqlstm.sqtdso[8] = (unsigned short )0;
#line 351 "esemco0r.pc"
        sqlstm.sqhstv[9] = (unsigned char  *)&o_base_no;
#line 351 "esemco0r.pc"
        sqlstm.sqhstl[9] = (unsigned long )sizeof(int);
#line 351 "esemco0r.pc"
        sqlstm.sqhsts[9] = (         int  )0;
#line 351 "esemco0r.pc"
        sqlstm.sqindv[9] = (         short *)0;
#line 351 "esemco0r.pc"
        sqlstm.sqinds[9] = (         int  )0;
#line 351 "esemco0r.pc"
        sqlstm.sqharm[9] = (unsigned long )0;
#line 351 "esemco0r.pc"
        sqlstm.sqadto[9] = (unsigned short )0;
#line 351 "esemco0r.pc"
        sqlstm.sqtdso[9] = (unsigned short )0;
#line 351 "esemco0r.pc"
        sqlstm.sqphsv = sqlstm.sqhstv;
#line 351 "esemco0r.pc"
        sqlstm.sqphsl = sqlstm.sqhstl;
#line 351 "esemco0r.pc"
        sqlstm.sqphss = sqlstm.sqhsts;
#line 351 "esemco0r.pc"
        sqlstm.sqpind = sqlstm.sqindv;
#line 351 "esemco0r.pc"
        sqlstm.sqpins = sqlstm.sqinds;
#line 351 "esemco0r.pc"
        sqlstm.sqparm = sqlstm.sqharm;
#line 351 "esemco0r.pc"
        sqlstm.sqparc = sqlstm.sqharc;
#line 351 "esemco0r.pc"
        sqlstm.sqpadto = sqlstm.sqadto;
#line 351 "esemco0r.pc"
        sqlstm.sqptdso = sqlstm.sqtdso;
#line 351 "esemco0r.pc"
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 351 "esemco0r.pc"
        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 351 "esemco0r.pc"
}

#line 363 "esemco0r.pc"

/* Alex Yeung 1999-08-05 */


	

printf( "esemco0r 058b EXEC SQL \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   058b EXEC SQL %c %s %s %s %d \" >> /tmp/debug", sys_date ,sys_time ,o_sys_category, o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr, o_base_no);
system(msg);


        /*************************************************************/
        /*  if this is a cancelled station (CANCEL_DATE is not NULL) */
        /*  skip it                                                  */
        /*************************************************************/

/*
        if ((o_grid_east == 0) || (o_grid_north == 0))
*/

        if (o_cancel_date.arr[0] != '-')
        {
            switch (tx_mode)
            {
                case TX_RX:
                    /* EXEC SQL
                         FETCH CO01
                         INTO  :o_sys_category, :o_sys_type, :o_sys_no, 
                               :o_sys_suffix, :o_base_no, :o_tx_freq,
                               :o_rx_freq, :o_tone_freq; */ 
#line 391 "esemco0r.pc"

{
#line 387 "esemco0r.pc"
                    struct sqlexd sqlstm;
#line 387 "esemco0r.pc"
                    sqlstm.sqlvsn = 13;
#line 387 "esemco0r.pc"
                    sqlstm.arrsiz = 10;
#line 387 "esemco0r.pc"
                    sqlstm.sqladtp = &sqladt;
#line 387 "esemco0r.pc"
                    sqlstm.sqltdsp = &sqltds;
#line 387 "esemco0r.pc"
                    sqlstm.iters = (unsigned int  )1;
#line 387 "esemco0r.pc"
                    sqlstm.offset = (unsigned int  )419;
#line 387 "esemco0r.pc"
                    sqlstm.selerr = (unsigned short)1;
#line 387 "esemco0r.pc"
                    sqlstm.sqlpfmem = (unsigned int  )0;
#line 387 "esemco0r.pc"
                    sqlstm.cud = sqlcud0;
#line 387 "esemco0r.pc"
                    sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 387 "esemco0r.pc"
                    sqlstm.sqlety = (unsigned short)4352;
#line 387 "esemco0r.pc"
                    sqlstm.occurs = (unsigned int  )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqfoff = (         int )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqfmod = (unsigned int )2;
#line 387 "esemco0r.pc"
                    sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
#line 387 "esemco0r.pc"
                    sqlstm.sqhstl[0] = (unsigned long )1;
#line 387 "esemco0r.pc"
                    sqlstm.sqhsts[0] = (         int  )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqindv[0] = (         short *)0;
#line 387 "esemco0r.pc"
                    sqlstm.sqinds[0] = (         int  )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqharm[0] = (unsigned long )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqadto[0] = (unsigned short )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqtdso[0] = (unsigned short )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
#line 387 "esemco0r.pc"
                    sqlstm.sqhstl[1] = (unsigned long )5;
#line 387 "esemco0r.pc"
                    sqlstm.sqhsts[1] = (         int  )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqindv[1] = (         short *)0;
#line 387 "esemco0r.pc"
                    sqlstm.sqinds[1] = (         int  )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqharm[1] = (unsigned long )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqadto[1] = (unsigned short )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqtdso[1] = (unsigned short )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
#line 387 "esemco0r.pc"
                    sqlstm.sqhstl[2] = (unsigned long )10;
#line 387 "esemco0r.pc"
                    sqlstm.sqhsts[2] = (         int  )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqindv[2] = (         short *)0;
#line 387 "esemco0r.pc"
                    sqlstm.sqinds[2] = (         int  )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqharm[2] = (unsigned long )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqadto[2] = (unsigned short )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqtdso[2] = (unsigned short )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
#line 387 "esemco0r.pc"
                    sqlstm.sqhstl[3] = (unsigned long )6;
#line 387 "esemco0r.pc"
                    sqlstm.sqhsts[3] = (         int  )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqindv[3] = (         short *)0;
#line 387 "esemco0r.pc"
                    sqlstm.sqinds[3] = (         int  )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqharm[3] = (unsigned long )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqadto[3] = (unsigned short )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqtdso[3] = (unsigned short )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqhstv[4] = (unsigned char  *)&o_base_no;
#line 387 "esemco0r.pc"
                    sqlstm.sqhstl[4] = (unsigned long )sizeof(int);
#line 387 "esemco0r.pc"
                    sqlstm.sqhsts[4] = (         int  )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqindv[4] = (         short *)0;
#line 387 "esemco0r.pc"
                    sqlstm.sqinds[4] = (         int  )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqharm[4] = (unsigned long )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqadto[4] = (unsigned short )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqtdso[4] = (unsigned short )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqhstv[5] = (unsigned char  *)&o_tx_freq;
#line 387 "esemco0r.pc"
                    sqlstm.sqhstl[5] = (unsigned long )sizeof(double);
#line 387 "esemco0r.pc"
                    sqlstm.sqhsts[5] = (         int  )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqindv[5] = (         short *)0;
#line 387 "esemco0r.pc"
                    sqlstm.sqinds[5] = (         int  )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqharm[5] = (unsigned long )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqadto[5] = (unsigned short )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqtdso[5] = (unsigned short )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqhstv[6] = (unsigned char  *)&o_rx_freq;
#line 387 "esemco0r.pc"
                    sqlstm.sqhstl[6] = (unsigned long )sizeof(double);
#line 387 "esemco0r.pc"
                    sqlstm.sqhsts[6] = (         int  )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqindv[6] = (         short *)0;
#line 387 "esemco0r.pc"
                    sqlstm.sqinds[6] = (         int  )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqharm[6] = (unsigned long )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqadto[6] = (unsigned short )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqtdso[6] = (unsigned short )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqhstv[7] = (unsigned char  *)&o_tone_freq;
#line 387 "esemco0r.pc"
                    sqlstm.sqhstl[7] = (unsigned long )sizeof(double);
#line 387 "esemco0r.pc"
                    sqlstm.sqhsts[7] = (         int  )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqindv[7] = (         short *)0;
#line 387 "esemco0r.pc"
                    sqlstm.sqinds[7] = (         int  )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqharm[7] = (unsigned long )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqadto[7] = (unsigned short )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqtdso[7] = (unsigned short )0;
#line 387 "esemco0r.pc"
                    sqlstm.sqphsv = sqlstm.sqhstv;
#line 387 "esemco0r.pc"
                    sqlstm.sqphsl = sqlstm.sqhstl;
#line 387 "esemco0r.pc"
                    sqlstm.sqphss = sqlstm.sqhsts;
#line 387 "esemco0r.pc"
                    sqlstm.sqpind = sqlstm.sqindv;
#line 387 "esemco0r.pc"
                    sqlstm.sqpins = sqlstm.sqinds;
#line 387 "esemco0r.pc"
                    sqlstm.sqparm = sqlstm.sqharm;
#line 387 "esemco0r.pc"
                    sqlstm.sqparc = sqlstm.sqharc;
#line 387 "esemco0r.pc"
                    sqlstm.sqpadto = sqlstm.sqadto;
#line 387 "esemco0r.pc"
                    sqlstm.sqptdso = sqlstm.sqtdso;
#line 387 "esemco0r.pc"
                    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 387 "esemco0r.pc"
                    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 387 "esemco0r.pc"
}

#line 391 "esemco0r.pc"

                    break;

                case RX_ONLY:
                    /* EXEC SQL
                         FETCH CO02
                         INTO  :o_sys_category, :o_sys_type, :o_sys_no, 
                               :o_sys_suffix, :o_base_no, :o_tx_freq,
                               :o_rx_freq, :o_tone_freq; */ 
#line 399 "esemco0r.pc"

{
#line 395 "esemco0r.pc"
                    struct sqlexd sqlstm;
#line 395 "esemco0r.pc"
                    sqlstm.sqlvsn = 13;
#line 395 "esemco0r.pc"
                    sqlstm.arrsiz = 10;
#line 395 "esemco0r.pc"
                    sqlstm.sqladtp = &sqladt;
#line 395 "esemco0r.pc"
                    sqlstm.sqltdsp = &sqltds;
#line 395 "esemco0r.pc"
                    sqlstm.iters = (unsigned int  )1;
#line 395 "esemco0r.pc"
                    sqlstm.offset = (unsigned int  )466;
#line 395 "esemco0r.pc"
                    sqlstm.selerr = (unsigned short)1;
#line 395 "esemco0r.pc"
                    sqlstm.sqlpfmem = (unsigned int  )0;
#line 395 "esemco0r.pc"
                    sqlstm.cud = sqlcud0;
#line 395 "esemco0r.pc"
                    sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 395 "esemco0r.pc"
                    sqlstm.sqlety = (unsigned short)4352;
#line 395 "esemco0r.pc"
                    sqlstm.occurs = (unsigned int  )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqfoff = (         int )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqfmod = (unsigned int )2;
#line 395 "esemco0r.pc"
                    sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
#line 395 "esemco0r.pc"
                    sqlstm.sqhstl[0] = (unsigned long )1;
#line 395 "esemco0r.pc"
                    sqlstm.sqhsts[0] = (         int  )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqindv[0] = (         short *)0;
#line 395 "esemco0r.pc"
                    sqlstm.sqinds[0] = (         int  )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqharm[0] = (unsigned long )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqadto[0] = (unsigned short )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqtdso[0] = (unsigned short )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
#line 395 "esemco0r.pc"
                    sqlstm.sqhstl[1] = (unsigned long )5;
#line 395 "esemco0r.pc"
                    sqlstm.sqhsts[1] = (         int  )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqindv[1] = (         short *)0;
#line 395 "esemco0r.pc"
                    sqlstm.sqinds[1] = (         int  )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqharm[1] = (unsigned long )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqadto[1] = (unsigned short )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqtdso[1] = (unsigned short )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
#line 395 "esemco0r.pc"
                    sqlstm.sqhstl[2] = (unsigned long )10;
#line 395 "esemco0r.pc"
                    sqlstm.sqhsts[2] = (         int  )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqindv[2] = (         short *)0;
#line 395 "esemco0r.pc"
                    sqlstm.sqinds[2] = (         int  )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqharm[2] = (unsigned long )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqadto[2] = (unsigned short )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqtdso[2] = (unsigned short )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
#line 395 "esemco0r.pc"
                    sqlstm.sqhstl[3] = (unsigned long )6;
#line 395 "esemco0r.pc"
                    sqlstm.sqhsts[3] = (         int  )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqindv[3] = (         short *)0;
#line 395 "esemco0r.pc"
                    sqlstm.sqinds[3] = (         int  )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqharm[3] = (unsigned long )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqadto[3] = (unsigned short )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqtdso[3] = (unsigned short )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqhstv[4] = (unsigned char  *)&o_base_no;
#line 395 "esemco0r.pc"
                    sqlstm.sqhstl[4] = (unsigned long )sizeof(int);
#line 395 "esemco0r.pc"
                    sqlstm.sqhsts[4] = (         int  )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqindv[4] = (         short *)0;
#line 395 "esemco0r.pc"
                    sqlstm.sqinds[4] = (         int  )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqharm[4] = (unsigned long )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqadto[4] = (unsigned short )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqtdso[4] = (unsigned short )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqhstv[5] = (unsigned char  *)&o_tx_freq;
#line 395 "esemco0r.pc"
                    sqlstm.sqhstl[5] = (unsigned long )sizeof(double);
#line 395 "esemco0r.pc"
                    sqlstm.sqhsts[5] = (         int  )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqindv[5] = (         short *)0;
#line 395 "esemco0r.pc"
                    sqlstm.sqinds[5] = (         int  )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqharm[5] = (unsigned long )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqadto[5] = (unsigned short )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqtdso[5] = (unsigned short )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqhstv[6] = (unsigned char  *)&o_rx_freq;
#line 395 "esemco0r.pc"
                    sqlstm.sqhstl[6] = (unsigned long )sizeof(double);
#line 395 "esemco0r.pc"
                    sqlstm.sqhsts[6] = (         int  )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqindv[6] = (         short *)0;
#line 395 "esemco0r.pc"
                    sqlstm.sqinds[6] = (         int  )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqharm[6] = (unsigned long )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqadto[6] = (unsigned short )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqtdso[6] = (unsigned short )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqhstv[7] = (unsigned char  *)&o_tone_freq;
#line 395 "esemco0r.pc"
                    sqlstm.sqhstl[7] = (unsigned long )sizeof(double);
#line 395 "esemco0r.pc"
                    sqlstm.sqhsts[7] = (         int  )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqindv[7] = (         short *)0;
#line 395 "esemco0r.pc"
                    sqlstm.sqinds[7] = (         int  )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqharm[7] = (unsigned long )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqadto[7] = (unsigned short )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqtdso[7] = (unsigned short )0;
#line 395 "esemco0r.pc"
                    sqlstm.sqphsv = sqlstm.sqhstv;
#line 395 "esemco0r.pc"
                    sqlstm.sqphsl = sqlstm.sqhstl;
#line 395 "esemco0r.pc"
                    sqlstm.sqphss = sqlstm.sqhsts;
#line 395 "esemco0r.pc"
                    sqlstm.sqpind = sqlstm.sqindv;
#line 395 "esemco0r.pc"
                    sqlstm.sqpins = sqlstm.sqinds;
#line 395 "esemco0r.pc"
                    sqlstm.sqparm = sqlstm.sqharm;
#line 395 "esemco0r.pc"
                    sqlstm.sqparc = sqlstm.sqharc;
#line 395 "esemco0r.pc"
                    sqlstm.sqpadto = sqlstm.sqadto;
#line 395 "esemco0r.pc"
                    sqlstm.sqptdso = sqlstm.sqtdso;
#line 395 "esemco0r.pc"
                    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 395 "esemco0r.pc"
                    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 395 "esemco0r.pc"
}

#line 399 "esemco0r.pc"

                    break;

                case TX_ONLY:
                    /* EXEC SQL
                         FETCH CO03
                         INTO  :o_sys_category, :o_sys_type, :o_sys_no, 
                               :o_sys_suffix, :o_base_no, :o_tx_freq,
                               :o_rx_freq, :o_tone_freq; */ 
#line 407 "esemco0r.pc"

{
#line 403 "esemco0r.pc"
                    struct sqlexd sqlstm;
#line 403 "esemco0r.pc"
                    sqlstm.sqlvsn = 13;
#line 403 "esemco0r.pc"
                    sqlstm.arrsiz = 10;
#line 403 "esemco0r.pc"
                    sqlstm.sqladtp = &sqladt;
#line 403 "esemco0r.pc"
                    sqlstm.sqltdsp = &sqltds;
#line 403 "esemco0r.pc"
                    sqlstm.iters = (unsigned int  )1;
#line 403 "esemco0r.pc"
                    sqlstm.offset = (unsigned int  )513;
#line 403 "esemco0r.pc"
                    sqlstm.selerr = (unsigned short)1;
#line 403 "esemco0r.pc"
                    sqlstm.sqlpfmem = (unsigned int  )0;
#line 403 "esemco0r.pc"
                    sqlstm.cud = sqlcud0;
#line 403 "esemco0r.pc"
                    sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 403 "esemco0r.pc"
                    sqlstm.sqlety = (unsigned short)4352;
#line 403 "esemco0r.pc"
                    sqlstm.occurs = (unsigned int  )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqfoff = (         int )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqfmod = (unsigned int )2;
#line 403 "esemco0r.pc"
                    sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
#line 403 "esemco0r.pc"
                    sqlstm.sqhstl[0] = (unsigned long )1;
#line 403 "esemco0r.pc"
                    sqlstm.sqhsts[0] = (         int  )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqindv[0] = (         short *)0;
#line 403 "esemco0r.pc"
                    sqlstm.sqinds[0] = (         int  )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqharm[0] = (unsigned long )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqadto[0] = (unsigned short )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqtdso[0] = (unsigned short )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
#line 403 "esemco0r.pc"
                    sqlstm.sqhstl[1] = (unsigned long )5;
#line 403 "esemco0r.pc"
                    sqlstm.sqhsts[1] = (         int  )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqindv[1] = (         short *)0;
#line 403 "esemco0r.pc"
                    sqlstm.sqinds[1] = (         int  )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqharm[1] = (unsigned long )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqadto[1] = (unsigned short )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqtdso[1] = (unsigned short )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
#line 403 "esemco0r.pc"
                    sqlstm.sqhstl[2] = (unsigned long )10;
#line 403 "esemco0r.pc"
                    sqlstm.sqhsts[2] = (         int  )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqindv[2] = (         short *)0;
#line 403 "esemco0r.pc"
                    sqlstm.sqinds[2] = (         int  )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqharm[2] = (unsigned long )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqadto[2] = (unsigned short )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqtdso[2] = (unsigned short )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
#line 403 "esemco0r.pc"
                    sqlstm.sqhstl[3] = (unsigned long )6;
#line 403 "esemco0r.pc"
                    sqlstm.sqhsts[3] = (         int  )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqindv[3] = (         short *)0;
#line 403 "esemco0r.pc"
                    sqlstm.sqinds[3] = (         int  )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqharm[3] = (unsigned long )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqadto[3] = (unsigned short )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqtdso[3] = (unsigned short )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqhstv[4] = (unsigned char  *)&o_base_no;
#line 403 "esemco0r.pc"
                    sqlstm.sqhstl[4] = (unsigned long )sizeof(int);
#line 403 "esemco0r.pc"
                    sqlstm.sqhsts[4] = (         int  )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqindv[4] = (         short *)0;
#line 403 "esemco0r.pc"
                    sqlstm.sqinds[4] = (         int  )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqharm[4] = (unsigned long )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqadto[4] = (unsigned short )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqtdso[4] = (unsigned short )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqhstv[5] = (unsigned char  *)&o_tx_freq;
#line 403 "esemco0r.pc"
                    sqlstm.sqhstl[5] = (unsigned long )sizeof(double);
#line 403 "esemco0r.pc"
                    sqlstm.sqhsts[5] = (         int  )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqindv[5] = (         short *)0;
#line 403 "esemco0r.pc"
                    sqlstm.sqinds[5] = (         int  )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqharm[5] = (unsigned long )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqadto[5] = (unsigned short )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqtdso[5] = (unsigned short )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqhstv[6] = (unsigned char  *)&o_rx_freq;
#line 403 "esemco0r.pc"
                    sqlstm.sqhstl[6] = (unsigned long )sizeof(double);
#line 403 "esemco0r.pc"
                    sqlstm.sqhsts[6] = (         int  )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqindv[6] = (         short *)0;
#line 403 "esemco0r.pc"
                    sqlstm.sqinds[6] = (         int  )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqharm[6] = (unsigned long )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqadto[6] = (unsigned short )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqtdso[6] = (unsigned short )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqhstv[7] = (unsigned char  *)&o_tone_freq;
#line 403 "esemco0r.pc"
                    sqlstm.sqhstl[7] = (unsigned long )sizeof(double);
#line 403 "esemco0r.pc"
                    sqlstm.sqhsts[7] = (         int  )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqindv[7] = (         short *)0;
#line 403 "esemco0r.pc"
                    sqlstm.sqinds[7] = (         int  )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqharm[7] = (unsigned long )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqadto[7] = (unsigned short )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqtdso[7] = (unsigned short )0;
#line 403 "esemco0r.pc"
                    sqlstm.sqphsv = sqlstm.sqhstv;
#line 403 "esemco0r.pc"
                    sqlstm.sqphsl = sqlstm.sqhstl;
#line 403 "esemco0r.pc"
                    sqlstm.sqphss = sqlstm.sqhsts;
#line 403 "esemco0r.pc"
                    sqlstm.sqpind = sqlstm.sqindv;
#line 403 "esemco0r.pc"
                    sqlstm.sqpins = sqlstm.sqinds;
#line 403 "esemco0r.pc"
                    sqlstm.sqparm = sqlstm.sqharm;
#line 403 "esemco0r.pc"
                    sqlstm.sqparc = sqlstm.sqharc;
#line 403 "esemco0r.pc"
                    sqlstm.sqpadto = sqlstm.sqadto;
#line 403 "esemco0r.pc"
                    sqlstm.sqptdso = sqlstm.sqtdso;
#line 403 "esemco0r.pc"
                    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 403 "esemco0r.pc"
                    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 403 "esemco0r.pc"
}

#line 407 "esemco0r.pc"

                    break;
            }
            if (sqlca.sqlcode == NOT_FOUND)
				
				{
                break;

			
printf( "esemco0r 058b  == NOT_FOUND \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   058b  == NOT_FOUND %c %s %s %s %d \" >> /tmp/debug", sys_date ,sys_time ,o_sys_category, o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr, o_base_no);
system(msg);
			
			
            continue;
				}
        }

        o_sub_district.arr[o_sub_district.len] = '\0';
/*
printf("sub east north: %s %d %d\n", o_sub_district.arr, o_grid_east, o_grid_north);
fflush(stdout);
*/

printf( "esemco0r 059 EXEC SQL \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   059 EXEC SQL  %c%s%s-%s base no. : %d\n \" >> /tmp/debug", sys_date ,sys_time,o_sys_category, o_sys_type.arr, o_sys_no.arr,o_sys_suffix.arr, o_base_no);	
system(msg);


        if (sqlca.sqlcode == NOT_FOUND)
        {
			
printf( "esemco0r 059--NOT_FOUND-- %c%s%s-%s base no. : %d \n\r" ,o_sys_category, o_sys_type.arr, o_sys_no.arr,o_sys_suffix.arr, o_base_no);	 /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   059--NOT_FOUND-- %c%s%s-%s base no. : %d  \n \" >> /tmp/debug", sys_date ,sys_time,o_sys_category, o_sys_type.arr, o_sys_no.arr,o_sys_suffix.arr, o_base_no);	
system(msg);
			
            fprintf(afp, "STATION record not found for ");
            fprintf(afp, "%c%s%s-%s base no. : %d\n",
                    o_sys_category, o_sys_type.arr, o_sys_no.arr,
                    o_sys_suffix.arr, o_base_no);
            //continue;
        }
		
		
		
printf( "esemco0r 059a EXEC SQL \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   059a EXEC SQL %c%s%s-%s base no. : %d\n  \" >> /tmp/debug", sys_date ,sys_time ,o_sys_category, o_sys_type.arr, o_sys_no.arr,o_sys_suffix.arr, o_base_no);	
system(msg);

		
/*
printf("curr: %s\n", curr_sys);
fflush(stdout);
*/
        if (strcmp(curr_sys, prev_sys))
        {
/*
printf("prev: %s\n", prev_sys);
fflush(stdout);
*/
            strcpy(prev_sys, curr_sys);
            o_tx_freq_lo = prop_tx_freq - FREQ_EPSILON;
            o_tx_freq_hi = prop_tx_freq + FREQ_EPSILON;
            o_rx_freq_lo = prop_rx_freq - FREQ_EPSILON;
            o_rx_freq_hi = prop_rx_freq + FREQ_EPSILON;

            switch (tx_mode)
            {
                case TX_RX:

/* Alex Yeung 1999-08-05
                    EXEC SQL
                         SELECT COUNT(UNIQUE MOBILE_NO)
*/
/* comment by Chen Yung
                    EXEC SQL
                         SELECT COUNT(DISTINCT MOBILE_NO)

                         INTO   :o_mobile_cnt
                         FROM   MOBILE_CH
                         WHERE  TX_FREQ between :o_rx_freq_lo and :o_rx_freq_hi
                         AND    RX_FREQ between :o_tx_freq_lo and :o_tx_freq_hi
                         AND    SYS_CATEGORY = :o_sys_category
                         AND    SYS_TYPE = :o_sys_type
                         AND    SYS_NO = :o_sys_no
                         AND    SYS_SUFFIX = :o_sys_suffix
                         AND    CANCEL_DATE IS NULL;
*/

printf( "esemco0r 060 EXEC SQL \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   060 EXEC SQL \" >> /tmp/debug", sys_date ,sys_time);	
system(msg);

                    /* EXEC SQL
                        SELECT COUNT(DISTINCT b.MOBILE_NO)
                        INTO   :o_mobile_cnt
                        FROM   MOBILE a, MOBILE_CH b
                        WHERE  b.TX_FREQ between :o_rx_freq_lo and :o_rx_freq_hi
                        AND    b.RX_FREQ between :o_tx_freq_lo and :o_tx_freq_hi
                        AND    b.SYS_CATEGORY = :o_sys_category
                        AND    b.SYS_TYPE = :o_sys_type
                        AND    b.SYS_NO = :o_sys_no
                        AND    b.SYS_SUFFIX = :o_sys_suffix
                        AND    b.CANCEL_DATE IS NULL
                        AND    a.SYS_CATEGORY = b.SYS_CATEGORY
                        AND    a.SYS_TYPE = b.SYS_TYPE
                        AND    a.SYS_NO = b.SYS_NO
                        AND    a.SYS_SUFFIX = b.SYS_SUFFIX
                        AND    a.MOBILE_NO = b.MOBILE_NO
                        AND    a.CANCEL_DATE IS NULL; */ 
#line 494 "esemco0r.pc"

{
#line 478 "esemco0r.pc"
                    struct sqlexd sqlstm;
#line 478 "esemco0r.pc"
                    sqlstm.sqlvsn = 13;
#line 478 "esemco0r.pc"
                    sqlstm.arrsiz = 10;
#line 478 "esemco0r.pc"
                    sqlstm.sqladtp = &sqladt;
#line 478 "esemco0r.pc"
                    sqlstm.sqltdsp = &sqltds;
#line 478 "esemco0r.pc"
                    sqlstm.stmt = "select count(distinct b.MOBILE_NO) into :\
b0  from MOBILE a ,MOBILE_CH b where ((((((((((((b.TX_FREQ between :b1 and :b2\
 and b.RX_FREQ between :b3 and :b4) and b.SYS_CATEGORY=:b5) and b.SYS_TYPE=:b6\
) and b.SYS_NO=:b7) and b.SYS_SUFFIX=:b8) and b.CANCEL_DATE is null ) and a.SY\
S_CATEGORY=b.SYS_CATEGORY) and a.SYS_TYPE=b.SYS_TYPE) and a.SYS_NO=b.SYS_NO) a\
nd a.SYS_SUFFIX=b.SYS_SUFFIX) and a.MOBILE_NO=b.MOBILE_NO) and a.CANCEL_DATE i\
s null )";
#line 478 "esemco0r.pc"
                    sqlstm.iters = (unsigned int  )1;
#line 478 "esemco0r.pc"
                    sqlstm.offset = (unsigned int  )560;
#line 478 "esemco0r.pc"
                    sqlstm.selerr = (unsigned short)1;
#line 478 "esemco0r.pc"
                    sqlstm.sqlpfmem = (unsigned int  )0;
#line 478 "esemco0r.pc"
                    sqlstm.cud = sqlcud0;
#line 478 "esemco0r.pc"
                    sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 478 "esemco0r.pc"
                    sqlstm.sqlety = (unsigned short)4352;
#line 478 "esemco0r.pc"
                    sqlstm.occurs = (unsigned int  )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqhstv[0] = (unsigned char  *)&o_mobile_cnt;
#line 478 "esemco0r.pc"
                    sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
#line 478 "esemco0r.pc"
                    sqlstm.sqhsts[0] = (         int  )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqindv[0] = (         short *)0;
#line 478 "esemco0r.pc"
                    sqlstm.sqinds[0] = (         int  )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqharm[0] = (unsigned long )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqadto[0] = (unsigned short )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqtdso[0] = (unsigned short )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqhstv[1] = (unsigned char  *)&o_rx_freq_lo;
#line 478 "esemco0r.pc"
                    sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
#line 478 "esemco0r.pc"
                    sqlstm.sqhsts[1] = (         int  )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqindv[1] = (         short *)0;
#line 478 "esemco0r.pc"
                    sqlstm.sqinds[1] = (         int  )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqharm[1] = (unsigned long )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqadto[1] = (unsigned short )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqtdso[1] = (unsigned short )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqhstv[2] = (unsigned char  *)&o_rx_freq_hi;
#line 478 "esemco0r.pc"
                    sqlstm.sqhstl[2] = (unsigned long )sizeof(double);
#line 478 "esemco0r.pc"
                    sqlstm.sqhsts[2] = (         int  )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqindv[2] = (         short *)0;
#line 478 "esemco0r.pc"
                    sqlstm.sqinds[2] = (         int  )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqharm[2] = (unsigned long )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqadto[2] = (unsigned short )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqtdso[2] = (unsigned short )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqhstv[3] = (unsigned char  *)&o_tx_freq_lo;
#line 478 "esemco0r.pc"
                    sqlstm.sqhstl[3] = (unsigned long )sizeof(double);
#line 478 "esemco0r.pc"
                    sqlstm.sqhsts[3] = (         int  )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqindv[3] = (         short *)0;
#line 478 "esemco0r.pc"
                    sqlstm.sqinds[3] = (         int  )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqharm[3] = (unsigned long )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqadto[3] = (unsigned short )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqtdso[3] = (unsigned short )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqhstv[4] = (unsigned char  *)&o_tx_freq_hi;
#line 478 "esemco0r.pc"
                    sqlstm.sqhstl[4] = (unsigned long )sizeof(double);
#line 478 "esemco0r.pc"
                    sqlstm.sqhsts[4] = (         int  )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqindv[4] = (         short *)0;
#line 478 "esemco0r.pc"
                    sqlstm.sqinds[4] = (         int  )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqharm[4] = (unsigned long )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqadto[4] = (unsigned short )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqtdso[4] = (unsigned short )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqhstv[5] = (unsigned char  *)&o_sys_category;
#line 478 "esemco0r.pc"
                    sqlstm.sqhstl[5] = (unsigned long )1;
#line 478 "esemco0r.pc"
                    sqlstm.sqhsts[5] = (         int  )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqindv[5] = (         short *)0;
#line 478 "esemco0r.pc"
                    sqlstm.sqinds[5] = (         int  )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqharm[5] = (unsigned long )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqadto[5] = (unsigned short )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqtdso[5] = (unsigned short )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqhstv[6] = (unsigned char  *)&o_sys_type;
#line 478 "esemco0r.pc"
                    sqlstm.sqhstl[6] = (unsigned long )5;
#line 478 "esemco0r.pc"
                    sqlstm.sqhsts[6] = (         int  )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqindv[6] = (         short *)0;
#line 478 "esemco0r.pc"
                    sqlstm.sqinds[6] = (         int  )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqharm[6] = (unsigned long )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqadto[6] = (unsigned short )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqtdso[6] = (unsigned short )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqhstv[7] = (unsigned char  *)&o_sys_no;
#line 478 "esemco0r.pc"
                    sqlstm.sqhstl[7] = (unsigned long )10;
#line 478 "esemco0r.pc"
                    sqlstm.sqhsts[7] = (         int  )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqindv[7] = (         short *)0;
#line 478 "esemco0r.pc"
                    sqlstm.sqinds[7] = (         int  )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqharm[7] = (unsigned long )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqadto[7] = (unsigned short )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqtdso[7] = (unsigned short )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqhstv[8] = (unsigned char  *)&o_sys_suffix;
#line 478 "esemco0r.pc"
                    sqlstm.sqhstl[8] = (unsigned long )6;
#line 478 "esemco0r.pc"
                    sqlstm.sqhsts[8] = (         int  )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqindv[8] = (         short *)0;
#line 478 "esemco0r.pc"
                    sqlstm.sqinds[8] = (         int  )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqharm[8] = (unsigned long )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqadto[8] = (unsigned short )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqtdso[8] = (unsigned short )0;
#line 478 "esemco0r.pc"
                    sqlstm.sqphsv = sqlstm.sqhstv;
#line 478 "esemco0r.pc"
                    sqlstm.sqphsl = sqlstm.sqhstl;
#line 478 "esemco0r.pc"
                    sqlstm.sqphss = sqlstm.sqhsts;
#line 478 "esemco0r.pc"
                    sqlstm.sqpind = sqlstm.sqindv;
#line 478 "esemco0r.pc"
                    sqlstm.sqpins = sqlstm.sqinds;
#line 478 "esemco0r.pc"
                    sqlstm.sqparm = sqlstm.sqharm;
#line 478 "esemco0r.pc"
                    sqlstm.sqparc = sqlstm.sqharc;
#line 478 "esemco0r.pc"
                    sqlstm.sqpadto = sqlstm.sqadto;
#line 478 "esemco0r.pc"
                    sqlstm.sqptdso = sqlstm.sqtdso;
#line 478 "esemco0r.pc"
                    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 478 "esemco0r.pc"
                    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 478 "esemco0r.pc"
}

#line 494 "esemco0r.pc"


printf( "esemco0r 060a EXEC SQL \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   060a EXEC SQL \" >> /tmp/debug", sys_date ,sys_time);	
system(msg);						
						
/* added by Chen Yung for handling merged systems and ZZx systems */

printf( "esemco0r 061 EXEC SQL \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   061 EXEC SQL \" >> /tmp/debug", sys_date ,sys_time);	
system(msg);
                    /* EXEC SQL
                        SELECT  DISTINCT SYS_ID
                        INTO    :o_sys_id
                        FROM    ESMS_MAP
                        WHERE   SYS_CATEGORY = :o_sys_category
                        AND     SYS_TYPE = :o_sys_type
                        AND     SYS_NO = :o_sys_no
                        AND     SYS_SUFFIX = :o_sys_suffix
                        AND     SYS_LINK = 'L'; */ 
#line 513 "esemco0r.pc"

{
#line 505 "esemco0r.pc"
                    struct sqlexd sqlstm;
#line 505 "esemco0r.pc"
                    sqlstm.sqlvsn = 13;
#line 505 "esemco0r.pc"
                    sqlstm.arrsiz = 10;
#line 505 "esemco0r.pc"
                    sqlstm.sqladtp = &sqladt;
#line 505 "esemco0r.pc"
                    sqlstm.sqltdsp = &sqltds;
#line 505 "esemco0r.pc"
                    sqlstm.stmt = "select distinct SYS_ID into :b0  from ESM\
S_MAP where ((((SYS_CATEGORY=:b1 and SYS_TYPE=:b2) and SYS_NO=:b3) and SYS_SUF\
FIX=:b4) and SYS_LINK='L')";
#line 505 "esemco0r.pc"
                    sqlstm.iters = (unsigned int  )1;
#line 505 "esemco0r.pc"
                    sqlstm.offset = (unsigned int  )611;
#line 505 "esemco0r.pc"
                    sqlstm.selerr = (unsigned short)1;
#line 505 "esemco0r.pc"
                    sqlstm.sqlpfmem = (unsigned int  )0;
#line 505 "esemco0r.pc"
                    sqlstm.cud = sqlcud0;
#line 505 "esemco0r.pc"
                    sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 505 "esemco0r.pc"
                    sqlstm.sqlety = (unsigned short)4352;
#line 505 "esemco0r.pc"
                    sqlstm.occurs = (unsigned int  )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_id;
#line 505 "esemco0r.pc"
                    sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
#line 505 "esemco0r.pc"
                    sqlstm.sqhsts[0] = (         int  )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqindv[0] = (         short *)0;
#line 505 "esemco0r.pc"
                    sqlstm.sqinds[0] = (         int  )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqharm[0] = (unsigned long )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqadto[0] = (unsigned short )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqtdso[0] = (unsigned short )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_category;
#line 505 "esemco0r.pc"
                    sqlstm.sqhstl[1] = (unsigned long )1;
#line 505 "esemco0r.pc"
                    sqlstm.sqhsts[1] = (         int  )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqindv[1] = (         short *)0;
#line 505 "esemco0r.pc"
                    sqlstm.sqinds[1] = (         int  )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqharm[1] = (unsigned long )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqadto[1] = (unsigned short )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqtdso[1] = (unsigned short )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_type;
#line 505 "esemco0r.pc"
                    sqlstm.sqhstl[2] = (unsigned long )5;
#line 505 "esemco0r.pc"
                    sqlstm.sqhsts[2] = (         int  )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqindv[2] = (         short *)0;
#line 505 "esemco0r.pc"
                    sqlstm.sqinds[2] = (         int  )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqharm[2] = (unsigned long )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqadto[2] = (unsigned short )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqtdso[2] = (unsigned short )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_no;
#line 505 "esemco0r.pc"
                    sqlstm.sqhstl[3] = (unsigned long )10;
#line 505 "esemco0r.pc"
                    sqlstm.sqhsts[3] = (         int  )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqindv[3] = (         short *)0;
#line 505 "esemco0r.pc"
                    sqlstm.sqinds[3] = (         int  )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqharm[3] = (unsigned long )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqadto[3] = (unsigned short )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqtdso[3] = (unsigned short )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqhstv[4] = (unsigned char  *)&o_sys_suffix;
#line 505 "esemco0r.pc"
                    sqlstm.sqhstl[4] = (unsigned long )6;
#line 505 "esemco0r.pc"
                    sqlstm.sqhsts[4] = (         int  )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqindv[4] = (         short *)0;
#line 505 "esemco0r.pc"
                    sqlstm.sqinds[4] = (         int  )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqharm[4] = (unsigned long )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqadto[4] = (unsigned short )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqtdso[4] = (unsigned short )0;
#line 505 "esemco0r.pc"
                    sqlstm.sqphsv = sqlstm.sqhstv;
#line 505 "esemco0r.pc"
                    sqlstm.sqphsl = sqlstm.sqhstl;
#line 505 "esemco0r.pc"
                    sqlstm.sqphss = sqlstm.sqhsts;
#line 505 "esemco0r.pc"
                    sqlstm.sqpind = sqlstm.sqindv;
#line 505 "esemco0r.pc"
                    sqlstm.sqpins = sqlstm.sqinds;
#line 505 "esemco0r.pc"
                    sqlstm.sqparm = sqlstm.sqharm;
#line 505 "esemco0r.pc"
                    sqlstm.sqparc = sqlstm.sqharc;
#line 505 "esemco0r.pc"
                    sqlstm.sqpadto = sqlstm.sqadto;
#line 505 "esemco0r.pc"
                    sqlstm.sqptdso = sqlstm.sqtdso;
#line 505 "esemco0r.pc"
                    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 505 "esemco0r.pc"
                    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 505 "esemco0r.pc"
}

#line 513 "esemco0r.pc"

                   

	
printf( "esemco0r 061a  EXEC SQL  \n\r"  ); /*Cyrus Debug*/
//sprintf(msg, "echo \"esemco0r%s%s   061a  EXEC SQL  %c %s %s %s %d \" >> /tmp/debug", sys_date ,sys_time ,o_sys_category, o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr, o_base_no);
sprintf(msg, "echo \"esemco0r%s %s  061a EXEC SQL   o_rx_freq_lo=%lf o_rx_freq_hi=%lf  o_tx_freq_lo=%lf o_tx_freq_hi=%lf  %c|%s|%s|%s \" >> /tmp/debug", sys_date ,sys_time, o_rx_freq_lo ,o_rx_freq_hi,o_tx_freq_lo,o_tx_freq_hi , o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr );	

system(msg);
				
                    if (sqlca.sqlcode != NOT_FOUND) 
                    {
						
printf( "esemco0r 061a  == NOT_FOUND \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   061a  == NOT_FOUND %c %s %s %s %d \" >> /tmp/debug", sys_date ,sys_time ,o_sys_category, o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr, o_base_no);
system(msg);
						
                        /* EXEC SQL DECLARE CO04 CURSOR FOR
                            SELECT    DISTINCT SYS_SUFFIX
                            FROM      ESMS_MAP
                            WHERE     SYS_ID = :o_sys_id
                            AND       SYS_SUFFIX <> :o_sys_suffix
                            ORDER BY  SYS_SUFFIX; */ 
#line 531 "esemco0r.pc"


                        /* EXEC SQL OPEN CO04; */ 
#line 533 "esemco0r.pc"

{
#line 533 "esemco0r.pc"
                        struct sqlexd sqlstm;
#line 533 "esemco0r.pc"
                        sqlstm.sqlvsn = 13;
#line 533 "esemco0r.pc"
                        sqlstm.arrsiz = 10;
#line 533 "esemco0r.pc"
                        sqlstm.sqladtp = &sqladt;
#line 533 "esemco0r.pc"
                        sqlstm.sqltdsp = &sqltds;
#line 533 "esemco0r.pc"
                        sqlstm.stmt = sq0007;
#line 533 "esemco0r.pc"
                        sqlstm.iters = (unsigned int  )1;
#line 533 "esemco0r.pc"
                        sqlstm.offset = (unsigned int  )646;
#line 533 "esemco0r.pc"
                        sqlstm.selerr = (unsigned short)1;
#line 533 "esemco0r.pc"
                        sqlstm.sqlpfmem = (unsigned int  )0;
#line 533 "esemco0r.pc"
                        sqlstm.cud = sqlcud0;
#line 533 "esemco0r.pc"
                        sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 533 "esemco0r.pc"
                        sqlstm.sqlety = (unsigned short)4352;
#line 533 "esemco0r.pc"
                        sqlstm.occurs = (unsigned int  )0;
#line 533 "esemco0r.pc"
                        sqlstm.sqcmod = (unsigned int )0;
#line 533 "esemco0r.pc"
                        sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_id;
#line 533 "esemco0r.pc"
                        sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
#line 533 "esemco0r.pc"
                        sqlstm.sqhsts[0] = (         int  )0;
#line 533 "esemco0r.pc"
                        sqlstm.sqindv[0] = (         short *)0;
#line 533 "esemco0r.pc"
                        sqlstm.sqinds[0] = (         int  )0;
#line 533 "esemco0r.pc"
                        sqlstm.sqharm[0] = (unsigned long )0;
#line 533 "esemco0r.pc"
                        sqlstm.sqadto[0] = (unsigned short )0;
#line 533 "esemco0r.pc"
                        sqlstm.sqtdso[0] = (unsigned short )0;
#line 533 "esemco0r.pc"
                        sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_suffix;
#line 533 "esemco0r.pc"
                        sqlstm.sqhstl[1] = (unsigned long )6;
#line 533 "esemco0r.pc"
                        sqlstm.sqhsts[1] = (         int  )0;
#line 533 "esemco0r.pc"
                        sqlstm.sqindv[1] = (         short *)0;
#line 533 "esemco0r.pc"
                        sqlstm.sqinds[1] = (         int  )0;
#line 533 "esemco0r.pc"
                        sqlstm.sqharm[1] = (unsigned long )0;
#line 533 "esemco0r.pc"
                        sqlstm.sqadto[1] = (unsigned short )0;
#line 533 "esemco0r.pc"
                        sqlstm.sqtdso[1] = (unsigned short )0;
#line 533 "esemco0r.pc"
                        sqlstm.sqphsv = sqlstm.sqhstv;
#line 533 "esemco0r.pc"
                        sqlstm.sqphsl = sqlstm.sqhstl;
#line 533 "esemco0r.pc"
                        sqlstm.sqphss = sqlstm.sqhsts;
#line 533 "esemco0r.pc"
                        sqlstm.sqpind = sqlstm.sqindv;
#line 533 "esemco0r.pc"
                        sqlstm.sqpins = sqlstm.sqinds;
#line 533 "esemco0r.pc"
                        sqlstm.sqparm = sqlstm.sqharm;
#line 533 "esemco0r.pc"
                        sqlstm.sqparc = sqlstm.sqharc;
#line 533 "esemco0r.pc"
                        sqlstm.sqpadto = sqlstm.sqadto;
#line 533 "esemco0r.pc"
                        sqlstm.sqptdso = sqlstm.sqtdso;
#line 533 "esemco0r.pc"
                        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 533 "esemco0r.pc"
                        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 533 "esemco0r.pc"
}

#line 533 "esemco0r.pc"




printf( "esemco0r 061c  EXEC SQL  \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   061c  EXEC SQL  %c %s %s %s %d \" >> /tmp/debug", sys_date ,sys_time ,o_sys_category, o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr, o_base_no);
system(msg);
					
						
                        tmp_mobile_cnt = o_mobile_cnt;
                        for ( ; ; )
                        {
                            /* EXEC SQL
                                 FETCH CO04
                                 INTO  :o_sys_suffix_2; */ 
#line 544 "esemco0r.pc"

{
#line 542 "esemco0r.pc"
                            struct sqlexd sqlstm;
#line 542 "esemco0r.pc"
                            sqlstm.sqlvsn = 13;
#line 542 "esemco0r.pc"
                            sqlstm.arrsiz = 10;
#line 542 "esemco0r.pc"
                            sqlstm.sqladtp = &sqladt;
#line 542 "esemco0r.pc"
                            sqlstm.sqltdsp = &sqltds;
#line 542 "esemco0r.pc"
                            sqlstm.iters = (unsigned int  )1;
#line 542 "esemco0r.pc"
                            sqlstm.offset = (unsigned int  )669;
#line 542 "esemco0r.pc"
                            sqlstm.selerr = (unsigned short)1;
#line 542 "esemco0r.pc"
                            sqlstm.sqlpfmem = (unsigned int  )0;
#line 542 "esemco0r.pc"
                            sqlstm.cud = sqlcud0;
#line 542 "esemco0r.pc"
                            sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 542 "esemco0r.pc"
                            sqlstm.sqlety = (unsigned short)4352;
#line 542 "esemco0r.pc"
                            sqlstm.occurs = (unsigned int  )0;
#line 542 "esemco0r.pc"
                            sqlstm.sqfoff = (         int )0;
#line 542 "esemco0r.pc"
                            sqlstm.sqfmod = (unsigned int )2;
#line 542 "esemco0r.pc"
                            sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_suffix_2;
#line 542 "esemco0r.pc"
                            sqlstm.sqhstl[0] = (unsigned long )6;
#line 542 "esemco0r.pc"
                            sqlstm.sqhsts[0] = (         int  )0;
#line 542 "esemco0r.pc"
                            sqlstm.sqindv[0] = (         short *)0;
#line 542 "esemco0r.pc"
                            sqlstm.sqinds[0] = (         int  )0;
#line 542 "esemco0r.pc"
                            sqlstm.sqharm[0] = (unsigned long )0;
#line 542 "esemco0r.pc"
                            sqlstm.sqadto[0] = (unsigned short )0;
#line 542 "esemco0r.pc"
                            sqlstm.sqtdso[0] = (unsigned short )0;
#line 542 "esemco0r.pc"
                            sqlstm.sqphsv = sqlstm.sqhstv;
#line 542 "esemco0r.pc"
                            sqlstm.sqphsl = sqlstm.sqhstl;
#line 542 "esemco0r.pc"
                            sqlstm.sqphss = sqlstm.sqhsts;
#line 542 "esemco0r.pc"
                            sqlstm.sqpind = sqlstm.sqindv;
#line 542 "esemco0r.pc"
                            sqlstm.sqpins = sqlstm.sqinds;
#line 542 "esemco0r.pc"
                            sqlstm.sqparm = sqlstm.sqharm;
#line 542 "esemco0r.pc"
                            sqlstm.sqparc = sqlstm.sqharc;
#line 542 "esemco0r.pc"
                            sqlstm.sqpadto = sqlstm.sqadto;
#line 542 "esemco0r.pc"
                            sqlstm.sqptdso = sqlstm.sqtdso;
#line 542 "esemco0r.pc"
                            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 542 "esemco0r.pc"
                            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 542 "esemco0r.pc"
}

#line 544 "esemco0r.pc"

                            if (sqlca.sqlcode == NOT_FOUND) 
                            {
                                /* EXEC SQL CLOSE CO04; */ 
#line 547 "esemco0r.pc"

{
#line 547 "esemco0r.pc"
                                struct sqlexd sqlstm;
#line 547 "esemco0r.pc"
                                sqlstm.sqlvsn = 13;
#line 547 "esemco0r.pc"
                                sqlstm.arrsiz = 10;
#line 547 "esemco0r.pc"
                                sqlstm.sqladtp = &sqladt;
#line 547 "esemco0r.pc"
                                sqlstm.sqltdsp = &sqltds;
#line 547 "esemco0r.pc"
                                sqlstm.iters = (unsigned int  )1;
#line 547 "esemco0r.pc"
                                sqlstm.offset = (unsigned int  )688;
#line 547 "esemco0r.pc"
                                sqlstm.cud = sqlcud0;
#line 547 "esemco0r.pc"
                                sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 547 "esemco0r.pc"
                                sqlstm.sqlety = (unsigned short)4352;
#line 547 "esemco0r.pc"
                                sqlstm.occurs = (unsigned int  )0;
#line 547 "esemco0r.pc"
                                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 547 "esemco0r.pc"
                                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 547 "esemco0r.pc"
}

#line 547 "esemco0r.pc"

                                break;
                            }
                            else 
                            {

printf( "esemco0r 061d  EXEC SQL  \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   061d  EXEC SQL  %c %s %s %s %d \" >> /tmp/debug", sys_date ,sys_time ,o_sys_category, o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr, o_base_no);
system(msg);
			

							
                                /* EXEC SQL
                                    SELECT COUNT(DISTINCT b.MOBILE_NO)
                                    INTO   :o_mobile_cnt
                                    FROM   MOBILE a, MOBILE_CH b
                                    WHERE  b.TX_FREQ between :o_rx_freq_lo and :o_rx_freq_hi
                                    AND    b.RX_FREQ between :o_tx_freq_lo and :o_tx_freq_hi
                                    AND    b.SYS_CATEGORY = :o_sys_category
                                    AND    b.SYS_TYPE = :o_sys_type
                                    AND    b.SYS_NO = :o_sys_no
                                    AND    b.SYS_SUFFIX = :o_sys_suffix_2
                                    AND    b.CANCEL_DATE IS NULL
                                    AND    a.SYS_CATEGORY = b.SYS_CATEGORY
                                    AND    a.SYS_TYPE = b.SYS_TYPE
                                    AND    a.SYS_NO = b.SYS_NO
                                    AND    a.SYS_SUFFIX = b.SYS_SUFFIX
                                    AND    a.MOBILE_NO = b.MOBILE_NO
                                    AND    a.CANCEL_DATE IS NULL; */ 
#line 572 "esemco0r.pc"

{
#line 556 "esemco0r.pc"
                                struct sqlexd sqlstm;
#line 556 "esemco0r.pc"
                                sqlstm.sqlvsn = 13;
#line 556 "esemco0r.pc"
                                sqlstm.arrsiz = 10;
#line 556 "esemco0r.pc"
                                sqlstm.sqladtp = &sqladt;
#line 556 "esemco0r.pc"
                                sqlstm.sqltdsp = &sqltds;
#line 556 "esemco0r.pc"
                                sqlstm.stmt = "select count(distinct b.MOBIL\
E_NO) into :b0  from MOBILE a ,MOBILE_CH b where ((((((((((((b.TX_FREQ between\
 :b1 and :b2 and b.RX_FREQ between :b3 and :b4) and b.SYS_CATEGORY=:b5) and b.\
SYS_TYPE=:b6) and b.SYS_NO=:b7) and b.SYS_SUFFIX=:b8) and b.CANCEL_DATE is nul\
l ) and a.SYS_CATEGORY=b.SYS_CATEGORY) and a.SYS_TYPE=b.SYS_TYPE) and a.SYS_NO\
=b.SYS_NO) and a.SYS_SUFFIX=b.SYS_SUFFIX) and a.MOBILE_NO=b.MOBILE_NO) and a.C\
ANCEL_DATE is null )";
#line 556 "esemco0r.pc"
                                sqlstm.iters = (unsigned int  )1;
#line 556 "esemco0r.pc"
                                sqlstm.offset = (unsigned int  )703;
#line 556 "esemco0r.pc"
                                sqlstm.selerr = (unsigned short)1;
#line 556 "esemco0r.pc"
                                sqlstm.sqlpfmem = (unsigned int  )0;
#line 556 "esemco0r.pc"
                                sqlstm.cud = sqlcud0;
#line 556 "esemco0r.pc"
                                sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 556 "esemco0r.pc"
                                sqlstm.sqlety = (unsigned short)4352;
#line 556 "esemco0r.pc"
                                sqlstm.occurs = (unsigned int  )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqhstv[0] = (unsigned char  *)&o_mobile_cnt;
#line 556 "esemco0r.pc"
                                sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
#line 556 "esemco0r.pc"
                                sqlstm.sqhsts[0] = (         int  )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqindv[0] = (         short *)0;
#line 556 "esemco0r.pc"
                                sqlstm.sqinds[0] = (         int  )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqharm[0] = (unsigned long )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqadto[0] = (unsigned short )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqtdso[0] = (unsigned short )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqhstv[1] = (unsigned char  *)&o_rx_freq_lo;
#line 556 "esemco0r.pc"
                                sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
#line 556 "esemco0r.pc"
                                sqlstm.sqhsts[1] = (         int  )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqindv[1] = (         short *)0;
#line 556 "esemco0r.pc"
                                sqlstm.sqinds[1] = (         int  )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqharm[1] = (unsigned long )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqadto[1] = (unsigned short )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqtdso[1] = (unsigned short )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqhstv[2] = (unsigned char  *)&o_rx_freq_hi;
#line 556 "esemco0r.pc"
                                sqlstm.sqhstl[2] = (unsigned long )sizeof(double);
#line 556 "esemco0r.pc"
                                sqlstm.sqhsts[2] = (         int  )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqindv[2] = (         short *)0;
#line 556 "esemco0r.pc"
                                sqlstm.sqinds[2] = (         int  )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqharm[2] = (unsigned long )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqadto[2] = (unsigned short )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqtdso[2] = (unsigned short )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqhstv[3] = (unsigned char  *)&o_tx_freq_lo;
#line 556 "esemco0r.pc"
                                sqlstm.sqhstl[3] = (unsigned long )sizeof(double);
#line 556 "esemco0r.pc"
                                sqlstm.sqhsts[3] = (         int  )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqindv[3] = (         short *)0;
#line 556 "esemco0r.pc"
                                sqlstm.sqinds[3] = (         int  )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqharm[3] = (unsigned long )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqadto[3] = (unsigned short )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqtdso[3] = (unsigned short )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqhstv[4] = (unsigned char  *)&o_tx_freq_hi;
#line 556 "esemco0r.pc"
                                sqlstm.sqhstl[4] = (unsigned long )sizeof(double);
#line 556 "esemco0r.pc"
                                sqlstm.sqhsts[4] = (         int  )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqindv[4] = (         short *)0;
#line 556 "esemco0r.pc"
                                sqlstm.sqinds[4] = (         int  )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqharm[4] = (unsigned long )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqadto[4] = (unsigned short )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqtdso[4] = (unsigned short )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqhstv[5] = (unsigned char  *)&o_sys_category;
#line 556 "esemco0r.pc"
                                sqlstm.sqhstl[5] = (unsigned long )1;
#line 556 "esemco0r.pc"
                                sqlstm.sqhsts[5] = (         int  )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqindv[5] = (         short *)0;
#line 556 "esemco0r.pc"
                                sqlstm.sqinds[5] = (         int  )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqharm[5] = (unsigned long )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqadto[5] = (unsigned short )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqtdso[5] = (unsigned short )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqhstv[6] = (unsigned char  *)&o_sys_type;
#line 556 "esemco0r.pc"
                                sqlstm.sqhstl[6] = (unsigned long )5;
#line 556 "esemco0r.pc"
                                sqlstm.sqhsts[6] = (         int  )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqindv[6] = (         short *)0;
#line 556 "esemco0r.pc"
                                sqlstm.sqinds[6] = (         int  )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqharm[6] = (unsigned long )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqadto[6] = (unsigned short )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqtdso[6] = (unsigned short )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqhstv[7] = (unsigned char  *)&o_sys_no;
#line 556 "esemco0r.pc"
                                sqlstm.sqhstl[7] = (unsigned long )10;
#line 556 "esemco0r.pc"
                                sqlstm.sqhsts[7] = (         int  )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqindv[7] = (         short *)0;
#line 556 "esemco0r.pc"
                                sqlstm.sqinds[7] = (         int  )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqharm[7] = (unsigned long )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqadto[7] = (unsigned short )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqtdso[7] = (unsigned short )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqhstv[8] = (unsigned char  *)&o_sys_suffix_2;
#line 556 "esemco0r.pc"
                                sqlstm.sqhstl[8] = (unsigned long )6;
#line 556 "esemco0r.pc"
                                sqlstm.sqhsts[8] = (         int  )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqindv[8] = (         short *)0;
#line 556 "esemco0r.pc"
                                sqlstm.sqinds[8] = (         int  )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqharm[8] = (unsigned long )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqadto[8] = (unsigned short )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqtdso[8] = (unsigned short )0;
#line 556 "esemco0r.pc"
                                sqlstm.sqphsv = sqlstm.sqhstv;
#line 556 "esemco0r.pc"
                                sqlstm.sqphsl = sqlstm.sqhstl;
#line 556 "esemco0r.pc"
                                sqlstm.sqphss = sqlstm.sqhsts;
#line 556 "esemco0r.pc"
                                sqlstm.sqpind = sqlstm.sqindv;
#line 556 "esemco0r.pc"
                                sqlstm.sqpins = sqlstm.sqinds;
#line 556 "esemco0r.pc"
                                sqlstm.sqparm = sqlstm.sqharm;
#line 556 "esemco0r.pc"
                                sqlstm.sqparc = sqlstm.sqharc;
#line 556 "esemco0r.pc"
                                sqlstm.sqpadto = sqlstm.sqadto;
#line 556 "esemco0r.pc"
                                sqlstm.sqptdso = sqlstm.sqtdso;
#line 556 "esemco0r.pc"
                                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 556 "esemco0r.pc"
                                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 556 "esemco0r.pc"
}

#line 572 "esemco0r.pc"

printf( "esemco0r 061e1  EXEC SQL  \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   061e1  EXEC SQL   %c | %s | %s - %s | %s | %d \" >> /tmp/debug", sys_date ,sys_time  , o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr,curr_sys, o_base_no);
system(msg);
			
											
			

printf( "esemco0r 061e2 after break  tmp_mobile_cnt=%d o_mobile_cnt=%d \n\r" , tmp_mobile_cnt , o_mobile_cnt ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  061e2 after break  tmp_mobile_cnt=%d o_mobile_cnt=%d  \" >> /tmp/debug",  sys_date ,sys_time , tmp_mobile_cnt , o_mobile_cnt);	
system(msg);
	
       

			
																							

                                tmp_mobile_cnt = tmp_mobile_cnt + o_mobile_cnt;


printf( "esemco0r 061e3 after break  tmp_mobile_cnt=%d o_mobile_cnt=%d \n\r" , tmp_mobile_cnt , o_mobile_cnt ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  061e3 after break  tmp_mobile_cnt=%d o_mobile_cnt=%d  \" >> /tmp/debug",  sys_date ,sys_time , tmp_mobile_cnt , o_mobile_cnt);	
system(msg);
								
                            }

printf( "esemco0r 061f after break  tmp_mobile_cnt=%d o_mobile_cnt=%d \n\r" , tmp_mobile_cnt , o_mobile_cnt ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  061f after break  tmp_mobile_cnt=%d o_mobile_cnt=%d  \" >> /tmp/debug",  sys_date ,sys_time , tmp_mobile_cnt , o_mobile_cnt);	
system(msg);							
							
                        }
                        o_mobile_cnt = tmp_mobile_cnt;
						
printf( "esemco0r 061g after break  o_mobile_cnt=%d \n\r" ,  o_mobile_cnt ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  061g after break  o_mobile_cnt=%d  \" >> /tmp/debug",  sys_date ,sys_time ,  o_mobile_cnt);	
system(msg);
						
                    }
/**/

printf( "esemco0r 061h after break  o_mobile_cnt=%d \n\r" ,  o_mobile_cnt ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  061h after break  o_mobile_cnt=%d  \" >> /tmp/debug",  sys_date ,sys_time ,  o_mobile_cnt);	
system(msg);
				      break;

                case TX_ONLY :

/* Alex Yeung 1999-08-05
                    EXEC SQL
                         SELECT COUNT(UNIQUE MOBILE_NO)
*/
/* comment by Chen Yung
                    EXEC SQL
                         SELECT COUNT(DISTINCT MOBILE_NO)

                         INTO   :o_mobile_cnt
                         FROM   MOBILE_CH
                         WHERE  TX_FREQ between :o_rx_freq_lo and :o_rx_freq_hi
                         AND    RX_FREQ IS NULL
                         AND    SYS_CATEGORY = :o_sys_category
                         AND    SYS_TYPE = :o_sys_type
                         AND    SYS_NO = :o_sys_no
                         AND    SYS_SUFFIX = :o_sys_suffix
                         AND    CANCEL_DATE IS NULL;
*/

printf( "esemco0r 062a EXEC SQL TX_ONLY \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   062a EXEC SQL TX_ONLY \" >> /tmp/debug", sys_date ,sys_time);	
system(msg);	

                    /* EXEC SQL
                        SELECT COUNT(DISTINCT b.MOBILE_NO)
                        INTO   :o_mobile_cnt
                        FROM   MOBILE a, MOBILE_CH b
                        WHERE  b.TX_FREQ between :o_rx_freq_lo and :o_rx_freq_hi
                        AND    b.RX_FREQ IS NULL
                        AND    b.SYS_CATEGORY = :o_sys_category
                        AND    b.SYS_TYPE = :o_sys_type
                        AND    b.SYS_NO = :o_sys_no
                        AND    b.SYS_SUFFIX = :o_sys_suffix
                        AND    b.CANCEL_DATE IS NULL
                        AND    a.SYS_CATEGORY = b.SYS_CATEGORY
                        AND    a.SYS_TYPE = b.SYS_TYPE
                        AND    a.SYS_NO = b.SYS_NO
                        AND    a.SYS_SUFFIX = b.SYS_SUFFIX
                        AND    a.MOBILE_NO = b.MOBILE_NO
                        AND    a.CANCEL_DATE IS NULL; */ 
#line 627 "esemco0r.pc"

{
#line 611 "esemco0r.pc"
                    struct sqlexd sqlstm;
#line 611 "esemco0r.pc"
                    sqlstm.sqlvsn = 13;
#line 611 "esemco0r.pc"
                    sqlstm.arrsiz = 10;
#line 611 "esemco0r.pc"
                    sqlstm.sqladtp = &sqladt;
#line 611 "esemco0r.pc"
                    sqlstm.sqltdsp = &sqltds;
#line 611 "esemco0r.pc"
                    sqlstm.stmt = "select count(distinct b.MOBILE_NO) into :\
b0  from MOBILE a ,MOBILE_CH b where ((((((((((((b.TX_FREQ between :b1 and :b2\
 and b.RX_FREQ is null ) and b.SYS_CATEGORY=:b3) and b.SYS_TYPE=:b4) and b.SYS\
_NO=:b5) and b.SYS_SUFFIX=:b6) and b.CANCEL_DATE is null ) and a.SYS_CATEGORY=\
b.SYS_CATEGORY) and a.SYS_TYPE=b.SYS_TYPE) and a.SYS_NO=b.SYS_NO) and a.SYS_SU\
FFIX=b.SYS_SUFFIX) and a.MOBILE_NO=b.MOBILE_NO) and a.CANCEL_DATE is null )";
#line 611 "esemco0r.pc"
                    sqlstm.iters = (unsigned int  )1;
#line 611 "esemco0r.pc"
                    sqlstm.offset = (unsigned int  )754;
#line 611 "esemco0r.pc"
                    sqlstm.selerr = (unsigned short)1;
#line 611 "esemco0r.pc"
                    sqlstm.sqlpfmem = (unsigned int  )0;
#line 611 "esemco0r.pc"
                    sqlstm.cud = sqlcud0;
#line 611 "esemco0r.pc"
                    sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 611 "esemco0r.pc"
                    sqlstm.sqlety = (unsigned short)4352;
#line 611 "esemco0r.pc"
                    sqlstm.occurs = (unsigned int  )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqhstv[0] = (unsigned char  *)&o_mobile_cnt;
#line 611 "esemco0r.pc"
                    sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
#line 611 "esemco0r.pc"
                    sqlstm.sqhsts[0] = (         int  )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqindv[0] = (         short *)0;
#line 611 "esemco0r.pc"
                    sqlstm.sqinds[0] = (         int  )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqharm[0] = (unsigned long )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqadto[0] = (unsigned short )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqtdso[0] = (unsigned short )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqhstv[1] = (unsigned char  *)&o_rx_freq_lo;
#line 611 "esemco0r.pc"
                    sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
#line 611 "esemco0r.pc"
                    sqlstm.sqhsts[1] = (         int  )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqindv[1] = (         short *)0;
#line 611 "esemco0r.pc"
                    sqlstm.sqinds[1] = (         int  )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqharm[1] = (unsigned long )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqadto[1] = (unsigned short )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqtdso[1] = (unsigned short )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqhstv[2] = (unsigned char  *)&o_rx_freq_hi;
#line 611 "esemco0r.pc"
                    sqlstm.sqhstl[2] = (unsigned long )sizeof(double);
#line 611 "esemco0r.pc"
                    sqlstm.sqhsts[2] = (         int  )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqindv[2] = (         short *)0;
#line 611 "esemco0r.pc"
                    sqlstm.sqinds[2] = (         int  )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqharm[2] = (unsigned long )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqadto[2] = (unsigned short )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqtdso[2] = (unsigned short )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_category;
#line 611 "esemco0r.pc"
                    sqlstm.sqhstl[3] = (unsigned long )1;
#line 611 "esemco0r.pc"
                    sqlstm.sqhsts[3] = (         int  )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqindv[3] = (         short *)0;
#line 611 "esemco0r.pc"
                    sqlstm.sqinds[3] = (         int  )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqharm[3] = (unsigned long )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqadto[3] = (unsigned short )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqtdso[3] = (unsigned short )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqhstv[4] = (unsigned char  *)&o_sys_type;
#line 611 "esemco0r.pc"
                    sqlstm.sqhstl[4] = (unsigned long )5;
#line 611 "esemco0r.pc"
                    sqlstm.sqhsts[4] = (         int  )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqindv[4] = (         short *)0;
#line 611 "esemco0r.pc"
                    sqlstm.sqinds[4] = (         int  )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqharm[4] = (unsigned long )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqadto[4] = (unsigned short )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqtdso[4] = (unsigned short )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqhstv[5] = (unsigned char  *)&o_sys_no;
#line 611 "esemco0r.pc"
                    sqlstm.sqhstl[5] = (unsigned long )10;
#line 611 "esemco0r.pc"
                    sqlstm.sqhsts[5] = (         int  )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqindv[5] = (         short *)0;
#line 611 "esemco0r.pc"
                    sqlstm.sqinds[5] = (         int  )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqharm[5] = (unsigned long )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqadto[5] = (unsigned short )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqtdso[5] = (unsigned short )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqhstv[6] = (unsigned char  *)&o_sys_suffix;
#line 611 "esemco0r.pc"
                    sqlstm.sqhstl[6] = (unsigned long )6;
#line 611 "esemco0r.pc"
                    sqlstm.sqhsts[6] = (         int  )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqindv[6] = (         short *)0;
#line 611 "esemco0r.pc"
                    sqlstm.sqinds[6] = (         int  )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqharm[6] = (unsigned long )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqadto[6] = (unsigned short )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqtdso[6] = (unsigned short )0;
#line 611 "esemco0r.pc"
                    sqlstm.sqphsv = sqlstm.sqhstv;
#line 611 "esemco0r.pc"
                    sqlstm.sqphsl = sqlstm.sqhstl;
#line 611 "esemco0r.pc"
                    sqlstm.sqphss = sqlstm.sqhsts;
#line 611 "esemco0r.pc"
                    sqlstm.sqpind = sqlstm.sqindv;
#line 611 "esemco0r.pc"
                    sqlstm.sqpins = sqlstm.sqinds;
#line 611 "esemco0r.pc"
                    sqlstm.sqparm = sqlstm.sqharm;
#line 611 "esemco0r.pc"
                    sqlstm.sqparc = sqlstm.sqharc;
#line 611 "esemco0r.pc"
                    sqlstm.sqpadto = sqlstm.sqadto;
#line 611 "esemco0r.pc"
                    sqlstm.sqptdso = sqlstm.sqtdso;
#line 611 "esemco0r.pc"
                    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 611 "esemco0r.pc"
                    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 611 "esemco0r.pc"
}

#line 627 "esemco0r.pc"


printf( "esemco0r 062b EXEC SQL TX_ONLY \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   062b EXEC SQL TX_ONLY \" >> /tmp/debug", sys_date ,sys_time);	
system(msg);							
						
/* added by Chen Yung for handling merged systems and ZZx systems */

printf( "esemco0r 062c EXEC SQL TX_ONLY \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   062c EXEC SQL TX_ONLY \" >> /tmp/debug", sys_date ,sys_time);	
system(msg);	

                    /* EXEC SQL
                        SELECT  DISTINCT SYS_ID
                        INTO    :o_sys_id
                        FROM    ESMS_MAP
                        WHERE   SYS_CATEGORY = :o_sys_category
                        AND     SYS_TYPE = :o_sys_type
                        AND     SYS_NO = :o_sys_no
                        AND     SYS_SUFFIX = :o_sys_suffix
                        AND     SYS_LINK = 'L'; */ 
#line 647 "esemco0r.pc"

{
#line 639 "esemco0r.pc"
                    struct sqlexd sqlstm;
#line 639 "esemco0r.pc"
                    sqlstm.sqlvsn = 13;
#line 639 "esemco0r.pc"
                    sqlstm.arrsiz = 10;
#line 639 "esemco0r.pc"
                    sqlstm.sqladtp = &sqladt;
#line 639 "esemco0r.pc"
                    sqlstm.sqltdsp = &sqltds;
#line 639 "esemco0r.pc"
                    sqlstm.stmt = "select distinct SYS_ID into :b0  from ESM\
S_MAP where ((((SYS_CATEGORY=:b1 and SYS_TYPE=:b2) and SYS_NO=:b3) and SYS_SUF\
FIX=:b4) and SYS_LINK='L')";
#line 639 "esemco0r.pc"
                    sqlstm.iters = (unsigned int  )1;
#line 639 "esemco0r.pc"
                    sqlstm.offset = (unsigned int  )797;
#line 639 "esemco0r.pc"
                    sqlstm.selerr = (unsigned short)1;
#line 639 "esemco0r.pc"
                    sqlstm.sqlpfmem = (unsigned int  )0;
#line 639 "esemco0r.pc"
                    sqlstm.cud = sqlcud0;
#line 639 "esemco0r.pc"
                    sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 639 "esemco0r.pc"
                    sqlstm.sqlety = (unsigned short)4352;
#line 639 "esemco0r.pc"
                    sqlstm.occurs = (unsigned int  )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_id;
#line 639 "esemco0r.pc"
                    sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
#line 639 "esemco0r.pc"
                    sqlstm.sqhsts[0] = (         int  )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqindv[0] = (         short *)0;
#line 639 "esemco0r.pc"
                    sqlstm.sqinds[0] = (         int  )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqharm[0] = (unsigned long )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqadto[0] = (unsigned short )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqtdso[0] = (unsigned short )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_category;
#line 639 "esemco0r.pc"
                    sqlstm.sqhstl[1] = (unsigned long )1;
#line 639 "esemco0r.pc"
                    sqlstm.sqhsts[1] = (         int  )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqindv[1] = (         short *)0;
#line 639 "esemco0r.pc"
                    sqlstm.sqinds[1] = (         int  )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqharm[1] = (unsigned long )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqadto[1] = (unsigned short )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqtdso[1] = (unsigned short )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_type;
#line 639 "esemco0r.pc"
                    sqlstm.sqhstl[2] = (unsigned long )5;
#line 639 "esemco0r.pc"
                    sqlstm.sqhsts[2] = (         int  )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqindv[2] = (         short *)0;
#line 639 "esemco0r.pc"
                    sqlstm.sqinds[2] = (         int  )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqharm[2] = (unsigned long )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqadto[2] = (unsigned short )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqtdso[2] = (unsigned short )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_no;
#line 639 "esemco0r.pc"
                    sqlstm.sqhstl[3] = (unsigned long )10;
#line 639 "esemco0r.pc"
                    sqlstm.sqhsts[3] = (         int  )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqindv[3] = (         short *)0;
#line 639 "esemco0r.pc"
                    sqlstm.sqinds[3] = (         int  )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqharm[3] = (unsigned long )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqadto[3] = (unsigned short )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqtdso[3] = (unsigned short )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqhstv[4] = (unsigned char  *)&o_sys_suffix;
#line 639 "esemco0r.pc"
                    sqlstm.sqhstl[4] = (unsigned long )6;
#line 639 "esemco0r.pc"
                    sqlstm.sqhsts[4] = (         int  )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqindv[4] = (         short *)0;
#line 639 "esemco0r.pc"
                    sqlstm.sqinds[4] = (         int  )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqharm[4] = (unsigned long )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqadto[4] = (unsigned short )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqtdso[4] = (unsigned short )0;
#line 639 "esemco0r.pc"
                    sqlstm.sqphsv = sqlstm.sqhstv;
#line 639 "esemco0r.pc"
                    sqlstm.sqphsl = sqlstm.sqhstl;
#line 639 "esemco0r.pc"
                    sqlstm.sqphss = sqlstm.sqhsts;
#line 639 "esemco0r.pc"
                    sqlstm.sqpind = sqlstm.sqindv;
#line 639 "esemco0r.pc"
                    sqlstm.sqpins = sqlstm.sqinds;
#line 639 "esemco0r.pc"
                    sqlstm.sqparm = sqlstm.sqharm;
#line 639 "esemco0r.pc"
                    sqlstm.sqparc = sqlstm.sqharc;
#line 639 "esemco0r.pc"
                    sqlstm.sqpadto = sqlstm.sqadto;
#line 639 "esemco0r.pc"
                    sqlstm.sqptdso = sqlstm.sqtdso;
#line 639 "esemco0r.pc"
                    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 639 "esemco0r.pc"
                    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 639 "esemco0r.pc"
}

#line 647 "esemco0r.pc"

                   
printf( "esemco0r 062d EXEC SQL TX_ONLY \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   062d EXEC SQL TX_ONLY \" >> /tmp/debug", sys_date ,sys_time);	
system(msg);	
				   
                    if (sqlca.sqlcode != NOT_FOUND) 
                    {
                        /* EXEC SQL DECLARE CO05 CURSOR FOR
                            SELECT    DISTINCT SYS_SUFFIX
                            FROM      ESMS_MAP
                            WHERE     SYS_ID = :o_sys_id
                            AND       SYS_SUFFIX <> :o_sys_suffix
                            ORDER BY  SYS_SUFFIX; */ 
#line 660 "esemco0r.pc"


                        /* EXEC SQL OPEN CO05; */ 
#line 662 "esemco0r.pc"

{
#line 662 "esemco0r.pc"
                        struct sqlexd sqlstm;
#line 662 "esemco0r.pc"
                        sqlstm.sqlvsn = 13;
#line 662 "esemco0r.pc"
                        sqlstm.arrsiz = 10;
#line 662 "esemco0r.pc"
                        sqlstm.sqladtp = &sqladt;
#line 662 "esemco0r.pc"
                        sqlstm.sqltdsp = &sqltds;
#line 662 "esemco0r.pc"
                        sqlstm.stmt = sq0011;
#line 662 "esemco0r.pc"
                        sqlstm.iters = (unsigned int  )1;
#line 662 "esemco0r.pc"
                        sqlstm.offset = (unsigned int  )832;
#line 662 "esemco0r.pc"
                        sqlstm.selerr = (unsigned short)1;
#line 662 "esemco0r.pc"
                        sqlstm.sqlpfmem = (unsigned int  )0;
#line 662 "esemco0r.pc"
                        sqlstm.cud = sqlcud0;
#line 662 "esemco0r.pc"
                        sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 662 "esemco0r.pc"
                        sqlstm.sqlety = (unsigned short)4352;
#line 662 "esemco0r.pc"
                        sqlstm.occurs = (unsigned int  )0;
#line 662 "esemco0r.pc"
                        sqlstm.sqcmod = (unsigned int )0;
#line 662 "esemco0r.pc"
                        sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_id;
#line 662 "esemco0r.pc"
                        sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
#line 662 "esemco0r.pc"
                        sqlstm.sqhsts[0] = (         int  )0;
#line 662 "esemco0r.pc"
                        sqlstm.sqindv[0] = (         short *)0;
#line 662 "esemco0r.pc"
                        sqlstm.sqinds[0] = (         int  )0;
#line 662 "esemco0r.pc"
                        sqlstm.sqharm[0] = (unsigned long )0;
#line 662 "esemco0r.pc"
                        sqlstm.sqadto[0] = (unsigned short )0;
#line 662 "esemco0r.pc"
                        sqlstm.sqtdso[0] = (unsigned short )0;
#line 662 "esemco0r.pc"
                        sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_suffix;
#line 662 "esemco0r.pc"
                        sqlstm.sqhstl[1] = (unsigned long )6;
#line 662 "esemco0r.pc"
                        sqlstm.sqhsts[1] = (         int  )0;
#line 662 "esemco0r.pc"
                        sqlstm.sqindv[1] = (         short *)0;
#line 662 "esemco0r.pc"
                        sqlstm.sqinds[1] = (         int  )0;
#line 662 "esemco0r.pc"
                        sqlstm.sqharm[1] = (unsigned long )0;
#line 662 "esemco0r.pc"
                        sqlstm.sqadto[1] = (unsigned short )0;
#line 662 "esemco0r.pc"
                        sqlstm.sqtdso[1] = (unsigned short )0;
#line 662 "esemco0r.pc"
                        sqlstm.sqphsv = sqlstm.sqhstv;
#line 662 "esemco0r.pc"
                        sqlstm.sqphsl = sqlstm.sqhstl;
#line 662 "esemco0r.pc"
                        sqlstm.sqphss = sqlstm.sqhsts;
#line 662 "esemco0r.pc"
                        sqlstm.sqpind = sqlstm.sqindv;
#line 662 "esemco0r.pc"
                        sqlstm.sqpins = sqlstm.sqinds;
#line 662 "esemco0r.pc"
                        sqlstm.sqparm = sqlstm.sqharm;
#line 662 "esemco0r.pc"
                        sqlstm.sqparc = sqlstm.sqharc;
#line 662 "esemco0r.pc"
                        sqlstm.sqpadto = sqlstm.sqadto;
#line 662 "esemco0r.pc"
                        sqlstm.sqptdso = sqlstm.sqtdso;
#line 662 "esemco0r.pc"
                        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 662 "esemco0r.pc"
                        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 662 "esemco0r.pc"
}

#line 662 "esemco0r.pc"








                        tmp_mobile_cnt = o_mobile_cnt;
						
						
printf( "esemco0r 062d3 after break  tmp_mobile_cnt=%d o_mobile_cnt=%d \n\r" , tmp_mobile_cnt , o_mobile_cnt ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  062d3 after break  tmp_mobile_cnt=%d o_mobile_cnt=%d  \" >> /tmp/debug",  sys_date ,sys_time , tmp_mobile_cnt , o_mobile_cnt);	
system(msg);

						
                        for ( ; ; )
                        {
                            /* EXEC SQL
                                 FETCH CO05
                                 INTO  :o_sys_suffix_2; */ 
#line 668 "esemco0r.pc"

{
#line 666 "esemco0r.pc"
                            struct sqlexd sqlstm;
#line 666 "esemco0r.pc"
                            sqlstm.sqlvsn = 13;
#line 666 "esemco0r.pc"
                            sqlstm.arrsiz = 10;
#line 666 "esemco0r.pc"
                            sqlstm.sqladtp = &sqladt;
#line 666 "esemco0r.pc"
                            sqlstm.sqltdsp = &sqltds;
#line 666 "esemco0r.pc"
                            sqlstm.iters = (unsigned int  )1;
#line 666 "esemco0r.pc"
                            sqlstm.offset = (unsigned int  )855;
#line 666 "esemco0r.pc"
                            sqlstm.selerr = (unsigned short)1;
#line 666 "esemco0r.pc"
                            sqlstm.sqlpfmem = (unsigned int  )0;
#line 666 "esemco0r.pc"
                            sqlstm.cud = sqlcud0;
#line 666 "esemco0r.pc"
                            sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 666 "esemco0r.pc"
                            sqlstm.sqlety = (unsigned short)4352;
#line 666 "esemco0r.pc"
                            sqlstm.occurs = (unsigned int  )0;
#line 666 "esemco0r.pc"
                            sqlstm.sqfoff = (         int )0;
#line 666 "esemco0r.pc"
                            sqlstm.sqfmod = (unsigned int )2;
#line 666 "esemco0r.pc"
                            sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_suffix_2;
#line 666 "esemco0r.pc"
                            sqlstm.sqhstl[0] = (unsigned long )6;
#line 666 "esemco0r.pc"
                            sqlstm.sqhsts[0] = (         int  )0;
#line 666 "esemco0r.pc"
                            sqlstm.sqindv[0] = (         short *)0;
#line 666 "esemco0r.pc"
                            sqlstm.sqinds[0] = (         int  )0;
#line 666 "esemco0r.pc"
                            sqlstm.sqharm[0] = (unsigned long )0;
#line 666 "esemco0r.pc"
                            sqlstm.sqadto[0] = (unsigned short )0;
#line 666 "esemco0r.pc"
                            sqlstm.sqtdso[0] = (unsigned short )0;
#line 666 "esemco0r.pc"
                            sqlstm.sqphsv = sqlstm.sqhstv;
#line 666 "esemco0r.pc"
                            sqlstm.sqphsl = sqlstm.sqhstl;
#line 666 "esemco0r.pc"
                            sqlstm.sqphss = sqlstm.sqhsts;
#line 666 "esemco0r.pc"
                            sqlstm.sqpind = sqlstm.sqindv;
#line 666 "esemco0r.pc"
                            sqlstm.sqpins = sqlstm.sqinds;
#line 666 "esemco0r.pc"
                            sqlstm.sqparm = sqlstm.sqharm;
#line 666 "esemco0r.pc"
                            sqlstm.sqparc = sqlstm.sqharc;
#line 666 "esemco0r.pc"
                            sqlstm.sqpadto = sqlstm.sqadto;
#line 666 "esemco0r.pc"
                            sqlstm.sqptdso = sqlstm.sqtdso;
#line 666 "esemco0r.pc"
                            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 666 "esemco0r.pc"
                            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 666 "esemco0r.pc"
}

#line 668 "esemco0r.pc"

                            if (sqlca.sqlcode == NOT_FOUND) 
                            {
                                /* EXEC SQL CLOSE CO05; */ 
#line 671 "esemco0r.pc"

{
#line 671 "esemco0r.pc"
                                struct sqlexd sqlstm;
#line 671 "esemco0r.pc"
                                sqlstm.sqlvsn = 13;
#line 671 "esemco0r.pc"
                                sqlstm.arrsiz = 10;
#line 671 "esemco0r.pc"
                                sqlstm.sqladtp = &sqladt;
#line 671 "esemco0r.pc"
                                sqlstm.sqltdsp = &sqltds;
#line 671 "esemco0r.pc"
                                sqlstm.iters = (unsigned int  )1;
#line 671 "esemco0r.pc"
                                sqlstm.offset = (unsigned int  )874;
#line 671 "esemco0r.pc"
                                sqlstm.cud = sqlcud0;
#line 671 "esemco0r.pc"
                                sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 671 "esemco0r.pc"
                                sqlstm.sqlety = (unsigned short)4352;
#line 671 "esemco0r.pc"
                                sqlstm.occurs = (unsigned int  )0;
#line 671 "esemco0r.pc"
                                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 671 "esemco0r.pc"
                                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 671 "esemco0r.pc"
}

#line 671 "esemco0r.pc"

                                break;
                            }
                            else 
                            {

printf( "esemco0r 062e EXEC SQL TX_ONLY \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   062e EXEC SQL TX_ONLY \" >> /tmp/debug", sys_date ,sys_time);	
system(msg);							
								
                                /* EXEC SQL
                                    SELECT COUNT(DISTINCT b.MOBILE_NO)
                                    INTO   :o_mobile_cnt
                                    FROM   MOBILE a, MOBILE_CH b
                                    WHERE  b.TX_FREQ between :o_rx_freq_lo and :o_rx_freq_hi
                                    AND    b.RX_FREQ IS NULL
                                    AND    b.SYS_CATEGORY = :o_sys_category
                                    AND    b.SYS_TYPE = :o_sys_type
                                    AND    b.SYS_NO = :o_sys_no
                                    AND    b.SYS_SUFFIX = :o_sys_suffix_2
                                    AND    b.CANCEL_DATE IS NULL
                                    AND    a.SYS_CATEGORY = b.SYS_CATEGORY
                                    AND    a.SYS_TYPE = b.SYS_TYPE
                                    AND    a.SYS_NO = b.SYS_NO
                                    AND    a.SYS_SUFFIX = b.SYS_SUFFIX
                                    AND    a.MOBILE_NO = b.MOBILE_NO
                                    AND    a.CANCEL_DATE IS NULL; */ 
#line 697 "esemco0r.pc"

{
#line 681 "esemco0r.pc"
                                struct sqlexd sqlstm;
#line 681 "esemco0r.pc"
                                sqlstm.sqlvsn = 13;
#line 681 "esemco0r.pc"
                                sqlstm.arrsiz = 10;
#line 681 "esemco0r.pc"
                                sqlstm.sqladtp = &sqladt;
#line 681 "esemco0r.pc"
                                sqlstm.sqltdsp = &sqltds;
#line 681 "esemco0r.pc"
                                sqlstm.stmt = "select count(distinct b.MOBIL\
E_NO) into :b0  from MOBILE a ,MOBILE_CH b where ((((((((((((b.TX_FREQ between\
 :b1 and :b2 and b.RX_FREQ is null ) and b.SYS_CATEGORY=:b3) and b.SYS_TYPE=:b\
4) and b.SYS_NO=:b5) and b.SYS_SUFFIX=:b6) and b.CANCEL_DATE is null ) and a.S\
YS_CATEGORY=b.SYS_CATEGORY) and a.SYS_TYPE=b.SYS_TYPE) and a.SYS_NO=b.SYS_NO) \
and a.SYS_SUFFIX=b.SYS_SUFFIX) and a.MOBILE_NO=b.MOBILE_NO) and a.CANCEL_DATE \
is null )";
#line 681 "esemco0r.pc"
                                sqlstm.iters = (unsigned int  )1;
#line 681 "esemco0r.pc"
                                sqlstm.offset = (unsigned int  )889;
#line 681 "esemco0r.pc"
                                sqlstm.selerr = (unsigned short)1;
#line 681 "esemco0r.pc"
                                sqlstm.sqlpfmem = (unsigned int  )0;
#line 681 "esemco0r.pc"
                                sqlstm.cud = sqlcud0;
#line 681 "esemco0r.pc"
                                sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 681 "esemco0r.pc"
                                sqlstm.sqlety = (unsigned short)4352;
#line 681 "esemco0r.pc"
                                sqlstm.occurs = (unsigned int  )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqhstv[0] = (unsigned char  *)&o_mobile_cnt;
#line 681 "esemco0r.pc"
                                sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
#line 681 "esemco0r.pc"
                                sqlstm.sqhsts[0] = (         int  )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqindv[0] = (         short *)0;
#line 681 "esemco0r.pc"
                                sqlstm.sqinds[0] = (         int  )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqharm[0] = (unsigned long )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqadto[0] = (unsigned short )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqtdso[0] = (unsigned short )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqhstv[1] = (unsigned char  *)&o_rx_freq_lo;
#line 681 "esemco0r.pc"
                                sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
#line 681 "esemco0r.pc"
                                sqlstm.sqhsts[1] = (         int  )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqindv[1] = (         short *)0;
#line 681 "esemco0r.pc"
                                sqlstm.sqinds[1] = (         int  )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqharm[1] = (unsigned long )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqadto[1] = (unsigned short )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqtdso[1] = (unsigned short )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqhstv[2] = (unsigned char  *)&o_rx_freq_hi;
#line 681 "esemco0r.pc"
                                sqlstm.sqhstl[2] = (unsigned long )sizeof(double);
#line 681 "esemco0r.pc"
                                sqlstm.sqhsts[2] = (         int  )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqindv[2] = (         short *)0;
#line 681 "esemco0r.pc"
                                sqlstm.sqinds[2] = (         int  )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqharm[2] = (unsigned long )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqadto[2] = (unsigned short )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqtdso[2] = (unsigned short )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_category;
#line 681 "esemco0r.pc"
                                sqlstm.sqhstl[3] = (unsigned long )1;
#line 681 "esemco0r.pc"
                                sqlstm.sqhsts[3] = (         int  )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqindv[3] = (         short *)0;
#line 681 "esemco0r.pc"
                                sqlstm.sqinds[3] = (         int  )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqharm[3] = (unsigned long )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqadto[3] = (unsigned short )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqtdso[3] = (unsigned short )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqhstv[4] = (unsigned char  *)&o_sys_type;
#line 681 "esemco0r.pc"
                                sqlstm.sqhstl[4] = (unsigned long )5;
#line 681 "esemco0r.pc"
                                sqlstm.sqhsts[4] = (         int  )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqindv[4] = (         short *)0;
#line 681 "esemco0r.pc"
                                sqlstm.sqinds[4] = (         int  )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqharm[4] = (unsigned long )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqadto[4] = (unsigned short )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqtdso[4] = (unsigned short )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqhstv[5] = (unsigned char  *)&o_sys_no;
#line 681 "esemco0r.pc"
                                sqlstm.sqhstl[5] = (unsigned long )10;
#line 681 "esemco0r.pc"
                                sqlstm.sqhsts[5] = (         int  )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqindv[5] = (         short *)0;
#line 681 "esemco0r.pc"
                                sqlstm.sqinds[5] = (         int  )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqharm[5] = (unsigned long )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqadto[5] = (unsigned short )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqtdso[5] = (unsigned short )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqhstv[6] = (unsigned char  *)&o_sys_suffix_2;
#line 681 "esemco0r.pc"
                                sqlstm.sqhstl[6] = (unsigned long )6;
#line 681 "esemco0r.pc"
                                sqlstm.sqhsts[6] = (         int  )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqindv[6] = (         short *)0;
#line 681 "esemco0r.pc"
                                sqlstm.sqinds[6] = (         int  )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqharm[6] = (unsigned long )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqadto[6] = (unsigned short )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqtdso[6] = (unsigned short )0;
#line 681 "esemco0r.pc"
                                sqlstm.sqphsv = sqlstm.sqhstv;
#line 681 "esemco0r.pc"
                                sqlstm.sqphsl = sqlstm.sqhstl;
#line 681 "esemco0r.pc"
                                sqlstm.sqphss = sqlstm.sqhsts;
#line 681 "esemco0r.pc"
                                sqlstm.sqpind = sqlstm.sqindv;
#line 681 "esemco0r.pc"
                                sqlstm.sqpins = sqlstm.sqinds;
#line 681 "esemco0r.pc"
                                sqlstm.sqparm = sqlstm.sqharm;
#line 681 "esemco0r.pc"
                                sqlstm.sqparc = sqlstm.sqharc;
#line 681 "esemco0r.pc"
                                sqlstm.sqpadto = sqlstm.sqadto;
#line 681 "esemco0r.pc"
                                sqlstm.sqptdso = sqlstm.sqtdso;
#line 681 "esemco0r.pc"
                                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 681 "esemco0r.pc"
                                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 681 "esemco0r.pc"
}

#line 697 "esemco0r.pc"

printf( "esemco0r 062f after break  tmp_mobile_cnt=%d o_mobile_cnt=%d \n\r" , tmp_mobile_cnt , o_mobile_cnt ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  062f after break  tmp_mobile_cnt=%d o_mobile_cnt=%d  \" >> /tmp/debug",  sys_date ,sys_time , tmp_mobile_cnt , o_mobile_cnt);	
system(msg);



                                tmp_mobile_cnt = tmp_mobile_cnt + o_mobile_cnt;
								
printf( "esemco0r 062f1 after break  tmp_mobile_cnt=%d o_mobile_cnt=%d \n\r" , tmp_mobile_cnt , o_mobile_cnt ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  062f1 after break  tmp_mobile_cnt=%d o_mobile_cnt=%d  \" >> /tmp/debug",  sys_date ,sys_time , tmp_mobile_cnt , o_mobile_cnt);	
system(msg);	
							
printf( "esemco0r 062f2 EXEC_SQL  %c|%s|%s-%s \n\r" ,o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  062f2 EXEC_SQL %c|%s|%s-%s  \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr);	
system(msg);				
                            }
                        }
						
			
printf( "esemco0r 063 after break  tmp_mobile_cnt=%d o_mobile_cnt=%d \n\r" , tmp_mobile_cnt , o_mobile_cnt ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  063 after break  tmp_mobile_cnt=%d o_mobile_cnt=%d  \" >> /tmp/debug",  sys_date ,sys_time , tmp_mobile_cnt , o_mobile_cnt);	
system(msg);
			
                        o_mobile_cnt = tmp_mobile_cnt;
						

printf( "esemco0r 063~ after break  tmp_mobile_cnt=%d o_mobile_cnt=%d \n\r" , tmp_mobile_cnt , o_mobile_cnt ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  063~ after break  tmp_mobile_cnt=%d o_mobile_cnt=%d  \" >> /tmp/debug",  sys_date ,sys_time , tmp_mobile_cnt , o_mobile_cnt);	
system(msg);


						
                    }
					
					
printf( "esemco0r 063~~ EXEC SQL  RX_ONLY  %c|%s|%s-%s \n\r" ,o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  063~~ EXEC SQL  RX_ONLY %c|%s|%s-%s  \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr);	
system(msg);					
					
/**/
                    break;

                case RX_ONLY:

/* Alex Yeung 1999-08-05
                    EXEC SQL
                         SELECT COUNT(UNIQUE MOBILE_NO)
*/
/* comment by Chen Yung
                    EXEC SQL
                         SELECT COUNT(DISTINCT MOBILE_NO)

                         INTO   :o_mobile_cnt
                         FROM   MOBILE_CH
                         WHERE  RX_FREQ between :o_tx_freq_lo and :o_tx_freq_hi
                         AND    TX_FREQ IS NULL
                         AND    SYS_CATEGORY = :o_sys_category
                         AND    SYS_TYPE = :o_sys_type
                         AND    SYS_NO = :o_sys_no
                         AND    SYS_SUFFIX = :o_sys_suffix
                         AND    CANCEL_DATE IS NULL;
*/

printf( "esemco0r 063a EXEC SQL RX_ONLY \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   063a EXEC SQL RX_ONLY \" >> /tmp/debug", sys_date ,sys_time);	
system(msg);	

	                /* EXEC SQL
                        SELECT COUNT(DISTINCT b.MOBILE_NO)
                        INTO   :o_mobile_cnt
                        FROM   MOBILE a, MOBILE_CH b
                        WHERE  b.RX_FREQ between :o_tx_freq_lo and :o_tx_freq_hi
                        AND    b.TX_FREQ IS NULL
                        AND    b.SYS_CATEGORY = :o_sys_category
                        AND    b.SYS_TYPE = :o_sys_type
                        AND    b.SYS_NO = :o_sys_no
                        AND    b.SYS_SUFFIX = :o_sys_suffix
                        AND    b.CANCEL_DATE IS NULL
                        AND    a.SYS_CATEGORY = b.SYS_CATEGORY
                        AND    a.SYS_TYPE = b.SYS_TYPE
                        AND    a.SYS_NO = b.SYS_NO
                        AND    a.SYS_SUFFIX = b.SYS_SUFFIX
                        AND    a.MOBILE_NO = b.MOBILE_NO
                        AND    a.CANCEL_DATE IS NULL; */ 
#line 752 "esemco0r.pc"

{
#line 736 "esemco0r.pc"
                 struct sqlexd sqlstm;
#line 736 "esemco0r.pc"
                 sqlstm.sqlvsn = 13;
#line 736 "esemco0r.pc"
                 sqlstm.arrsiz = 10;
#line 736 "esemco0r.pc"
                 sqlstm.sqladtp = &sqladt;
#line 736 "esemco0r.pc"
                 sqlstm.sqltdsp = &sqltds;
#line 736 "esemco0r.pc"
                 sqlstm.stmt = "select count(distinct b.MOBILE_NO) into :b0 \
 from MOBILE a ,MOBILE_CH b where ((((((((((((b.RX_FREQ between :b1 and :b2 an\
d b.TX_FREQ is null ) and b.SYS_CATEGORY=:b3) and b.SYS_TYPE=:b4) and b.SYS_NO\
=:b5) and b.SYS_SUFFIX=:b6) and b.CANCEL_DATE is null ) and a.SYS_CATEGORY=b.S\
YS_CATEGORY) and a.SYS_TYPE=b.SYS_TYPE) and a.SYS_NO=b.SYS_NO) and a.SYS_SUFFI\
X=b.SYS_SUFFIX) and a.MOBILE_NO=b.MOBILE_NO) and a.CANCEL_DATE is null )";
#line 736 "esemco0r.pc"
                 sqlstm.iters = (unsigned int  )1;
#line 736 "esemco0r.pc"
                 sqlstm.offset = (unsigned int  )932;
#line 736 "esemco0r.pc"
                 sqlstm.selerr = (unsigned short)1;
#line 736 "esemco0r.pc"
                 sqlstm.sqlpfmem = (unsigned int  )0;
#line 736 "esemco0r.pc"
                 sqlstm.cud = sqlcud0;
#line 736 "esemco0r.pc"
                 sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 736 "esemco0r.pc"
                 sqlstm.sqlety = (unsigned short)4352;
#line 736 "esemco0r.pc"
                 sqlstm.occurs = (unsigned int  )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqhstv[0] = (unsigned char  *)&o_mobile_cnt;
#line 736 "esemco0r.pc"
                 sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
#line 736 "esemco0r.pc"
                 sqlstm.sqhsts[0] = (         int  )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqindv[0] = (         short *)0;
#line 736 "esemco0r.pc"
                 sqlstm.sqinds[0] = (         int  )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqharm[0] = (unsigned long )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqadto[0] = (unsigned short )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqtdso[0] = (unsigned short )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqhstv[1] = (unsigned char  *)&o_tx_freq_lo;
#line 736 "esemco0r.pc"
                 sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
#line 736 "esemco0r.pc"
                 sqlstm.sqhsts[1] = (         int  )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqindv[1] = (         short *)0;
#line 736 "esemco0r.pc"
                 sqlstm.sqinds[1] = (         int  )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqharm[1] = (unsigned long )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqadto[1] = (unsigned short )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqtdso[1] = (unsigned short )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqhstv[2] = (unsigned char  *)&o_tx_freq_hi;
#line 736 "esemco0r.pc"
                 sqlstm.sqhstl[2] = (unsigned long )sizeof(double);
#line 736 "esemco0r.pc"
                 sqlstm.sqhsts[2] = (         int  )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqindv[2] = (         short *)0;
#line 736 "esemco0r.pc"
                 sqlstm.sqinds[2] = (         int  )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqharm[2] = (unsigned long )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqadto[2] = (unsigned short )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqtdso[2] = (unsigned short )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_category;
#line 736 "esemco0r.pc"
                 sqlstm.sqhstl[3] = (unsigned long )1;
#line 736 "esemco0r.pc"
                 sqlstm.sqhsts[3] = (         int  )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqindv[3] = (         short *)0;
#line 736 "esemco0r.pc"
                 sqlstm.sqinds[3] = (         int  )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqharm[3] = (unsigned long )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqadto[3] = (unsigned short )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqtdso[3] = (unsigned short )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqhstv[4] = (unsigned char  *)&o_sys_type;
#line 736 "esemco0r.pc"
                 sqlstm.sqhstl[4] = (unsigned long )5;
#line 736 "esemco0r.pc"
                 sqlstm.sqhsts[4] = (         int  )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqindv[4] = (         short *)0;
#line 736 "esemco0r.pc"
                 sqlstm.sqinds[4] = (         int  )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqharm[4] = (unsigned long )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqadto[4] = (unsigned short )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqtdso[4] = (unsigned short )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqhstv[5] = (unsigned char  *)&o_sys_no;
#line 736 "esemco0r.pc"
                 sqlstm.sqhstl[5] = (unsigned long )10;
#line 736 "esemco0r.pc"
                 sqlstm.sqhsts[5] = (         int  )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqindv[5] = (         short *)0;
#line 736 "esemco0r.pc"
                 sqlstm.sqinds[5] = (         int  )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqharm[5] = (unsigned long )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqadto[5] = (unsigned short )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqtdso[5] = (unsigned short )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqhstv[6] = (unsigned char  *)&o_sys_suffix;
#line 736 "esemco0r.pc"
                 sqlstm.sqhstl[6] = (unsigned long )6;
#line 736 "esemco0r.pc"
                 sqlstm.sqhsts[6] = (         int  )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqindv[6] = (         short *)0;
#line 736 "esemco0r.pc"
                 sqlstm.sqinds[6] = (         int  )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqharm[6] = (unsigned long )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqadto[6] = (unsigned short )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqtdso[6] = (unsigned short )0;
#line 736 "esemco0r.pc"
                 sqlstm.sqphsv = sqlstm.sqhstv;
#line 736 "esemco0r.pc"
                 sqlstm.sqphsl = sqlstm.sqhstl;
#line 736 "esemco0r.pc"
                 sqlstm.sqphss = sqlstm.sqhsts;
#line 736 "esemco0r.pc"
                 sqlstm.sqpind = sqlstm.sqindv;
#line 736 "esemco0r.pc"
                 sqlstm.sqpins = sqlstm.sqinds;
#line 736 "esemco0r.pc"
                 sqlstm.sqparm = sqlstm.sqharm;
#line 736 "esemco0r.pc"
                 sqlstm.sqparc = sqlstm.sqharc;
#line 736 "esemco0r.pc"
                 sqlstm.sqpadto = sqlstm.sqadto;
#line 736 "esemco0r.pc"
                 sqlstm.sqptdso = sqlstm.sqtdso;
#line 736 "esemco0r.pc"
                 sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 736 "esemco0r.pc"
                 if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 736 "esemco0r.pc"
}

#line 752 "esemco0r.pc"


printf( "esemco0r 063b EXEC SQL RX_ONLY \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   063b EXEC SQL RX_ONLY \" >> /tmp/debug", sys_date ,sys_time);	
system(msg);						
						
/* added by Chen Yung for handling merged systems and ZZx systems */

printf( "esemco0r 063c EXEC SQL RX_ONLY \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   063c EXEC SQL RX_ONLY \" >> /tmp/debug", sys_date ,sys_time);	
system(msg);

                    /* EXEC SQL
                        SELECT  DISTINCT SYS_ID
                        INTO    :o_sys_id
                        FROM    ESMS_MAP
                        WHERE   SYS_CATEGORY = :o_sys_category
                        AND     SYS_TYPE = :o_sys_type
                        AND     SYS_NO = :o_sys_no
                        AND     SYS_SUFFIX = :o_sys_suffix
                        AND     SYS_LINK = 'L'; */ 
#line 772 "esemco0r.pc"

{
#line 764 "esemco0r.pc"
                    struct sqlexd sqlstm;
#line 764 "esemco0r.pc"
                    sqlstm.sqlvsn = 13;
#line 764 "esemco0r.pc"
                    sqlstm.arrsiz = 10;
#line 764 "esemco0r.pc"
                    sqlstm.sqladtp = &sqladt;
#line 764 "esemco0r.pc"
                    sqlstm.sqltdsp = &sqltds;
#line 764 "esemco0r.pc"
                    sqlstm.stmt = "select distinct SYS_ID into :b0  from ESM\
S_MAP where ((((SYS_CATEGORY=:b1 and SYS_TYPE=:b2) and SYS_NO=:b3) and SYS_SUF\
FIX=:b4) and SYS_LINK='L')";
#line 764 "esemco0r.pc"
                    sqlstm.iters = (unsigned int  )1;
#line 764 "esemco0r.pc"
                    sqlstm.offset = (unsigned int  )975;
#line 764 "esemco0r.pc"
                    sqlstm.selerr = (unsigned short)1;
#line 764 "esemco0r.pc"
                    sqlstm.sqlpfmem = (unsigned int  )0;
#line 764 "esemco0r.pc"
                    sqlstm.cud = sqlcud0;
#line 764 "esemco0r.pc"
                    sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 764 "esemco0r.pc"
                    sqlstm.sqlety = (unsigned short)4352;
#line 764 "esemco0r.pc"
                    sqlstm.occurs = (unsigned int  )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_id;
#line 764 "esemco0r.pc"
                    sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
#line 764 "esemco0r.pc"
                    sqlstm.sqhsts[0] = (         int  )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqindv[0] = (         short *)0;
#line 764 "esemco0r.pc"
                    sqlstm.sqinds[0] = (         int  )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqharm[0] = (unsigned long )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqadto[0] = (unsigned short )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqtdso[0] = (unsigned short )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_category;
#line 764 "esemco0r.pc"
                    sqlstm.sqhstl[1] = (unsigned long )1;
#line 764 "esemco0r.pc"
                    sqlstm.sqhsts[1] = (         int  )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqindv[1] = (         short *)0;
#line 764 "esemco0r.pc"
                    sqlstm.sqinds[1] = (         int  )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqharm[1] = (unsigned long )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqadto[1] = (unsigned short )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqtdso[1] = (unsigned short )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_type;
#line 764 "esemco0r.pc"
                    sqlstm.sqhstl[2] = (unsigned long )5;
#line 764 "esemco0r.pc"
                    sqlstm.sqhsts[2] = (         int  )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqindv[2] = (         short *)0;
#line 764 "esemco0r.pc"
                    sqlstm.sqinds[2] = (         int  )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqharm[2] = (unsigned long )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqadto[2] = (unsigned short )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqtdso[2] = (unsigned short )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_no;
#line 764 "esemco0r.pc"
                    sqlstm.sqhstl[3] = (unsigned long )10;
#line 764 "esemco0r.pc"
                    sqlstm.sqhsts[3] = (         int  )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqindv[3] = (         short *)0;
#line 764 "esemco0r.pc"
                    sqlstm.sqinds[3] = (         int  )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqharm[3] = (unsigned long )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqadto[3] = (unsigned short )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqtdso[3] = (unsigned short )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqhstv[4] = (unsigned char  *)&o_sys_suffix;
#line 764 "esemco0r.pc"
                    sqlstm.sqhstl[4] = (unsigned long )6;
#line 764 "esemco0r.pc"
                    sqlstm.sqhsts[4] = (         int  )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqindv[4] = (         short *)0;
#line 764 "esemco0r.pc"
                    sqlstm.sqinds[4] = (         int  )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqharm[4] = (unsigned long )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqadto[4] = (unsigned short )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqtdso[4] = (unsigned short )0;
#line 764 "esemco0r.pc"
                    sqlstm.sqphsv = sqlstm.sqhstv;
#line 764 "esemco0r.pc"
                    sqlstm.sqphsl = sqlstm.sqhstl;
#line 764 "esemco0r.pc"
                    sqlstm.sqphss = sqlstm.sqhsts;
#line 764 "esemco0r.pc"
                    sqlstm.sqpind = sqlstm.sqindv;
#line 764 "esemco0r.pc"
                    sqlstm.sqpins = sqlstm.sqinds;
#line 764 "esemco0r.pc"
                    sqlstm.sqparm = sqlstm.sqharm;
#line 764 "esemco0r.pc"
                    sqlstm.sqparc = sqlstm.sqharc;
#line 764 "esemco0r.pc"
                    sqlstm.sqpadto = sqlstm.sqadto;
#line 764 "esemco0r.pc"
                    sqlstm.sqptdso = sqlstm.sqtdso;
#line 764 "esemco0r.pc"
                    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 764 "esemco0r.pc"
                    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 764 "esemco0r.pc"
}

#line 772 "esemco0r.pc"

                        
printf( "esemco0r 063d EXEC SQL RX_ONLY \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   063d EXEC SQL RX_ONLY \" >> /tmp/debug", sys_date ,sys_time);	
system(msg);
						
                    if (sqlca.sqlcode != NOT_FOUND) 
                    {
                        /* EXEC SQL DECLARE CO06 CURSOR FOR
                            SELECT    DISTINCT SYS_SUFFIX
                            FROM      ESMS_MAP
                            WHERE     SYS_ID = :o_sys_id
                            AND       SYS_SUFFIX <> :o_sys_suffix
                            ORDER BY  SYS_SUFFIX; */ 
#line 785 "esemco0r.pc"


                        /* EXEC SQL OPEN CO06; */ 
#line 787 "esemco0r.pc"

{
#line 787 "esemco0r.pc"
                        struct sqlexd sqlstm;
#line 787 "esemco0r.pc"
                        sqlstm.sqlvsn = 13;
#line 787 "esemco0r.pc"
                        sqlstm.arrsiz = 10;
#line 787 "esemco0r.pc"
                        sqlstm.sqladtp = &sqladt;
#line 787 "esemco0r.pc"
                        sqlstm.sqltdsp = &sqltds;
#line 787 "esemco0r.pc"
                        sqlstm.stmt = sq0015;
#line 787 "esemco0r.pc"
                        sqlstm.iters = (unsigned int  )1;
#line 787 "esemco0r.pc"
                        sqlstm.offset = (unsigned int  )1010;
#line 787 "esemco0r.pc"
                        sqlstm.selerr = (unsigned short)1;
#line 787 "esemco0r.pc"
                        sqlstm.sqlpfmem = (unsigned int  )0;
#line 787 "esemco0r.pc"
                        sqlstm.cud = sqlcud0;
#line 787 "esemco0r.pc"
                        sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 787 "esemco0r.pc"
                        sqlstm.sqlety = (unsigned short)4352;
#line 787 "esemco0r.pc"
                        sqlstm.occurs = (unsigned int  )0;
#line 787 "esemco0r.pc"
                        sqlstm.sqcmod = (unsigned int )0;
#line 787 "esemco0r.pc"
                        sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_id;
#line 787 "esemco0r.pc"
                        sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
#line 787 "esemco0r.pc"
                        sqlstm.sqhsts[0] = (         int  )0;
#line 787 "esemco0r.pc"
                        sqlstm.sqindv[0] = (         short *)0;
#line 787 "esemco0r.pc"
                        sqlstm.sqinds[0] = (         int  )0;
#line 787 "esemco0r.pc"
                        sqlstm.sqharm[0] = (unsigned long )0;
#line 787 "esemco0r.pc"
                        sqlstm.sqadto[0] = (unsigned short )0;
#line 787 "esemco0r.pc"
                        sqlstm.sqtdso[0] = (unsigned short )0;
#line 787 "esemco0r.pc"
                        sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_suffix;
#line 787 "esemco0r.pc"
                        sqlstm.sqhstl[1] = (unsigned long )6;
#line 787 "esemco0r.pc"
                        sqlstm.sqhsts[1] = (         int  )0;
#line 787 "esemco0r.pc"
                        sqlstm.sqindv[1] = (         short *)0;
#line 787 "esemco0r.pc"
                        sqlstm.sqinds[1] = (         int  )0;
#line 787 "esemco0r.pc"
                        sqlstm.sqharm[1] = (unsigned long )0;
#line 787 "esemco0r.pc"
                        sqlstm.sqadto[1] = (unsigned short )0;
#line 787 "esemco0r.pc"
                        sqlstm.sqtdso[1] = (unsigned short )0;
#line 787 "esemco0r.pc"
                        sqlstm.sqphsv = sqlstm.sqhstv;
#line 787 "esemco0r.pc"
                        sqlstm.sqphsl = sqlstm.sqhstl;
#line 787 "esemco0r.pc"
                        sqlstm.sqphss = sqlstm.sqhsts;
#line 787 "esemco0r.pc"
                        sqlstm.sqpind = sqlstm.sqindv;
#line 787 "esemco0r.pc"
                        sqlstm.sqpins = sqlstm.sqinds;
#line 787 "esemco0r.pc"
                        sqlstm.sqparm = sqlstm.sqharm;
#line 787 "esemco0r.pc"
                        sqlstm.sqparc = sqlstm.sqharc;
#line 787 "esemco0r.pc"
                        sqlstm.sqpadto = sqlstm.sqadto;
#line 787 "esemco0r.pc"
                        sqlstm.sqptdso = sqlstm.sqtdso;
#line 787 "esemco0r.pc"
                        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 787 "esemco0r.pc"
                        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 787 "esemco0r.pc"
}

#line 787 "esemco0r.pc"

                        tmp_mobile_cnt = o_mobile_cnt;
                        for ( ; ; )
                        {
                            /* EXEC SQL
                                 FETCH CO06
                                 INTO  :o_sys_suffix_2; */ 
#line 793 "esemco0r.pc"

{
#line 791 "esemco0r.pc"
                            struct sqlexd sqlstm;
#line 791 "esemco0r.pc"
                            sqlstm.sqlvsn = 13;
#line 791 "esemco0r.pc"
                            sqlstm.arrsiz = 10;
#line 791 "esemco0r.pc"
                            sqlstm.sqladtp = &sqladt;
#line 791 "esemco0r.pc"
                            sqlstm.sqltdsp = &sqltds;
#line 791 "esemco0r.pc"
                            sqlstm.iters = (unsigned int  )1;
#line 791 "esemco0r.pc"
                            sqlstm.offset = (unsigned int  )1033;
#line 791 "esemco0r.pc"
                            sqlstm.selerr = (unsigned short)1;
#line 791 "esemco0r.pc"
                            sqlstm.sqlpfmem = (unsigned int  )0;
#line 791 "esemco0r.pc"
                            sqlstm.cud = sqlcud0;
#line 791 "esemco0r.pc"
                            sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 791 "esemco0r.pc"
                            sqlstm.sqlety = (unsigned short)4352;
#line 791 "esemco0r.pc"
                            sqlstm.occurs = (unsigned int  )0;
#line 791 "esemco0r.pc"
                            sqlstm.sqfoff = (         int )0;
#line 791 "esemco0r.pc"
                            sqlstm.sqfmod = (unsigned int )2;
#line 791 "esemco0r.pc"
                            sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_suffix_2;
#line 791 "esemco0r.pc"
                            sqlstm.sqhstl[0] = (unsigned long )6;
#line 791 "esemco0r.pc"
                            sqlstm.sqhsts[0] = (         int  )0;
#line 791 "esemco0r.pc"
                            sqlstm.sqindv[0] = (         short *)0;
#line 791 "esemco0r.pc"
                            sqlstm.sqinds[0] = (         int  )0;
#line 791 "esemco0r.pc"
                            sqlstm.sqharm[0] = (unsigned long )0;
#line 791 "esemco0r.pc"
                            sqlstm.sqadto[0] = (unsigned short )0;
#line 791 "esemco0r.pc"
                            sqlstm.sqtdso[0] = (unsigned short )0;
#line 791 "esemco0r.pc"
                            sqlstm.sqphsv = sqlstm.sqhstv;
#line 791 "esemco0r.pc"
                            sqlstm.sqphsl = sqlstm.sqhstl;
#line 791 "esemco0r.pc"
                            sqlstm.sqphss = sqlstm.sqhsts;
#line 791 "esemco0r.pc"
                            sqlstm.sqpind = sqlstm.sqindv;
#line 791 "esemco0r.pc"
                            sqlstm.sqpins = sqlstm.sqinds;
#line 791 "esemco0r.pc"
                            sqlstm.sqparm = sqlstm.sqharm;
#line 791 "esemco0r.pc"
                            sqlstm.sqparc = sqlstm.sqharc;
#line 791 "esemco0r.pc"
                            sqlstm.sqpadto = sqlstm.sqadto;
#line 791 "esemco0r.pc"
                            sqlstm.sqptdso = sqlstm.sqtdso;
#line 791 "esemco0r.pc"
                            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 791 "esemco0r.pc"
                            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 791 "esemco0r.pc"
}

#line 793 "esemco0r.pc"

                            if (sqlca.sqlcode == NOT_FOUND) 
                            {
                                /* EXEC SQL CLOSE CO06; */ 
#line 796 "esemco0r.pc"

{
#line 796 "esemco0r.pc"
                                struct sqlexd sqlstm;
#line 796 "esemco0r.pc"
                                sqlstm.sqlvsn = 13;
#line 796 "esemco0r.pc"
                                sqlstm.arrsiz = 10;
#line 796 "esemco0r.pc"
                                sqlstm.sqladtp = &sqladt;
#line 796 "esemco0r.pc"
                                sqlstm.sqltdsp = &sqltds;
#line 796 "esemco0r.pc"
                                sqlstm.iters = (unsigned int  )1;
#line 796 "esemco0r.pc"
                                sqlstm.offset = (unsigned int  )1052;
#line 796 "esemco0r.pc"
                                sqlstm.cud = sqlcud0;
#line 796 "esemco0r.pc"
                                sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 796 "esemco0r.pc"
                                sqlstm.sqlety = (unsigned short)4352;
#line 796 "esemco0r.pc"
                                sqlstm.occurs = (unsigned int  )0;
#line 796 "esemco0r.pc"
                                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 796 "esemco0r.pc"
                                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 796 "esemco0r.pc"
}

#line 796 "esemco0r.pc"

printf( "esemco0r 063d2 EXEC SQL  RX_ONLY  %c|%s|%s-%s \n\r" ,o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  063d2 EXEC SQL  RX_ONLY %c|%s|%s-%s  \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr);	
system(msg);

                                break;
                            }
                            else 
                            {
								
printf( "esemco0r 063e EXEC SQL RX_ONLY \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   063e EXEC SQL RX_ONLY \" >> /tmp/debug", sys_date ,sys_time);	
system(msg);
								
	                            /* EXEC SQL
                                    SELECT COUNT(DISTINCT b.MOBILE_NO)
                                    INTO   :o_mobile_cnt
                                    FROM   MOBILE a, MOBILE_CH b
                                    WHERE  b.RX_FREQ between :o_tx_freq_lo and :o_tx_freq_hi
                                    AND    b.TX_FREQ IS NULL
                                    AND    b.SYS_CATEGORY = :o_sys_category
                                    AND    b.SYS_TYPE = :o_sys_type
                                    AND    b.SYS_NO = :o_sys_no
                                    AND    b.SYS_SUFFIX = :o_sys_suffix_2
                                    AND    b.CANCEL_DATE IS NULL
                                    AND    a.SYS_CATEGORY = b.SYS_CATEGORY
                                    AND    a.SYS_TYPE = b.SYS_TYPE
                                    AND    a.SYS_NO = b.SYS_NO
                                    AND    a.SYS_SUFFIX = b.SYS_SUFFIX
                                    AND    a.MOBILE_NO = b.MOBILE_NO
                                    AND    a.CANCEL_DATE IS NULL; */ 
#line 822 "esemco0r.pc"

{
#line 806 "esemco0r.pc"
                             struct sqlexd sqlstm;
#line 806 "esemco0r.pc"
                             sqlstm.sqlvsn = 13;
#line 806 "esemco0r.pc"
                             sqlstm.arrsiz = 10;
#line 806 "esemco0r.pc"
                             sqlstm.sqladtp = &sqladt;
#line 806 "esemco0r.pc"
                             sqlstm.sqltdsp = &sqltds;
#line 806 "esemco0r.pc"
                             sqlstm.stmt = "select count(distinct b.MOBILE_N\
O) into :b0  from MOBILE a ,MOBILE_CH b where ((((((((((((b.RX_FREQ between :b\
1 and :b2 and b.TX_FREQ is null ) and b.SYS_CATEGORY=:b3) and b.SYS_TYPE=:b4) \
and b.SYS_NO=:b5) and b.SYS_SUFFIX=:b6) and b.CANCEL_DATE is null ) and a.SYS_\
CATEGORY=b.SYS_CATEGORY) and a.SYS_TYPE=b.SYS_TYPE) and a.SYS_NO=b.SYS_NO) and\
 a.SYS_SUFFIX=b.SYS_SUFFIX) and a.MOBILE_NO=b.MOBILE_NO) and a.CANCEL_DATE is \
null )";
#line 806 "esemco0r.pc"
                             sqlstm.iters = (unsigned int  )1;
#line 806 "esemco0r.pc"
                             sqlstm.offset = (unsigned int  )1067;
#line 806 "esemco0r.pc"
                             sqlstm.selerr = (unsigned short)1;
#line 806 "esemco0r.pc"
                             sqlstm.sqlpfmem = (unsigned int  )0;
#line 806 "esemco0r.pc"
                             sqlstm.cud = sqlcud0;
#line 806 "esemco0r.pc"
                             sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 806 "esemco0r.pc"
                             sqlstm.sqlety = (unsigned short)4352;
#line 806 "esemco0r.pc"
                             sqlstm.occurs = (unsigned int  )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqhstv[0] = (unsigned char  *)&o_mobile_cnt;
#line 806 "esemco0r.pc"
                             sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
#line 806 "esemco0r.pc"
                             sqlstm.sqhsts[0] = (         int  )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqindv[0] = (         short *)0;
#line 806 "esemco0r.pc"
                             sqlstm.sqinds[0] = (         int  )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqharm[0] = (unsigned long )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqadto[0] = (unsigned short )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqtdso[0] = (unsigned short )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqhstv[1] = (unsigned char  *)&o_tx_freq_lo;
#line 806 "esemco0r.pc"
                             sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
#line 806 "esemco0r.pc"
                             sqlstm.sqhsts[1] = (         int  )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqindv[1] = (         short *)0;
#line 806 "esemco0r.pc"
                             sqlstm.sqinds[1] = (         int  )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqharm[1] = (unsigned long )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqadto[1] = (unsigned short )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqtdso[1] = (unsigned short )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqhstv[2] = (unsigned char  *)&o_tx_freq_hi;
#line 806 "esemco0r.pc"
                             sqlstm.sqhstl[2] = (unsigned long )sizeof(double);
#line 806 "esemco0r.pc"
                             sqlstm.sqhsts[2] = (         int  )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqindv[2] = (         short *)0;
#line 806 "esemco0r.pc"
                             sqlstm.sqinds[2] = (         int  )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqharm[2] = (unsigned long )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqadto[2] = (unsigned short )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqtdso[2] = (unsigned short )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_category;
#line 806 "esemco0r.pc"
                             sqlstm.sqhstl[3] = (unsigned long )1;
#line 806 "esemco0r.pc"
                             sqlstm.sqhsts[3] = (         int  )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqindv[3] = (         short *)0;
#line 806 "esemco0r.pc"
                             sqlstm.sqinds[3] = (         int  )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqharm[3] = (unsigned long )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqadto[3] = (unsigned short )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqtdso[3] = (unsigned short )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqhstv[4] = (unsigned char  *)&o_sys_type;
#line 806 "esemco0r.pc"
                             sqlstm.sqhstl[4] = (unsigned long )5;
#line 806 "esemco0r.pc"
                             sqlstm.sqhsts[4] = (         int  )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqindv[4] = (         short *)0;
#line 806 "esemco0r.pc"
                             sqlstm.sqinds[4] = (         int  )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqharm[4] = (unsigned long )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqadto[4] = (unsigned short )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqtdso[4] = (unsigned short )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqhstv[5] = (unsigned char  *)&o_sys_no;
#line 806 "esemco0r.pc"
                             sqlstm.sqhstl[5] = (unsigned long )10;
#line 806 "esemco0r.pc"
                             sqlstm.sqhsts[5] = (         int  )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqindv[5] = (         short *)0;
#line 806 "esemco0r.pc"
                             sqlstm.sqinds[5] = (         int  )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqharm[5] = (unsigned long )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqadto[5] = (unsigned short )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqtdso[5] = (unsigned short )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqhstv[6] = (unsigned char  *)&o_sys_suffix_2;
#line 806 "esemco0r.pc"
                             sqlstm.sqhstl[6] = (unsigned long )6;
#line 806 "esemco0r.pc"
                             sqlstm.sqhsts[6] = (         int  )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqindv[6] = (         short *)0;
#line 806 "esemco0r.pc"
                             sqlstm.sqinds[6] = (         int  )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqharm[6] = (unsigned long )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqadto[6] = (unsigned short )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqtdso[6] = (unsigned short )0;
#line 806 "esemco0r.pc"
                             sqlstm.sqphsv = sqlstm.sqhstv;
#line 806 "esemco0r.pc"
                             sqlstm.sqphsl = sqlstm.sqhstl;
#line 806 "esemco0r.pc"
                             sqlstm.sqphss = sqlstm.sqhsts;
#line 806 "esemco0r.pc"
                             sqlstm.sqpind = sqlstm.sqindv;
#line 806 "esemco0r.pc"
                             sqlstm.sqpins = sqlstm.sqinds;
#line 806 "esemco0r.pc"
                             sqlstm.sqparm = sqlstm.sqharm;
#line 806 "esemco0r.pc"
                             sqlstm.sqparc = sqlstm.sqharc;
#line 806 "esemco0r.pc"
                             sqlstm.sqpadto = sqlstm.sqadto;
#line 806 "esemco0r.pc"
                             sqlstm.sqptdso = sqlstm.sqtdso;
#line 806 "esemco0r.pc"
                             sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 806 "esemco0r.pc"
                             if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 806 "esemco0r.pc"
}

#line 822 "esemco0r.pc"





printf( "esemco0r 063f1 after break  tmp_mobile_cnt=%d o_mobile_cnt=%d \n\r" , tmp_mobile_cnt , o_mobile_cnt ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  063f1 after break  tmp_mobile_cnt=%d o_mobile_cnt=%d  \" >> /tmp/debug",  sys_date ,sys_time , tmp_mobile_cnt , o_mobile_cnt);	
system(msg);


                                tmp_mobile_cnt = tmp_mobile_cnt + o_mobile_cnt;
					
printf( "esemco0r 063f2 after break  tmp_mobile_cnt=%d o_mobile_cnt=%d \n\r" , tmp_mobile_cnt , o_mobile_cnt ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  063f2 after break  tmp_mobile_cnt=%d o_mobile_cnt=%d  \" >> /tmp/debug",  sys_date ,sys_time , tmp_mobile_cnt , o_mobile_cnt);	
system(msg);
					
				
printf( "esemco0r 063f3 EXEC SQL  RX_ONLY  %c|%s|%s-%s \n\r" ,o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  063f3 EXEC SQL  RX_ONLY %c|%s|%s-%s  \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr);	
system(msg);								
								
					
                            }
							
printf( "esemco0r 064 EXEC SQL  RX_ONLY  %c|%s|%s-%s \n\r" ,o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  064 EXEC SQL  RX_ONLY %c|%s|%s-%s  \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr);	
system(msg);	
							
                        }
						
						
						
						
						
printf( "esemco0r 065a after break  tmp_mobile_cnt=%d o_mobile_cnt=%d \n\r" , tmp_mobile_cnt , o_mobile_cnt ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  065a after break  tmp_mobile_cnt=%d o_mobile_cnt=%d  \" >> /tmp/debug",  sys_date ,sys_time , tmp_mobile_cnt , o_mobile_cnt);	
system(msg);						
                        o_mobile_cnt = tmp_mobile_cnt;
						
printf( "esemco0r 065b after break  tmp_mobile_cnt=%d o_mobile_cnt=%d \n\r" , tmp_mobile_cnt , o_mobile_cnt ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  065b after break  tmp_mobile_cnt=%d o_mobile_cnt=%d  \" >> /tmp/debug",  sys_date ,sys_time , tmp_mobile_cnt , o_mobile_cnt);	
system(msg);					

printf( "esemco0r 065c EXEC SQL  RX_ONLY  %c|%s|%s-%s \n\r" ,o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  065c EXEC SQL  RX_ONLY %c|%s|%s-%s  \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr);	
system(msg);	

	
                    }
/**/

printf( "esemco0r 066 EXEC SQL  RX_ONLY  %c|%s|%s-%s \n\r" ,o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  066 EXEC SQL  RX_ONLY %c|%s|%s-%s  \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr);	
system(msg);	

                    break;
            }
    
	
printf( "esemco0r 067a after break  tot_mobile_cnt=%d o_mobile_cnt=%d \n\r" , tot_mobile_cnt , o_mobile_cnt ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  067a after break  tot_mobile_cnt=%d o_mobile_cnt=%d  \" >> /tmp/debug",  sys_date ,sys_time , tot_mobile_cnt , o_mobile_cnt);	
system(msg);		 	
            tot_mobile_cnt += o_mobile_cnt;

printf( "esemco0r 067b after break  tot_mobile_cnt=%d o_mobile_cnt=%d \n\r" , tot_mobile_cnt , o_mobile_cnt ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  067b after break  tot_mobile_cnt=%d o_mobile_cnt=%d  \" >> /tmp/debug",  sys_date ,sys_time , tot_mobile_cnt , o_mobile_cnt);	
system(msg);					

printf( "esemco0r 067c EXEC SQL  RX_ONLY  %c|%s|%s-%s \n\r" ,o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  067c EXEC SQL  RX_ONLY %c|%s|%s-%s  \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr);	
system(msg);	
			
			
			
			
			
			
			
			
			
/*
system("echo \"esemco0r 2\" >> /tmp/debug");
*/

/*
sprintf(msg, "echo \"%c %s %s %s %d\" >> /tmp/debug",
o_sys_category, o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr );
system(msg);
*/

printf( "esemco0r 068 EXEC SQL RX_ONLY \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   068 EXEC SQL RX_ONLY \" >> /tmp/debug", sys_date ,sys_time);	
system(msg);






            /* EXEC SQL
                 SELECT E_NAME, BUSS_CODE
                 INTO   :o_client_name, :o_buss_code
                 FROM   SYSTEM
                 WHERE  SYS_CATEGORY = :o_sys_category
                 AND    SYS_TYPE = :o_sys_type
                 AND    SYS_NO = :o_sys_no
                 AND    SYS_SUFFIX = :o_sys_suffix; */ 
#line 860 "esemco0r.pc"

{
#line 853 "esemco0r.pc"
            struct sqlexd sqlstm;
#line 853 "esemco0r.pc"
            sqlstm.sqlvsn = 13;
#line 853 "esemco0r.pc"
            sqlstm.arrsiz = 10;
#line 853 "esemco0r.pc"
            sqlstm.sqladtp = &sqladt;
#line 853 "esemco0r.pc"
            sqlstm.sqltdsp = &sqltds;
#line 853 "esemco0r.pc"
            sqlstm.stmt = "select E_NAME ,BUSS_CODE into :b0,:b1  from SYSTE\
M where (((SYS_CATEGORY=:b2 and SYS_TYPE=:b3) and SYS_NO=:b4) and SYS_SUFFIX=:\
b5)";
#line 853 "esemco0r.pc"
            sqlstm.iters = (unsigned int  )1;
#line 853 "esemco0r.pc"
            sqlstm.offset = (unsigned int  )1110;
#line 853 "esemco0r.pc"
            sqlstm.selerr = (unsigned short)1;
#line 853 "esemco0r.pc"
            sqlstm.sqlpfmem = (unsigned int  )0;
#line 853 "esemco0r.pc"
            sqlstm.cud = sqlcud0;
#line 853 "esemco0r.pc"
            sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 853 "esemco0r.pc"
            sqlstm.sqlety = (unsigned short)4352;
#line 853 "esemco0r.pc"
            sqlstm.occurs = (unsigned int  )0;
#line 853 "esemco0r.pc"
            sqlstm.sqhstv[0] = (unsigned char  *)&o_client_name;
#line 853 "esemco0r.pc"
            sqlstm.sqhstl[0] = (unsigned long )51;
#line 853 "esemco0r.pc"
            sqlstm.sqhsts[0] = (         int  )0;
#line 853 "esemco0r.pc"
            sqlstm.sqindv[0] = (         short *)0;
#line 853 "esemco0r.pc"
            sqlstm.sqinds[0] = (         int  )0;
#line 853 "esemco0r.pc"
            sqlstm.sqharm[0] = (unsigned long )0;
#line 853 "esemco0r.pc"
            sqlstm.sqadto[0] = (unsigned short )0;
#line 853 "esemco0r.pc"
            sqlstm.sqtdso[0] = (unsigned short )0;
#line 853 "esemco0r.pc"
            sqlstm.sqhstv[1] = (unsigned char  *)&o_buss_code;
#line 853 "esemco0r.pc"
            sqlstm.sqhstl[1] = (unsigned long )7;
#line 853 "esemco0r.pc"
            sqlstm.sqhsts[1] = (         int  )0;
#line 853 "esemco0r.pc"
            sqlstm.sqindv[1] = (         short *)0;
#line 853 "esemco0r.pc"
            sqlstm.sqinds[1] = (         int  )0;
#line 853 "esemco0r.pc"
            sqlstm.sqharm[1] = (unsigned long )0;
#line 853 "esemco0r.pc"
            sqlstm.sqadto[1] = (unsigned short )0;
#line 853 "esemco0r.pc"
            sqlstm.sqtdso[1] = (unsigned short )0;
#line 853 "esemco0r.pc"
            sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_category;
#line 853 "esemco0r.pc"
            sqlstm.sqhstl[2] = (unsigned long )1;
#line 853 "esemco0r.pc"
            sqlstm.sqhsts[2] = (         int  )0;
#line 853 "esemco0r.pc"
            sqlstm.sqindv[2] = (         short *)0;
#line 853 "esemco0r.pc"
            sqlstm.sqinds[2] = (         int  )0;
#line 853 "esemco0r.pc"
            sqlstm.sqharm[2] = (unsigned long )0;
#line 853 "esemco0r.pc"
            sqlstm.sqadto[2] = (unsigned short )0;
#line 853 "esemco0r.pc"
            sqlstm.sqtdso[2] = (unsigned short )0;
#line 853 "esemco0r.pc"
            sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_type;
#line 853 "esemco0r.pc"
            sqlstm.sqhstl[3] = (unsigned long )5;
#line 853 "esemco0r.pc"
            sqlstm.sqhsts[3] = (         int  )0;
#line 853 "esemco0r.pc"
            sqlstm.sqindv[3] = (         short *)0;
#line 853 "esemco0r.pc"
            sqlstm.sqinds[3] = (         int  )0;
#line 853 "esemco0r.pc"
            sqlstm.sqharm[3] = (unsigned long )0;
#line 853 "esemco0r.pc"
            sqlstm.sqadto[3] = (unsigned short )0;
#line 853 "esemco0r.pc"
            sqlstm.sqtdso[3] = (unsigned short )0;
#line 853 "esemco0r.pc"
            sqlstm.sqhstv[4] = (unsigned char  *)&o_sys_no;
#line 853 "esemco0r.pc"
            sqlstm.sqhstl[4] = (unsigned long )10;
#line 853 "esemco0r.pc"
            sqlstm.sqhsts[4] = (         int  )0;
#line 853 "esemco0r.pc"
            sqlstm.sqindv[4] = (         short *)0;
#line 853 "esemco0r.pc"
            sqlstm.sqinds[4] = (         int  )0;
#line 853 "esemco0r.pc"
            sqlstm.sqharm[4] = (unsigned long )0;
#line 853 "esemco0r.pc"
            sqlstm.sqadto[4] = (unsigned short )0;
#line 853 "esemco0r.pc"
            sqlstm.sqtdso[4] = (unsigned short )0;
#line 853 "esemco0r.pc"
            sqlstm.sqhstv[5] = (unsigned char  *)&o_sys_suffix;
#line 853 "esemco0r.pc"
            sqlstm.sqhstl[5] = (unsigned long )6;
#line 853 "esemco0r.pc"
            sqlstm.sqhsts[5] = (         int  )0;
#line 853 "esemco0r.pc"
            sqlstm.sqindv[5] = (         short *)0;
#line 853 "esemco0r.pc"
            sqlstm.sqinds[5] = (         int  )0;
#line 853 "esemco0r.pc"
            sqlstm.sqharm[5] = (unsigned long )0;
#line 853 "esemco0r.pc"
            sqlstm.sqadto[5] = (unsigned short )0;
#line 853 "esemco0r.pc"
            sqlstm.sqtdso[5] = (unsigned short )0;
#line 853 "esemco0r.pc"
            sqlstm.sqphsv = sqlstm.sqhstv;
#line 853 "esemco0r.pc"
            sqlstm.sqphsl = sqlstm.sqhstl;
#line 853 "esemco0r.pc"
            sqlstm.sqphss = sqlstm.sqhsts;
#line 853 "esemco0r.pc"
            sqlstm.sqpind = sqlstm.sqindv;
#line 853 "esemco0r.pc"
            sqlstm.sqpins = sqlstm.sqinds;
#line 853 "esemco0r.pc"
            sqlstm.sqparm = sqlstm.sqharm;
#line 853 "esemco0r.pc"
            sqlstm.sqparc = sqlstm.sqharc;
#line 853 "esemco0r.pc"
            sqlstm.sqpadto = sqlstm.sqadto;
#line 853 "esemco0r.pc"
            sqlstm.sqptdso = sqlstm.sqtdso;
#line 853 "esemco0r.pc"
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 853 "esemco0r.pc"
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 853 "esemco0r.pc"
}

#line 860 "esemco0r.pc"




printf( "esemco0r 068b EXEC SQL  RX_ONLY  %c|%s|%s-%s \n\r" ,o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  068b EXEC SQL  RX_ONLY %c|%s|%s-%s  \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr);	
system(msg);	
			
			
				 
            if (sqlca.sqlcode == NOT_FOUND)
            {
				
	printf( "esemco0r 068c NOT_FOUND  %c|%s|%s-%s \n\r" ,o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  068c  NOT_FOUND %c|%s|%s-%s  \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr);	
system(msg);			

				
                fprintf(afp, "SYSTEM record not found for %c-%s-%s-%s\n",
                        o_sys_category, o_sys_type.arr, o_sys_no.arr, 
                        o_sys_suffix.arr);
                continue;
            }

            o_client_name.arr[o_client_name.len] = '\0';
            o_buss_code.arr[o_buss_code.len] = '\0';
/*
printf("client buss_code: %s %s\n", o_client_name.arr, o_buss_code.arr);
fflush(stdout);
*/

/*
system("echo \"esemco0r 3\" >> /tmp/debug");
*/

/*
sprintf(msg, "echo \"%s\" >> /tmp/debug", o_buss_code.arr);
system(msg);
*/

printf( "esemco0r 069 EXEC SQL RX_ONLY \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   069 EXEC SQL RX_ONLY \" >> /tmp/debug", sys_date ,sys_time);	
system(msg);


printf( "esemco0r 069b EXEC SQL  RX_ONLY  %c|%s|%s-%s \n\r" ,o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  069b EXEC SQL  RX_ONLY %c|%s|%s-%s  \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr);	
system(msg);

            /* EXEC SQL
                 SELECT BUSS_E_DESC
                 INTO   :o_buss_desc
                 FROM   BUSINESS
                 WHERE  BUSS_CODE = :o_buss_code; */ 
#line 898 "esemco0r.pc"

{
#line 894 "esemco0r.pc"
            struct sqlexd sqlstm;
#line 894 "esemco0r.pc"
            sqlstm.sqlvsn = 13;
#line 894 "esemco0r.pc"
            sqlstm.arrsiz = 10;
#line 894 "esemco0r.pc"
            sqlstm.sqladtp = &sqladt;
#line 894 "esemco0r.pc"
            sqlstm.sqltdsp = &sqltds;
#line 894 "esemco0r.pc"
            sqlstm.stmt = "select BUSS_E_DESC into :b0  from BUSINESS where \
BUSS_CODE=:b1";
#line 894 "esemco0r.pc"
            sqlstm.iters = (unsigned int  )1;
#line 894 "esemco0r.pc"
            sqlstm.offset = (unsigned int  )1149;
#line 894 "esemco0r.pc"
            sqlstm.selerr = (unsigned short)1;
#line 894 "esemco0r.pc"
            sqlstm.sqlpfmem = (unsigned int  )0;
#line 894 "esemco0r.pc"
            sqlstm.cud = sqlcud0;
#line 894 "esemco0r.pc"
            sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 894 "esemco0r.pc"
            sqlstm.sqlety = (unsigned short)4352;
#line 894 "esemco0r.pc"
            sqlstm.occurs = (unsigned int  )0;
#line 894 "esemco0r.pc"
            sqlstm.sqhstv[0] = (unsigned char  *)&o_buss_desc;
#line 894 "esemco0r.pc"
            sqlstm.sqhstl[0] = (unsigned long )33;
#line 894 "esemco0r.pc"
            sqlstm.sqhsts[0] = (         int  )0;
#line 894 "esemco0r.pc"
            sqlstm.sqindv[0] = (         short *)0;
#line 894 "esemco0r.pc"
            sqlstm.sqinds[0] = (         int  )0;
#line 894 "esemco0r.pc"
            sqlstm.sqharm[0] = (unsigned long )0;
#line 894 "esemco0r.pc"
            sqlstm.sqadto[0] = (unsigned short )0;
#line 894 "esemco0r.pc"
            sqlstm.sqtdso[0] = (unsigned short )0;
#line 894 "esemco0r.pc"
            sqlstm.sqhstv[1] = (unsigned char  *)&o_buss_code;
#line 894 "esemco0r.pc"
            sqlstm.sqhstl[1] = (unsigned long )7;
#line 894 "esemco0r.pc"
            sqlstm.sqhsts[1] = (         int  )0;
#line 894 "esemco0r.pc"
            sqlstm.sqindv[1] = (         short *)0;
#line 894 "esemco0r.pc"
            sqlstm.sqinds[1] = (         int  )0;
#line 894 "esemco0r.pc"
            sqlstm.sqharm[1] = (unsigned long )0;
#line 894 "esemco0r.pc"
            sqlstm.sqadto[1] = (unsigned short )0;
#line 894 "esemco0r.pc"
            sqlstm.sqtdso[1] = (unsigned short )0;
#line 894 "esemco0r.pc"
            sqlstm.sqphsv = sqlstm.sqhstv;
#line 894 "esemco0r.pc"
            sqlstm.sqphsl = sqlstm.sqhstl;
#line 894 "esemco0r.pc"
            sqlstm.sqphss = sqlstm.sqhsts;
#line 894 "esemco0r.pc"
            sqlstm.sqpind = sqlstm.sqindv;
#line 894 "esemco0r.pc"
            sqlstm.sqpins = sqlstm.sqinds;
#line 894 "esemco0r.pc"
            sqlstm.sqparm = sqlstm.sqharm;
#line 894 "esemco0r.pc"
            sqlstm.sqparc = sqlstm.sqharc;
#line 894 "esemco0r.pc"
            sqlstm.sqpadto = sqlstm.sqadto;
#line 894 "esemco0r.pc"
            sqlstm.sqptdso = sqlstm.sqtdso;
#line 894 "esemco0r.pc"
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 894 "esemco0r.pc"
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 894 "esemco0r.pc"
}

#line 898 "esemco0r.pc"


printf( "esemco0r 070 EXEC SQL  RX_ONLY  %c|%s|%s-%s \n\r" ,o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  070 EXEC SQL  RX_ONLY %c|%s|%s-%s  \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr);	
system(msg);	
						 
				 
            if (sqlca.sqlcode == NOT_FOUND)
            {

printf( "esemco0r 070a EXEC SQL  NOT_FOUND  %c|%s|%s-%s \n\r" ,o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  070a EXEC SQL  NOT_FOUND %c|%s|%s-%s BUSINESS record not found for code : %s \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr,o_buss_code.arr);	
system(msg);	
					
				
                fprintf(afp, "BUSINESS record not found for code : %s\n",
                       o_buss_code.arr);
                continue;
            }

            o_buss_desc.arr[o_buss_desc.len] = '\0';
/*
printf("buss: %s %d\n", o_buss_desc.arr, o_buss_desc.len);
fflush(stdin);
*/
        }
		
		
printf( "esemco0r 071 end if  %c|%s|%s-%s \n\r" ,o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  071 end if  RX_ONLY %c|%s|%s-%s  \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr);	
system(msg);

/* if statment added by CK Wong to screen the report of pager cochannel */
/* because the pager channel is used exclusively by one system only !   */

/*        if(strcmp(o_sys_type.arr,"01")!=0 && strcmp(o_sys_type.arr,"42")!=0) */
        if(strcmp((char *)o_sys_type.arr,"01")!=0 && strcmp((char *)o_sys_type.arr,"42")!=0)
        {
          print_cochaninf_line(cfp, curr_sys, &line_cnt, &page_cnt);
          cochan_tot++;
        }

		
printf( "esemco0r 072 EXEC SQL  RX_ONLY  %c|%s|%s-%s \n\r" ,o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  072 EXEC SQL  RX_ONLY %c|%s|%s-%s  \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr);	
system(msg);
		
		

sprintf(msg, "echo \"esemco0r%s %s  072a EXEC SQL  RX_ONLY o_tx_freq=%If o_rx_freq=%lf o_tone_freq=%lf o_base_no=%d    \" >> /tmp/debug", sys_date ,sys_time,  o_tx_freq,o_rx_freq,o_tone_freq,o_base_no);	
system(msg);	

sprintf(msg, "echo \"esemco0r%s %s  072b EXEC SQL  RX_ONLY prev_tx_freq=%If prev_rx_freq=%lf prev_tone_freq=%lf prev_base_no=%d    \" >> /tmp/debug", sys_date ,sys_time,  prev_tx_freq,prev_rx_freq,prev_tone_freq,prev_base_no);	
system(msg);		
		
        prev_tx_freq = o_tx_freq;
        prev_rx_freq = o_rx_freq;
        prev_tone_freq = o_tone_freq;
        prev_base_no = o_base_no;


sprintf(msg, "echo \"esemco0r%s %s  072c EXEC SQL  RX_ONLY o_tx_freq=%If o_rx_freq=%lf o_tone_freq=%lf o_base_no=%d    \" >> /tmp/debug", sys_date ,sys_time,  o_tx_freq,o_rx_freq,o_tone_freq,o_base_no);	
system(msg);	

sprintf(msg, "echo \"esemco0r%s %s  072d EXEC SQL  RX_ONLY prev_tx_freq=%If prev_rx_freq=%lf prev_tone_freq=%lf prev_base_no=%d    \" >> /tmp/debug", sys_date ,sys_time,  prev_tx_freq,prev_rx_freq,prev_tone_freq,prev_base_no);	
system(msg);
		
		
		
		
		
        switch (tx_mode)
        {
            case TX_RX:
                /* EXEC SQL
                     FETCH CO01
                     INTO  :o_sys_category, :o_sys_type, :o_sys_no, 
                           :o_sys_suffix, :o_base_no, :o_tx_freq, 
                           :o_rx_freq, :o_tone_freq; */ 
#line 940 "esemco0r.pc"

{
#line 936 "esemco0r.pc"
                struct sqlexd sqlstm;
#line 936 "esemco0r.pc"
                sqlstm.sqlvsn = 13;
#line 936 "esemco0r.pc"
                sqlstm.arrsiz = 10;
#line 936 "esemco0r.pc"
                sqlstm.sqladtp = &sqladt;
#line 936 "esemco0r.pc"
                sqlstm.sqltdsp = &sqltds;
#line 936 "esemco0r.pc"
                sqlstm.iters = (unsigned int  )1;
#line 936 "esemco0r.pc"
                sqlstm.offset = (unsigned int  )1172;
#line 936 "esemco0r.pc"
                sqlstm.selerr = (unsigned short)1;
#line 936 "esemco0r.pc"
                sqlstm.sqlpfmem = (unsigned int  )0;
#line 936 "esemco0r.pc"
                sqlstm.cud = sqlcud0;
#line 936 "esemco0r.pc"
                sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 936 "esemco0r.pc"
                sqlstm.sqlety = (unsigned short)4352;
#line 936 "esemco0r.pc"
                sqlstm.occurs = (unsigned int  )0;
#line 936 "esemco0r.pc"
                sqlstm.sqfoff = (         int )0;
#line 936 "esemco0r.pc"
                sqlstm.sqfmod = (unsigned int )2;
#line 936 "esemco0r.pc"
                sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
#line 936 "esemco0r.pc"
                sqlstm.sqhstl[0] = (unsigned long )1;
#line 936 "esemco0r.pc"
                sqlstm.sqhsts[0] = (         int  )0;
#line 936 "esemco0r.pc"
                sqlstm.sqindv[0] = (         short *)0;
#line 936 "esemco0r.pc"
                sqlstm.sqinds[0] = (         int  )0;
#line 936 "esemco0r.pc"
                sqlstm.sqharm[0] = (unsigned long )0;
#line 936 "esemco0r.pc"
                sqlstm.sqadto[0] = (unsigned short )0;
#line 936 "esemco0r.pc"
                sqlstm.sqtdso[0] = (unsigned short )0;
#line 936 "esemco0r.pc"
                sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
#line 936 "esemco0r.pc"
                sqlstm.sqhstl[1] = (unsigned long )5;
#line 936 "esemco0r.pc"
                sqlstm.sqhsts[1] = (         int  )0;
#line 936 "esemco0r.pc"
                sqlstm.sqindv[1] = (         short *)0;
#line 936 "esemco0r.pc"
                sqlstm.sqinds[1] = (         int  )0;
#line 936 "esemco0r.pc"
                sqlstm.sqharm[1] = (unsigned long )0;
#line 936 "esemco0r.pc"
                sqlstm.sqadto[1] = (unsigned short )0;
#line 936 "esemco0r.pc"
                sqlstm.sqtdso[1] = (unsigned short )0;
#line 936 "esemco0r.pc"
                sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
#line 936 "esemco0r.pc"
                sqlstm.sqhstl[2] = (unsigned long )10;
#line 936 "esemco0r.pc"
                sqlstm.sqhsts[2] = (         int  )0;
#line 936 "esemco0r.pc"
                sqlstm.sqindv[2] = (         short *)0;
#line 936 "esemco0r.pc"
                sqlstm.sqinds[2] = (         int  )0;
#line 936 "esemco0r.pc"
                sqlstm.sqharm[2] = (unsigned long )0;
#line 936 "esemco0r.pc"
                sqlstm.sqadto[2] = (unsigned short )0;
#line 936 "esemco0r.pc"
                sqlstm.sqtdso[2] = (unsigned short )0;
#line 936 "esemco0r.pc"
                sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
#line 936 "esemco0r.pc"
                sqlstm.sqhstl[3] = (unsigned long )6;
#line 936 "esemco0r.pc"
                sqlstm.sqhsts[3] = (         int  )0;
#line 936 "esemco0r.pc"
                sqlstm.sqindv[3] = (         short *)0;
#line 936 "esemco0r.pc"
                sqlstm.sqinds[3] = (         int  )0;
#line 936 "esemco0r.pc"
                sqlstm.sqharm[3] = (unsigned long )0;
#line 936 "esemco0r.pc"
                sqlstm.sqadto[3] = (unsigned short )0;
#line 936 "esemco0r.pc"
                sqlstm.sqtdso[3] = (unsigned short )0;
#line 936 "esemco0r.pc"
                sqlstm.sqhstv[4] = (unsigned char  *)&o_base_no;
#line 936 "esemco0r.pc"
                sqlstm.sqhstl[4] = (unsigned long )sizeof(int);
#line 936 "esemco0r.pc"
                sqlstm.sqhsts[4] = (         int  )0;
#line 936 "esemco0r.pc"
                sqlstm.sqindv[4] = (         short *)0;
#line 936 "esemco0r.pc"
                sqlstm.sqinds[4] = (         int  )0;
#line 936 "esemco0r.pc"
                sqlstm.sqharm[4] = (unsigned long )0;
#line 936 "esemco0r.pc"
                sqlstm.sqadto[4] = (unsigned short )0;
#line 936 "esemco0r.pc"
                sqlstm.sqtdso[4] = (unsigned short )0;
#line 936 "esemco0r.pc"
                sqlstm.sqhstv[5] = (unsigned char  *)&o_tx_freq;
#line 936 "esemco0r.pc"
                sqlstm.sqhstl[5] = (unsigned long )sizeof(double);
#line 936 "esemco0r.pc"
                sqlstm.sqhsts[5] = (         int  )0;
#line 936 "esemco0r.pc"
                sqlstm.sqindv[5] = (         short *)0;
#line 936 "esemco0r.pc"
                sqlstm.sqinds[5] = (         int  )0;
#line 936 "esemco0r.pc"
                sqlstm.sqharm[5] = (unsigned long )0;
#line 936 "esemco0r.pc"
                sqlstm.sqadto[5] = (unsigned short )0;
#line 936 "esemco0r.pc"
                sqlstm.sqtdso[5] = (unsigned short )0;
#line 936 "esemco0r.pc"
                sqlstm.sqhstv[6] = (unsigned char  *)&o_rx_freq;
#line 936 "esemco0r.pc"
                sqlstm.sqhstl[6] = (unsigned long )sizeof(double);
#line 936 "esemco0r.pc"
                sqlstm.sqhsts[6] = (         int  )0;
#line 936 "esemco0r.pc"
                sqlstm.sqindv[6] = (         short *)0;
#line 936 "esemco0r.pc"
                sqlstm.sqinds[6] = (         int  )0;
#line 936 "esemco0r.pc"
                sqlstm.sqharm[6] = (unsigned long )0;
#line 936 "esemco0r.pc"
                sqlstm.sqadto[6] = (unsigned short )0;
#line 936 "esemco0r.pc"
                sqlstm.sqtdso[6] = (unsigned short )0;
#line 936 "esemco0r.pc"
                sqlstm.sqhstv[7] = (unsigned char  *)&o_tone_freq;
#line 936 "esemco0r.pc"
                sqlstm.sqhstl[7] = (unsigned long )sizeof(double);
#line 936 "esemco0r.pc"
                sqlstm.sqhsts[7] = (         int  )0;
#line 936 "esemco0r.pc"
                sqlstm.sqindv[7] = (         short *)0;
#line 936 "esemco0r.pc"
                sqlstm.sqinds[7] = (         int  )0;
#line 936 "esemco0r.pc"
                sqlstm.sqharm[7] = (unsigned long )0;
#line 936 "esemco0r.pc"
                sqlstm.sqadto[7] = (unsigned short )0;
#line 936 "esemco0r.pc"
                sqlstm.sqtdso[7] = (unsigned short )0;
#line 936 "esemco0r.pc"
                sqlstm.sqphsv = sqlstm.sqhstv;
#line 936 "esemco0r.pc"
                sqlstm.sqphsl = sqlstm.sqhstl;
#line 936 "esemco0r.pc"
                sqlstm.sqphss = sqlstm.sqhsts;
#line 936 "esemco0r.pc"
                sqlstm.sqpind = sqlstm.sqindv;
#line 936 "esemco0r.pc"
                sqlstm.sqpins = sqlstm.sqinds;
#line 936 "esemco0r.pc"
                sqlstm.sqparm = sqlstm.sqharm;
#line 936 "esemco0r.pc"
                sqlstm.sqparc = sqlstm.sqharc;
#line 936 "esemco0r.pc"
                sqlstm.sqpadto = sqlstm.sqadto;
#line 936 "esemco0r.pc"
                sqlstm.sqptdso = sqlstm.sqtdso;
#line 936 "esemco0r.pc"
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 936 "esemco0r.pc"
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 936 "esemco0r.pc"
}

#line 940 "esemco0r.pc"

                break;

            case RX_ONLY:
                /* EXEC SQL
                     FETCH CO02
                     INTO  :o_sys_category, :o_sys_type, :o_sys_no, 
                           :o_sys_suffix, :o_base_no, :o_tx_freq,
                           :o_rx_freq, :o_tone_freq; */ 
#line 948 "esemco0r.pc"

{
#line 944 "esemco0r.pc"
                struct sqlexd sqlstm;
#line 944 "esemco0r.pc"
                sqlstm.sqlvsn = 13;
#line 944 "esemco0r.pc"
                sqlstm.arrsiz = 10;
#line 944 "esemco0r.pc"
                sqlstm.sqladtp = &sqladt;
#line 944 "esemco0r.pc"
                sqlstm.sqltdsp = &sqltds;
#line 944 "esemco0r.pc"
                sqlstm.iters = (unsigned int  )1;
#line 944 "esemco0r.pc"
                sqlstm.offset = (unsigned int  )1219;
#line 944 "esemco0r.pc"
                sqlstm.selerr = (unsigned short)1;
#line 944 "esemco0r.pc"
                sqlstm.sqlpfmem = (unsigned int  )0;
#line 944 "esemco0r.pc"
                sqlstm.cud = sqlcud0;
#line 944 "esemco0r.pc"
                sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 944 "esemco0r.pc"
                sqlstm.sqlety = (unsigned short)4352;
#line 944 "esemco0r.pc"
                sqlstm.occurs = (unsigned int  )0;
#line 944 "esemco0r.pc"
                sqlstm.sqfoff = (         int )0;
#line 944 "esemco0r.pc"
                sqlstm.sqfmod = (unsigned int )2;
#line 944 "esemco0r.pc"
                sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
#line 944 "esemco0r.pc"
                sqlstm.sqhstl[0] = (unsigned long )1;
#line 944 "esemco0r.pc"
                sqlstm.sqhsts[0] = (         int  )0;
#line 944 "esemco0r.pc"
                sqlstm.sqindv[0] = (         short *)0;
#line 944 "esemco0r.pc"
                sqlstm.sqinds[0] = (         int  )0;
#line 944 "esemco0r.pc"
                sqlstm.sqharm[0] = (unsigned long )0;
#line 944 "esemco0r.pc"
                sqlstm.sqadto[0] = (unsigned short )0;
#line 944 "esemco0r.pc"
                sqlstm.sqtdso[0] = (unsigned short )0;
#line 944 "esemco0r.pc"
                sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
#line 944 "esemco0r.pc"
                sqlstm.sqhstl[1] = (unsigned long )5;
#line 944 "esemco0r.pc"
                sqlstm.sqhsts[1] = (         int  )0;
#line 944 "esemco0r.pc"
                sqlstm.sqindv[1] = (         short *)0;
#line 944 "esemco0r.pc"
                sqlstm.sqinds[1] = (         int  )0;
#line 944 "esemco0r.pc"
                sqlstm.sqharm[1] = (unsigned long )0;
#line 944 "esemco0r.pc"
                sqlstm.sqadto[1] = (unsigned short )0;
#line 944 "esemco0r.pc"
                sqlstm.sqtdso[1] = (unsigned short )0;
#line 944 "esemco0r.pc"
                sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
#line 944 "esemco0r.pc"
                sqlstm.sqhstl[2] = (unsigned long )10;
#line 944 "esemco0r.pc"
                sqlstm.sqhsts[2] = (         int  )0;
#line 944 "esemco0r.pc"
                sqlstm.sqindv[2] = (         short *)0;
#line 944 "esemco0r.pc"
                sqlstm.sqinds[2] = (         int  )0;
#line 944 "esemco0r.pc"
                sqlstm.sqharm[2] = (unsigned long )0;
#line 944 "esemco0r.pc"
                sqlstm.sqadto[2] = (unsigned short )0;
#line 944 "esemco0r.pc"
                sqlstm.sqtdso[2] = (unsigned short )0;
#line 944 "esemco0r.pc"
                sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
#line 944 "esemco0r.pc"
                sqlstm.sqhstl[3] = (unsigned long )6;
#line 944 "esemco0r.pc"
                sqlstm.sqhsts[3] = (         int  )0;
#line 944 "esemco0r.pc"
                sqlstm.sqindv[3] = (         short *)0;
#line 944 "esemco0r.pc"
                sqlstm.sqinds[3] = (         int  )0;
#line 944 "esemco0r.pc"
                sqlstm.sqharm[3] = (unsigned long )0;
#line 944 "esemco0r.pc"
                sqlstm.sqadto[3] = (unsigned short )0;
#line 944 "esemco0r.pc"
                sqlstm.sqtdso[3] = (unsigned short )0;
#line 944 "esemco0r.pc"
                sqlstm.sqhstv[4] = (unsigned char  *)&o_base_no;
#line 944 "esemco0r.pc"
                sqlstm.sqhstl[4] = (unsigned long )sizeof(int);
#line 944 "esemco0r.pc"
                sqlstm.sqhsts[4] = (         int  )0;
#line 944 "esemco0r.pc"
                sqlstm.sqindv[4] = (         short *)0;
#line 944 "esemco0r.pc"
                sqlstm.sqinds[4] = (         int  )0;
#line 944 "esemco0r.pc"
                sqlstm.sqharm[4] = (unsigned long )0;
#line 944 "esemco0r.pc"
                sqlstm.sqadto[4] = (unsigned short )0;
#line 944 "esemco0r.pc"
                sqlstm.sqtdso[4] = (unsigned short )0;
#line 944 "esemco0r.pc"
                sqlstm.sqhstv[5] = (unsigned char  *)&o_tx_freq;
#line 944 "esemco0r.pc"
                sqlstm.sqhstl[5] = (unsigned long )sizeof(double);
#line 944 "esemco0r.pc"
                sqlstm.sqhsts[5] = (         int  )0;
#line 944 "esemco0r.pc"
                sqlstm.sqindv[5] = (         short *)0;
#line 944 "esemco0r.pc"
                sqlstm.sqinds[5] = (         int  )0;
#line 944 "esemco0r.pc"
                sqlstm.sqharm[5] = (unsigned long )0;
#line 944 "esemco0r.pc"
                sqlstm.sqadto[5] = (unsigned short )0;
#line 944 "esemco0r.pc"
                sqlstm.sqtdso[5] = (unsigned short )0;
#line 944 "esemco0r.pc"
                sqlstm.sqhstv[6] = (unsigned char  *)&o_rx_freq;
#line 944 "esemco0r.pc"
                sqlstm.sqhstl[6] = (unsigned long )sizeof(double);
#line 944 "esemco0r.pc"
                sqlstm.sqhsts[6] = (         int  )0;
#line 944 "esemco0r.pc"
                sqlstm.sqindv[6] = (         short *)0;
#line 944 "esemco0r.pc"
                sqlstm.sqinds[6] = (         int  )0;
#line 944 "esemco0r.pc"
                sqlstm.sqharm[6] = (unsigned long )0;
#line 944 "esemco0r.pc"
                sqlstm.sqadto[6] = (unsigned short )0;
#line 944 "esemco0r.pc"
                sqlstm.sqtdso[6] = (unsigned short )0;
#line 944 "esemco0r.pc"
                sqlstm.sqhstv[7] = (unsigned char  *)&o_tone_freq;
#line 944 "esemco0r.pc"
                sqlstm.sqhstl[7] = (unsigned long )sizeof(double);
#line 944 "esemco0r.pc"
                sqlstm.sqhsts[7] = (         int  )0;
#line 944 "esemco0r.pc"
                sqlstm.sqindv[7] = (         short *)0;
#line 944 "esemco0r.pc"
                sqlstm.sqinds[7] = (         int  )0;
#line 944 "esemco0r.pc"
                sqlstm.sqharm[7] = (unsigned long )0;
#line 944 "esemco0r.pc"
                sqlstm.sqadto[7] = (unsigned short )0;
#line 944 "esemco0r.pc"
                sqlstm.sqtdso[7] = (unsigned short )0;
#line 944 "esemco0r.pc"
                sqlstm.sqphsv = sqlstm.sqhstv;
#line 944 "esemco0r.pc"
                sqlstm.sqphsl = sqlstm.sqhstl;
#line 944 "esemco0r.pc"
                sqlstm.sqphss = sqlstm.sqhsts;
#line 944 "esemco0r.pc"
                sqlstm.sqpind = sqlstm.sqindv;
#line 944 "esemco0r.pc"
                sqlstm.sqpins = sqlstm.sqinds;
#line 944 "esemco0r.pc"
                sqlstm.sqparm = sqlstm.sqharm;
#line 944 "esemco0r.pc"
                sqlstm.sqparc = sqlstm.sqharc;
#line 944 "esemco0r.pc"
                sqlstm.sqpadto = sqlstm.sqadto;
#line 944 "esemco0r.pc"
                sqlstm.sqptdso = sqlstm.sqtdso;
#line 944 "esemco0r.pc"
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 944 "esemco0r.pc"
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 944 "esemco0r.pc"
}

#line 948 "esemco0r.pc"

                break;

            case TX_ONLY:
                /* EXEC SQL
                     FETCH CO03
                     INTO  :o_sys_category, :o_sys_type, :o_sys_no, 
                           :o_sys_suffix, :o_base_no, :o_tx_freq,
                           :o_rx_freq, :o_tone_freq; */ 
#line 956 "esemco0r.pc"

{
#line 952 "esemco0r.pc"
                struct sqlexd sqlstm;
#line 952 "esemco0r.pc"
                sqlstm.sqlvsn = 13;
#line 952 "esemco0r.pc"
                sqlstm.arrsiz = 10;
#line 952 "esemco0r.pc"
                sqlstm.sqladtp = &sqladt;
#line 952 "esemco0r.pc"
                sqlstm.sqltdsp = &sqltds;
#line 952 "esemco0r.pc"
                sqlstm.iters = (unsigned int  )1;
#line 952 "esemco0r.pc"
                sqlstm.offset = (unsigned int  )1266;
#line 952 "esemco0r.pc"
                sqlstm.selerr = (unsigned short)1;
#line 952 "esemco0r.pc"
                sqlstm.sqlpfmem = (unsigned int  )0;
#line 952 "esemco0r.pc"
                sqlstm.cud = sqlcud0;
#line 952 "esemco0r.pc"
                sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 952 "esemco0r.pc"
                sqlstm.sqlety = (unsigned short)4352;
#line 952 "esemco0r.pc"
                sqlstm.occurs = (unsigned int  )0;
#line 952 "esemco0r.pc"
                sqlstm.sqfoff = (         int )0;
#line 952 "esemco0r.pc"
                sqlstm.sqfmod = (unsigned int )2;
#line 952 "esemco0r.pc"
                sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
#line 952 "esemco0r.pc"
                sqlstm.sqhstl[0] = (unsigned long )1;
#line 952 "esemco0r.pc"
                sqlstm.sqhsts[0] = (         int  )0;
#line 952 "esemco0r.pc"
                sqlstm.sqindv[0] = (         short *)0;
#line 952 "esemco0r.pc"
                sqlstm.sqinds[0] = (         int  )0;
#line 952 "esemco0r.pc"
                sqlstm.sqharm[0] = (unsigned long )0;
#line 952 "esemco0r.pc"
                sqlstm.sqadto[0] = (unsigned short )0;
#line 952 "esemco0r.pc"
                sqlstm.sqtdso[0] = (unsigned short )0;
#line 952 "esemco0r.pc"
                sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
#line 952 "esemco0r.pc"
                sqlstm.sqhstl[1] = (unsigned long )5;
#line 952 "esemco0r.pc"
                sqlstm.sqhsts[1] = (         int  )0;
#line 952 "esemco0r.pc"
                sqlstm.sqindv[1] = (         short *)0;
#line 952 "esemco0r.pc"
                sqlstm.sqinds[1] = (         int  )0;
#line 952 "esemco0r.pc"
                sqlstm.sqharm[1] = (unsigned long )0;
#line 952 "esemco0r.pc"
                sqlstm.sqadto[1] = (unsigned short )0;
#line 952 "esemco0r.pc"
                sqlstm.sqtdso[1] = (unsigned short )0;
#line 952 "esemco0r.pc"
                sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
#line 952 "esemco0r.pc"
                sqlstm.sqhstl[2] = (unsigned long )10;
#line 952 "esemco0r.pc"
                sqlstm.sqhsts[2] = (         int  )0;
#line 952 "esemco0r.pc"
                sqlstm.sqindv[2] = (         short *)0;
#line 952 "esemco0r.pc"
                sqlstm.sqinds[2] = (         int  )0;
#line 952 "esemco0r.pc"
                sqlstm.sqharm[2] = (unsigned long )0;
#line 952 "esemco0r.pc"
                sqlstm.sqadto[2] = (unsigned short )0;
#line 952 "esemco0r.pc"
                sqlstm.sqtdso[2] = (unsigned short )0;
#line 952 "esemco0r.pc"
                sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
#line 952 "esemco0r.pc"
                sqlstm.sqhstl[3] = (unsigned long )6;
#line 952 "esemco0r.pc"
                sqlstm.sqhsts[3] = (         int  )0;
#line 952 "esemco0r.pc"
                sqlstm.sqindv[3] = (         short *)0;
#line 952 "esemco0r.pc"
                sqlstm.sqinds[3] = (         int  )0;
#line 952 "esemco0r.pc"
                sqlstm.sqharm[3] = (unsigned long )0;
#line 952 "esemco0r.pc"
                sqlstm.sqadto[3] = (unsigned short )0;
#line 952 "esemco0r.pc"
                sqlstm.sqtdso[3] = (unsigned short )0;
#line 952 "esemco0r.pc"
                sqlstm.sqhstv[4] = (unsigned char  *)&o_base_no;
#line 952 "esemco0r.pc"
                sqlstm.sqhstl[4] = (unsigned long )sizeof(int);
#line 952 "esemco0r.pc"
                sqlstm.sqhsts[4] = (         int  )0;
#line 952 "esemco0r.pc"
                sqlstm.sqindv[4] = (         short *)0;
#line 952 "esemco0r.pc"
                sqlstm.sqinds[4] = (         int  )0;
#line 952 "esemco0r.pc"
                sqlstm.sqharm[4] = (unsigned long )0;
#line 952 "esemco0r.pc"
                sqlstm.sqadto[4] = (unsigned short )0;
#line 952 "esemco0r.pc"
                sqlstm.sqtdso[4] = (unsigned short )0;
#line 952 "esemco0r.pc"
                sqlstm.sqhstv[5] = (unsigned char  *)&o_tx_freq;
#line 952 "esemco0r.pc"
                sqlstm.sqhstl[5] = (unsigned long )sizeof(double);
#line 952 "esemco0r.pc"
                sqlstm.sqhsts[5] = (         int  )0;
#line 952 "esemco0r.pc"
                sqlstm.sqindv[5] = (         short *)0;
#line 952 "esemco0r.pc"
                sqlstm.sqinds[5] = (         int  )0;
#line 952 "esemco0r.pc"
                sqlstm.sqharm[5] = (unsigned long )0;
#line 952 "esemco0r.pc"
                sqlstm.sqadto[5] = (unsigned short )0;
#line 952 "esemco0r.pc"
                sqlstm.sqtdso[5] = (unsigned short )0;
#line 952 "esemco0r.pc"
                sqlstm.sqhstv[6] = (unsigned char  *)&o_rx_freq;
#line 952 "esemco0r.pc"
                sqlstm.sqhstl[6] = (unsigned long )sizeof(double);
#line 952 "esemco0r.pc"
                sqlstm.sqhsts[6] = (         int  )0;
#line 952 "esemco0r.pc"
                sqlstm.sqindv[6] = (         short *)0;
#line 952 "esemco0r.pc"
                sqlstm.sqinds[6] = (         int  )0;
#line 952 "esemco0r.pc"
                sqlstm.sqharm[6] = (unsigned long )0;
#line 952 "esemco0r.pc"
                sqlstm.sqadto[6] = (unsigned short )0;
#line 952 "esemco0r.pc"
                sqlstm.sqtdso[6] = (unsigned short )0;
#line 952 "esemco0r.pc"
                sqlstm.sqhstv[7] = (unsigned char  *)&o_tone_freq;
#line 952 "esemco0r.pc"
                sqlstm.sqhstl[7] = (unsigned long )sizeof(double);
#line 952 "esemco0r.pc"
                sqlstm.sqhsts[7] = (         int  )0;
#line 952 "esemco0r.pc"
                sqlstm.sqindv[7] = (         short *)0;
#line 952 "esemco0r.pc"
                sqlstm.sqinds[7] = (         int  )0;
#line 952 "esemco0r.pc"
                sqlstm.sqharm[7] = (unsigned long )0;
#line 952 "esemco0r.pc"
                sqlstm.sqadto[7] = (unsigned short )0;
#line 952 "esemco0r.pc"
                sqlstm.sqtdso[7] = (unsigned short )0;
#line 952 "esemco0r.pc"
                sqlstm.sqphsv = sqlstm.sqhstv;
#line 952 "esemco0r.pc"
                sqlstm.sqphsl = sqlstm.sqhstl;
#line 952 "esemco0r.pc"
                sqlstm.sqphss = sqlstm.sqhsts;
#line 952 "esemco0r.pc"
                sqlstm.sqpind = sqlstm.sqindv;
#line 952 "esemco0r.pc"
                sqlstm.sqpins = sqlstm.sqinds;
#line 952 "esemco0r.pc"
                sqlstm.sqparm = sqlstm.sqharm;
#line 952 "esemco0r.pc"
                sqlstm.sqparc = sqlstm.sqharc;
#line 952 "esemco0r.pc"
                sqlstm.sqpadto = sqlstm.sqadto;
#line 952 "esemco0r.pc"
                sqlstm.sqptdso = sqlstm.sqtdso;
#line 952 "esemco0r.pc"
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 952 "esemco0r.pc"
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 952 "esemco0r.pc"
}

#line 956 "esemco0r.pc"

                break;
        }

		

printf( "esemco0r 073 EXEC SQL  RX_ONLY  %c|%s|%s-%s \n\r" ,o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  073 EXEC SQL  RX_ONLY %c|%s|%s-%s  \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr);	
system(msg);		
		
        if (sqlca.sqlcode == NOT_FOUND)
			
		printf( "esemco0r 073a EXEC SQL  RX_ONLY  %c|%s|%s-%s \n\r" ,o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  073a EXEC SQL  RX_ONLY %c|%s|%s-%s  \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr);	
system(msg);
            break;
    }

/* this is the new code */
    for (i = 0; i < prop_fq_cnt; i++)
        if ((prop->tx_channel == fq_list[i]->tx_channel)
        &&  (prop->rx_channel == fq_list[i]->rx_channel))
            if (prop->stn_node != fq_list[i]->stn_node)
            {
                int    j = fq_list[i]->stn_node;
/*
printf("i sysid node: %d %c%s%s%s %d\n", i,
exist[fq_list[i]->stn_node]->sys_category,
exist[fq_list[i]->stn_node]->sys_type,
exist[fq_list[i]->stn_node]->sys_no,
exist[fq_list[i]->stn_node]->sys_suffix,
fq_list[i]->stn_node);
*/
               if (line_cnt == COCHANINF_LINES)
                   line_cnt = 0;

               if (line_cnt == 0)
               {
                   print_cochaninf_head(cfp, &page_cnt);
                   page_cnt++;
               }

               sprintf(curr_sys, "%c%s%s-%s", exist[j]->sys_category,
                       exist[j]->sys_type, exist[j]->sys_no,
                       exist[j]->sys_suffix);
               fprintf(cfp, "   %-48s %-14s%2s%c%6s-%6s%-3s%4s%05d %05d%11s-\n",
                       "(PROPOSED STATION)", curr_sys, "",
                       exist[j]->station_type, "", "", exist[j]->sub_district, 
                       "", exist[j]->east_grid, exist[j]->north_grid, "");
               line_cnt++; 
               cochan_tot++; 
           }

		   
printf( "esemco0r 074 EXEC SQL  RX_ONLY  %c|%s|%s-%s \n\r" ,o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s %s  074 EXEC SQL  RX_ONLY %c|%s|%s-%s  \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr);	
system(msg);
		   
		   
		   
    if (cochan_tot == 0)
    {
		

sprintf(msg, "echo \"esemco0r%s %s  075 No co-channel stations found  %c|%s|%s-%s  co_fname=%s    \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr,co_fname);	
system(msg);
		
        fprintf(afp, "No co-channel stations found\n");
        fclose(cfp);
        unlink(co_fname);
    }
    else
    {
		
		
sprintf(msg, "echo \"esemco0r%s %s  076 else No co-channel stations found  %c|%s|%s-%s  co_fname=%s    \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr,co_fname);	
system(msg);
		

		
        skip_lines = 4 + (COCHANINF_LINES - line_cnt);
        for (i = 0; i < skip_lines; i++)
            fprintf(cfp, "\n");
        fprintf(cfp, "%47sTOTAL NO. OF MOBILES             :  %-4d\n", "",
                    tot_mobile_cnt);
        fprintf(cfp, "%47sTOTAL NO. OF CO-CHANNEL STATIONS :  %-3d\f", "",
                    cochan_tot);
        fclose(cfp);

		
sprintf(msg, "echo \"esemco0r%s %s  077 else No co-channel stations found  %c|%s|%s-%s  co_fname=%s   \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr,co_fname);	
system(msg);		
/*
            sprintf(cmdline, "lp -dprinter1 %s > /dev/null", co_fname);
*/
            sprintf(cmdline, "cat %s >> %s", co_fname, print_file);
            system(cmdline);
    }

		

sprintf(msg, "echo \"esemco0r%s %s  078 EXEC SQL  RX_ONLY %c|%s|%s-%s  \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr);	
system(msg);
		   
	
	
close_cursor:


sprintf(msg, "echo \"esemco0r%s %s  079 EXEC SQL  RX_ONLY %c|%s|%s-%s  \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr);	
system(msg);


    switch (tx_mode)
    {
        case TX_RX:
            /* EXEC SQL CLOSE CO01; */ 
#line 1036 "esemco0r.pc"

{
#line 1036 "esemco0r.pc"
            struct sqlexd sqlstm;
#line 1036 "esemco0r.pc"
            sqlstm.sqlvsn = 13;
#line 1036 "esemco0r.pc"
            sqlstm.arrsiz = 10;
#line 1036 "esemco0r.pc"
            sqlstm.sqladtp = &sqladt;
#line 1036 "esemco0r.pc"
            sqlstm.sqltdsp = &sqltds;
#line 1036 "esemco0r.pc"
            sqlstm.iters = (unsigned int  )1;
#line 1036 "esemco0r.pc"
            sqlstm.offset = (unsigned int  )1313;
#line 1036 "esemco0r.pc"
            sqlstm.cud = sqlcud0;
#line 1036 "esemco0r.pc"
            sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 1036 "esemco0r.pc"
            sqlstm.sqlety = (unsigned short)4352;
#line 1036 "esemco0r.pc"
            sqlstm.occurs = (unsigned int  )0;
#line 1036 "esemco0r.pc"
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 1036 "esemco0r.pc"
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 1036 "esemco0r.pc"
}

#line 1036 "esemco0r.pc"

            break;

        case RX_ONLY:
            /* EXEC SQL CLOSE CO02; */ 
#line 1040 "esemco0r.pc"

{
#line 1040 "esemco0r.pc"
            struct sqlexd sqlstm;
#line 1040 "esemco0r.pc"
            sqlstm.sqlvsn = 13;
#line 1040 "esemco0r.pc"
            sqlstm.arrsiz = 10;
#line 1040 "esemco0r.pc"
            sqlstm.sqladtp = &sqladt;
#line 1040 "esemco0r.pc"
            sqlstm.sqltdsp = &sqltds;
#line 1040 "esemco0r.pc"
            sqlstm.iters = (unsigned int  )1;
#line 1040 "esemco0r.pc"
            sqlstm.offset = (unsigned int  )1328;
#line 1040 "esemco0r.pc"
            sqlstm.cud = sqlcud0;
#line 1040 "esemco0r.pc"
            sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 1040 "esemco0r.pc"
            sqlstm.sqlety = (unsigned short)4352;
#line 1040 "esemco0r.pc"
            sqlstm.occurs = (unsigned int  )0;
#line 1040 "esemco0r.pc"
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 1040 "esemco0r.pc"
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 1040 "esemco0r.pc"
}

#line 1040 "esemco0r.pc"

            break;

        case TX_ONLY:
            /* EXEC SQL CLOSE CO03; */ 
#line 1044 "esemco0r.pc"

{
#line 1044 "esemco0r.pc"
            struct sqlexd sqlstm;
#line 1044 "esemco0r.pc"
            sqlstm.sqlvsn = 13;
#line 1044 "esemco0r.pc"
            sqlstm.arrsiz = 10;
#line 1044 "esemco0r.pc"
            sqlstm.sqladtp = &sqladt;
#line 1044 "esemco0r.pc"
            sqlstm.sqltdsp = &sqltds;
#line 1044 "esemco0r.pc"
            sqlstm.iters = (unsigned int  )1;
#line 1044 "esemco0r.pc"
            sqlstm.offset = (unsigned int  )1343;
#line 1044 "esemco0r.pc"
            sqlstm.cud = sqlcud0;
#line 1044 "esemco0r.pc"
            sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 1044 "esemco0r.pc"
            sqlstm.sqlety = (unsigned short)4352;
#line 1044 "esemco0r.pc"
            sqlstm.occurs = (unsigned int  )0;
#line 1044 "esemco0r.pc"
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 1044 "esemco0r.pc"
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 1044 "esemco0r.pc"
}

#line 1044 "esemco0r.pc"

            break;
    }

	
sprintf(msg, "echo \"esemco0r%s %s  099 END   %c|%s|%s-%s  \" >> /tmp/debug", sys_date ,sys_time, o_sys_category, o_sys_type.arr,o_sys_no.arr, o_sys_suffix.arr);	
system(msg);	
	

sprintf(msg, "echo \"  \" >> /tmp/debug");	
system(msg);	
	
	
	
	
	
	
/* commented out by Chen Yung
    return;
*/
/* changed by Chen Yung */
/* original: */
/*    return; */
    return(0);


sqlerr_rtn:
/* changed by Chen Yung */
/* original: */
/*    printf("%s\c", sqlca.sqlerrm.sqlerrmc); */

printf( "**ERROR** esemco0r cochaninf() %s  \n\r" ,sqlca.sqlerrm.sqlerrmc ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s  esemco0r cochaninf() %s \" >> /tmp/debug", sys_date ,sys_time,sqlca.sqlerrm.sqlerrmc );	
system(msg);	



    printf("%s\n", sqlca.sqlerrm.sqlerrmc);
    fflush(afp);
    fclose(afp);
    exit(1);

}


/**********************************************************************/
/*  print co-channel details to co-channel report                     */
/**********************************************************************/

void print_cochaninf_line(cfp, curr_sys, line_cnt, page_cnt)
FILE    *cfp;
char    *curr_sys;
int     *line_cnt, *page_cnt;
{

/*
#ifdef DEBUG
    printf("print_cochaninf_line\n");
#endif
*/

   if (*line_cnt == COCHANINF_LINES)
       *line_cnt = 0;

   if (*line_cnt == 0)
   {
       print_cochaninf_head(cfp, page_cnt);
       (*page_cnt)++;
   }

   
sprintf(msg, "echo \"esemco0r%s %s  072a1 %-48s %-14s  %c   %4d%6s%-3s%4s%05d %05d %-30s \" >> /tmp/debug", sys_date ,sys_time,  o_client_name.arr, curr_sys, o_station_type, o_mobile_cnt, "",  o_sub_district.arr, "", o_grid_east, o_grid_north, o_buss_desc.arr);	
system(msg);
   
   fprintf(cfp, "   %-48s %-14s  %c   %4d%6s%-3s%4s%05d %05d %-30s\n",
           o_client_name.arr, curr_sys, o_station_type, o_mobile_cnt, "",
           o_sub_district.arr, "", o_grid_east, o_grid_north, o_buss_desc.arr);
		   
sprintf(msg, "echo \"esemco0r%s %s  072a2 %-48s %-14s  %c   %4d%6s%-3s%4s%05d %05d %-30s \" >> /tmp/debug", sys_date ,sys_time,  o_client_name.arr, curr_sys, o_station_type, o_mobile_cnt, "",  o_sub_district.arr, "", o_grid_east, o_grid_north, o_buss_desc.arr);	
system(msg);		   
		   
		   
		   
		   
   (*line_cnt)++; 
}


/**********************************************************************/
/*  print co-channel report heading                                   */
/**********************************************************************/

void print_cochaninf_head(cfp, page_cnt)
FILE    *cfp;
int     *page_cnt;
{

/*
#ifdef DEBUG
    printf("print_cochaninf_head\n");
#endif
*/


printf( "esemco0r 000 Print head print_cochaninf_head \n\r"  ); /*Cyrus Debug*/
sprintf(msg, "echo \"esemco0r%s%s   000 START Print head print_cochaninf_head  \" >> /tmp/debug", sys_date ,sys_time);	
system(msg);	


   if (*page_cnt > 1)
       fprintf(cfp, "\f");

   fprintf(cfp, "RUN DATE: %s%23s", sys_date, "");
   fprintf(cfp, "***************************************************");
   fprintf(cfp, "%23sPAGE   : %-d\n", "", *page_cnt);
   fprintf(cfp, "RUN TIME: %s%23s", sys_time, "");
   fprintf(cfp, "*                                                 *");
   fprintf(cfp, "%23sPROGRAM: esemco0r\n", "");
   fprintf(cfp, "USER ID : %-19s%12s", emc_uid, "");
   fprintf(cfp, "*                 E M C   ANALYSIS                *\n");
   fprintf(cfp, "%41s", "");
   fprintf(cfp, "*              CO-CHANNEL INFORMATION             *\n");
   fprintf(cfp, "%41s", "");
   fprintf(cfp, "*                                                 *\n");
   fprintf(cfp, "%41s", "");
   fprintf(cfp, "*            PROP. SYSTEM : %c%s%s-%s%11s*\n",
           prop->sys_category, prop->sys_type, prop->sys_no, 
           prop->sys_suffix, "");
   fprintf(cfp, "%41s", "");
   fprintf(cfp, "*             TX FREQ : %11.5lf MHz           *\n",
           prop_tx_freq);
   fprintf(cfp, "%41s", "");
   fprintf(cfp, "*             RX FREQ : %11.5lf MHz           *\n",
           prop_rx_freq);
   fprintf(cfp, "%41s", "");
   fprintf(cfp, "*                                                 *\n");
   fprintf(cfp, "%41s", "");
   fprintf(cfp, "***************************************************");
   fprintf(cfp, "\n\n\n\n");
   fprintf(cfp, "%67sSTN  NO. OF  SUB-\n", "");
   fprintf(cfp, "%3sLICENCE NAME", "");
   fprintf(cfp, "%37sSYSTEM-ID%6sTYPE MOBILES DISTRICT EAST ", "", "");
   fprintf(cfp, " NORTH BUSINESS CLASSIFICATION\n");
   fprintf(cfp, "%3s================================================", "");
   fprintf(cfp, " ============== ==== ======= ========");
   fprintf(cfp, " ===== ===== ==============================\n\n");
}
