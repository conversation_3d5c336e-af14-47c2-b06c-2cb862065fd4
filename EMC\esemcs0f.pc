/**********************************************************************/
/*                                                                    */
/*    Module Name   :  main (esemcs0f.pc)                             */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  <PERSON><PERSON> <PERSON><PERSON>                                      */
/*                                                                    */
/*    Callers       :  esemcn0f.c through 'system' statement          */  
/*                                                                    */
/*    Parameters    :  user id.                                       */
/*                     user password                                  */
/*                     EMC user id.                                   */
/*                     printer id.                                    */
/*                                                                    */
/*    Called Modules:  screen functions from 'screen.c'               */
/*                     (centre_msg, disp_err, disp_space)             */
/*                     screen function from 'cursesX'                 */
/*                     user_login and user_logout (login.pc)          */
/*                     intermod_2 (esemim0r.c)                        */
/*                     intermod_3 (esemim0r.c)                        */
/*                     sub_district_stn (esemsl0r.pc)                 */
/*                                                                    */
/*    Purpose       :  Accept user input co-site analysis data,       */
/*                     validate the input data, then perform the      */
/*                     user-chosen analysis                           */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <time.h>
#include <curses.h>
#include "../include/define.h"
#include "../include/global.h"
#include "../include/emc.h"
#include "../include/exist.h"
#include "../include/ref.h"
#include "../include/propose.h"
#include <unistd.h>
#include <ctype.h>

#define  COSITE 1

#define  MAX_FLD_LEN  12              /* max. field length of input field */

#define  LOGIN_OK     0

#define  NEXT_START_PT    5          /* field index at which user starts his */
                                     /* input for next co-site analysis      */

#define  COSITE_OPTION    6

#define  HILL_TOP_SITE    'H'

#define  FREQ_MULTIPLE    10000
#define  CHANNEL_SEP      125          /* in the order of 100Hz */
#define  MAX_FREQ         99999.9875   /* in the order of MHz   */
#define  MAX_CULL_FREQ    99.9875      /* in the order of MHz   */

#define  REPRINT_ERROR    "Report not found, press any key to continue"

#include "../include/screen.h"




int user_login    (char *,char *,char *);     						/*20170704 Cyrus [Add] */
int strip_blank   (char *,char *); 									/*20170704 Cyrus [Add] */
int get_sys_date_time(char *,char *,char *,char *); 				/*20170704 Cyrus [Add] */
int disp_heading (char *,char *,char *);							/*20170707 Cyrus Add */
void disp_entry_scn(int,int,int);									/*20170707 Cyrus Add */
int clear_err();													/*20170707 Cyrus Add */
void refresh_screen(int,int);										/*20170707 Cyrus Add */
void init_field(FIELD *, int *);									/*20170707 Cyrus Add */
void disp_err(char *);												/*20170707 Cyrus Add */
void disp_space(int,int,int);										/*20170707 Cyrus Add */
void hide_cursor();													/*20170707 Cyrus Add */
int get_subdist_stn(char *);



/* field validation function declarations */
int    chk_freq();
int    chk_cull_freq();
int    chk_sub_dist();
int    chk_cs_option();

/* For field definitions, see ../include/screen.h */
FIELD item[] = 
{
	{5, 23,FREQ,       TRUE, 11,5, NEW, "00000.00000","",0,FALSE,chk_freq},
	{6, 23,FREQ,       TRUE, 11,5, NEW, "00000.00000","",0,FALSE,chk_freq},
	{7, 23,STRING,     TRUE, 3, 0, NEW, "   ",        "",0,FALSE,chk_sub_dist},
	{6, 68,FREQ,       FALSE,8, 5, NEW, "3.0     ",   "3.0",0,FALSE,chk_cull_freq},
	{7, 68,FREQ,       FALSE,8, 5, NEW, "3.0     ",   "3.0",0,FALSE,chk_cull_freq},
	{19,32,INTEGER,    TRUE, 1, 0, NEW, " ",          "",0,FALSE,chk_cs_option},
	{-1,-1,DUMMY,      TRUE, 0, 0, NEW, "",           "",0,FALSE,(int(*)())NULL}
};

/*
char   *item_err_msg[] = 
{
   "Freq. not in multiple of min. channel separation",
   "Freq. not in multiple of min. channel separation",
   "Invalid sub-district code or not hill-top site",
   "Invalid option",
   ""
};
*/
     
char   *prog_id = "emcs0f_01";
char   *screen_head = "CO-SITE ANALYSIS FOR HILL-TOP SITE";

char   passwd[20];
char   emc_uid[20];
char   sub_district[4];

double prop_tx_freq, prop_rx_freq;
int    tx_mode, freq_band;

/*****************************************************/
/*           Variables for summary totals            */
/*****************************************************/
int    intmod2_vict_tot;
int    intmod2_tx1_tot;
int    intmod2_tx2_tot;
int    intmod3_vict_tot;
int    intmod3_tx1_tot;
int    intmod3_tx3_tot;

char            *getenv();
extern double   atof();

extern char msg[];

EXEC SQL BEGIN DECLARE SECTION;

    char     o_dist_type;
    char     o_station_type;
    char     o_act_flag;
    double   o_tx_freq;
    double   o_rx_freq;
    char     o_pw_sign;
    float    o_pw_dbw;
    int      o_base_no;
    int      o_east;
    int      o_north;
    int      o_ant_gain;
    int      o_ant_height;
    char     o_sys_category;
    VARCHAR  o_sys_type[3];
    VARCHAR  o_sys_no[8];
    VARCHAR  o_sys_suffix[4];
    VARCHAR  o_subdistrict[4];
    VARCHAR  o_cancel_date[10];

EXEC SQL END DECLARE SECTION;

EXEC SQL INCLUDE SQLCA;


int main(argc, argv)
int    argc;
char   **argv;
{
    char    cs_fname[150];
    char    err_msg[80];
    int     term_code;
    int     item_cnt;                   /* total no. of items               */
    int     last_item;                  /* index of last filled-up item     */
    int     x_pos, y_pos;               /* current cursor position          */
    int     curr_pos;                   /* current position of input string */
    int     loop;                       /* TRUE if entry continue on        */
                                        /* current item                     */
    int     modified = FALSE;           /* TRUE when current item has been  */
                                        /* modified                         */
    int     token;                      /* user-input character             */
    int     status = !(QUIT);
    int     direction;                  /* field shuttle direction          */
    int     err_flag = FALSE;
    int     first_select = TRUE;        /* TRUE when cosite data is freshly */
                                        /* input                            */
    int     option;
    int     i;
    register int     j;
    struct tm  *tt;
char s[80];


    EXEC SQL WHENEVER NOT FOUND CONTINUE;

    initscr();
    raw();
    noecho();
    clear();
    keypad(stdscr, TRUE); 

    if (argc != 6)
    {
       sprintf(err_msg, "Usage: esemcs0f user_id passwd emc_uid -P printer_id");
       goto force_exit;
    }

    if (strcmp(argv[4], "-P"))
    {
       sprintf(err_msg, "Usage: esemcs0f user_id passwd emc_uid -P printer_id");
       goto force_exit;
    }
    
    strcpy(user_id, argv[1]);
    strcpy(passwd, argv[2]);

    if (user_login(user_id, passwd, err_msg) != LOGIN_OK)
    {
        strip_blank(err_msg, TRAIL);
        strcat(err_msg, "  ");
        strcat(err_msg, user_id);
        strcat(err_msg, passwd);
        goto force_exit;
    }

    EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn;

    strcpy(emc_uid, argv[3]);
    strcpy(printer_id, argv[5]);
    get_sys_date_time(sys_date, sys_time, yymmdd, hhmmss);

    for (i = 0; item[i].xpos != -1; i++)
        ;

    item_cnt = i;

    emc_dir = getenv("EMC");
    
    disp_heading(prog_id, screen_head, sys_date);
    disp_entry_scn(item_cnt, item_cnt, NEW_SCREEN);
 
    for (i = 0; status != QUIT; )
    {
        x_pos = item[i].xpos; 
        y_pos = item[i].ypos;

        /**********************************************************/
        /* if data type of current field is sign field, then skip */
        /* the 1st position of current string and increment       */
        /* screen x-coordinate by 1, else keep 1st position       */
        /**********************************************************/
        if (is_sign(&item[i]))
        {
            curr_pos = 1;
            x_pos++;
        }
        else
            curr_pos = 0;
        move(y_pos, x_pos);
        loop = TRUE;

        for (; loop == TRUE;)
        {
            refresh();
            token = getch();

            if (err_flag)
            {
                err_flag = FALSE;
                attrset(A_NORMAL);
                getyx(stdscr, y_pos, x_pos);
                clear_err();
                move(y_pos, x_pos);
                attrset(A_REVERSE);
            }

            /**************************************/
            /* Function key definitions:          */
            /*    KEY_F(1)  - quit session        */
            /*    KEY_F(3)  - refresh screen      */
            /*    KEY_F(4)  - clear field         */
            /*    TAB,                            */
            /*    NEWLINE,                        */
            /*    KEY_DOWN  - next field          */
            /*    CTRL_B,                         */
            /*    KEY_UP    - next field          */
            /*    KEY_LEFT  - next char position  */
            /*    KEY_RIGHT - next char position  */
            /*    DELETE,                         */
            /*    BACKSPACE - delete current char */
            /*                and back 1 position */
            /**************************************/

            switch (token)
            {
                case KEY_F(1):
                    status = QUIT;
                    loop = FALSE;
                    break;
/*
                case KEY_F(2):
                    confirm = TRUE;
                    loop = FALSE;
                    break;
*/
                case KEY_F(3):
                    attroff(A_REVERSE);
                    refresh_screen(item_cnt, i);
                    move(y_pos, x_pos);
                    break; 

                case KEY_F(4):
                    init_field(&item[i], &curr_pos);
                    loop = FALSE; 
                    direction = RESTART; 
                    break; 

                case KEY_UP:
                case CTRL_B:
                    if (i == 0)
                        beep();
                    else
                    {
                        loop = FALSE;
                        direction = BACKWARD;
                    }
                    break;

                case KEY_DOWN:
                case NEWLINE:
                case TAB:
/*
sprintf(s," i len req: %d %d %d", i, item[i].curr_len, item[i].required);
mvaddstr(23,0,s);
                    if ((item[i].state == NEW) && (item[i].required))
                    if ((is_sign(&item[i]) && (item[i].curr_len == 1))
                     || (!is_sign(&item[i]) && empty(&item[i]))
                    && (item[i].required))
*/
                    /*********************************************************/
                    /* beep user if he wants to skip an input-required field */
                    /*********************************************************/
                    if (is_sign(&item[i]))
                    {
                        if ((item[i].curr_len == 1) && (item[i].required))
                        {
                            err_flag = TRUE;
                            disp_err(INPUT_REQ_MSG);
                            attron(A_REVERSE);
                            break;
                        }
                    }
                    else
                        if (empty(&item[i]) && (item[i].required))
                        {
                            err_flag = TRUE;
                            disp_err(INPUT_REQ_MSG);
                            attron(A_REVERSE);
                            break;
                        }

                    if (empty(&item[i]) 
                    ||  (is_sign(&item[i]) && (item[i].curr_len == 1)))
                        strcpy(item[i].curr_str, item[i].init_str);

                    loop = FALSE;
                    direction = FORWARD;
                    break;

                case KEY_LEFT:

                    /************************************/
                    /* beep user if he has reached the  */
                    /* left-most position of a field    */
                    /************************************/
                    if (is_sign(&item[i]))
                        if (x_pos == item[i].xpos + 1)
                        {
                            beep();
                            break;
                        }

                    if (x_pos == item[i].xpos)
                        beep();
                    else
                    {
                        x_pos--; curr_pos--;
                    }
                    break;

                case KEY_RIGHT:

                    /************************************/
                    /* beep user if he has reached the  */
                    /* right boundary of current field  */
                    /************************************/
                    if (x_pos - item[i].xpos == item[i].curr_len)
                        beep();
                    else
                    {
                        x_pos++; curr_pos++;
                    }
                    break;

                case BACKSPACE:
                case DELETE:

                    /************************************/
                    /* beep user if he has reached the  */
                    /* left-most position of a field    */
                    /************************************/
                    if (is_sign(&item[i]))
                        if (x_pos == item[i].xpos + 1)
                        {
                            beep();
                            break;
                        }

                    if (x_pos == item[i].xpos)
                        beep();
                    else
                    {
                        x_pos--; curr_pos--;
                        if (is_float(&item[i]) 
                        && (item[i].curr_str[curr_pos] == DOT))
                            item[i].has_dot = FALSE;
                        move(y_pos, x_pos);
                        addch(BLANK);
                        modified = TRUE;
                    }
                    break;

                case DOT:

                    /******************************************************/
                    /* beep user if data type of current field is integer */
                    /******************************************************/
                    if (is_int(&item[i]))
                    {
                        beep();
                        break;
                    }


                    /********************************************************/
                    /* beep user if data type of current field is float and */
                    /* user has already input one dot                       */
                    /********************************************************/
                    if (is_float(&item[i]))
                        if (item[i].has_dot)
                        {
                            beep();
                            break;
                        }
                        else
                            item[i].has_dot = TRUE;

                    if (item[i].state == NEW)
                        if (is_sign(&item[i]))
                        {
                            if (curr_pos == 1)
                            {
                                disp_space(y_pos, x_pos, item[i].fld_len - 1);
                                move(y_pos, x_pos);
                            }
                        }
                        else
                            if (curr_pos == 0)
                            {
                                disp_space(y_pos, x_pos, item[i].fld_len);
                                move(y_pos, x_pos);
                            }

                    addch(token);
                    item[i].curr_str[curr_pos] = token;
                    x_pos++; curr_pos++;
                    modified = TRUE;
                    item[i].state = MODIFIED;

                    if (x_pos - item[i].xpos == item[i].fld_len)
                    {
                        item[i].curr_str[item[i].fld_len] = '\0';
                        item[i].curr_len = item[i].fld_len;
                        loop = FALSE;
                        direction = FORWARD;
                    }

                    break;

                default:
                    if ((!isalnum(token)) && (token != MINUS) 
                    &&  (token != PLUS))
                    {
                        beep();
                        break;
                    }

                    if (!isdigit(token))
                    {
/*
mvaddstr(23, 0, "not digit");
*/
                        if (is_sign(&item[i]))
                        {
                            if ((token != MINUS) && (token != PLUS))
                            {
                                beep();
                                break;
                            }
                            else
                                if (curr_pos > 1)
                                {
                                    beep();
                                    break;
                                }
                        }
                        else
/*
sprintf(s, "should not be integer: %d %d %s", i, item[i].type, item[i].init_str);
mvaddstr(22, 0, s);
*/
                            if ((item[i].type != CHAR) 
                            &&  (item[i].type != STRING))
                            {
                                beep();
                                break;
                            }
                            else
                                if (isalpha(token))
                                    token = toupper(token); 
                    }
                    else
                        if (item[i].state == NEW)
                            if (is_sign(&item[i]))
                            {
                                if (curr_pos == 1)
                                {
                                    disp_space(y_pos, x_pos, item[i].fld_len-1);
                                    move(y_pos, x_pos);
                                }
                            }
                            else
                                if (curr_pos == 0)
                                {
                                    disp_space(y_pos, x_pos, item[i].fld_len);
                                    move(y_pos, x_pos);
                                }

                    if (((token == MINUS) || (token == PLUS)) 
                    &&  is_sign(&item[i]))
                    {
                        x_pos--; curr_pos--;  /* because we don't want to  */
                                              /* move cursor to 1 position */
                                              /* this statement is used    */
                                              /* to complement the         */
                                              /* 'x_pos++; curr_pos++'     */
                                              /* a few lines below         */
                        if (token == MINUS)
                        {
                            item[i].curr_str[0] = MINUS;
                            move(y_pos, x_pos);
                            addch(MINUS);
                        }
                            
                    }
                    else
                    {
                        item[i].curr_str[curr_pos] = token;
                        addch(token);
                    }

/*
sprintf(s, "modified");
mvaddstr(22, 0, s);
*/
                    if ((token != MINUS) && (token != PLUS))
                        item[i].state = MODIFIED;

                    modified = TRUE;
                    x_pos++; curr_pos++;

                    if (x_pos - item[i].xpos == item[i].fld_len)
                    {
                        item[i].curr_str[item[i].fld_len] = '\0';
                        item[i].curr_len = item[i].fld_len;
                        loop = FALSE;
                        direction = FORWARD;
                    }
                
                    break;
            }

            refresh();
            if (loop == FALSE)
            {
                int    (*check_item)();

                check_item = item[i].validate;
                if (check_item != (int(*)())NULL)
                {
                    if ((direction != BACKWARD) && (!empty(&item[i])))
                        if ((*check_item)(&i, err_msg) == ERROR)
                        {
                            err_flag = TRUE;
                            disp_err(err_msg);
                            attron(A_REVERSE);
                            loop = FALSE;
                            direction = RESTART;
                        }
                }

                switch (direction)
                {
                    case FORWARD:
                        i++;
                        break;
                    case BACKWARD:
                        i--;
                        break;
                    case RESTART:
                        break;
                }

                continue;
            }
            
            if (modified && (!err_flag))
            {
                int    len = x_pos - item[i].xpos;
   
                if ((item[i].curr_len < len) || is_delete(token))
                {
                    item[i].curr_len = len;
                    item[i].curr_str[item[i].curr_len] = '\0';
                }
/*
disp_space(22,0,80);
sprintf(s,"i mode: %d %d", i, tx_mode);
mvaddstr(22,0,s);
*/

                modified = FALSE;
            }
  
            move(y_pos, x_pos);
        }

        if ((i == item_cnt) && (!err_flag))
        {
            option = atoi(item[i-1].curr_str);
            if (option == 0)
            {
                i = 0;
                for (j = 0; j < item_cnt; j++)
                    init_field(&item[j], &curr_pos);
                first_select = TRUE;
            }
            else
            {
                i = NEXT_START_PT;
                hide_cursor();
                if (first_select)
                {
                    prop_tx_freq = atof(item[0].curr_str);
                    prop_rx_freq = atof(item[1].curr_str);
                    strcpy(sub_district,item[2].curr_str);
                }
                switch (option)
                {
                    case 1 :
                    case 2 :

                        /**********************************************/
                        /* select sub-district stations only when the */
                        /* co-site data is freshly input              */
                        /**********************************************/
                        if (first_select)
                        {
                            /*if (get_subdist_stn(item[2].curr_str, err_msg)   20170711 Cyrus remove  item[2].curr_str*/
							if (get_subdist_stn(err_msg)								
                                == ERROR)
                                goto force_exit;
                            first_select = FALSE;
                        }

                        if (option == 1)
                        {
                            intermod2_cull_freq = atof(item[3].curr_str);
                            intermod3_cull_freq = atof(item[4].curr_str);
                            intmod2_vict_tot = 0;
                            intmod2_tx1_tot = 0;
                            intmod2_tx2_tot = 0;
                            intmod3_vict_tot = 0;
                            intmod3_tx1_tot = 0;
                            intmod3_tx3_tot = 0;
                            set_band_mode2(&tx_mode,
                                           &freq_band,
                                           prop_tx_freq,
                                           prop_rx_freq);
                            centre_msg(23, "2-signal intermod ...  ",
                                       A_BLINK, A_REVERSE);
                            intermod_2();
                            centre_msg(23, "3-signal intermod ...  ",
                                       A_BLINK, A_REVERSE);
                            intermod_3();
                            if (print_intermod_summary(err_msg) == ERROR)
                                goto force_exit;
                        }
                        else
                        {
                            int    status;

                            centre_msg(23, " Generating report ... ",
                                       A_BLINK, A_REVERSE);
                            status = sub_district_stn(err_msg);
                            if (status == ERROR)
                                goto force_exit;
                            if (status == NOT_FOUND)
                            {
                                disp_err(err_msg);
                                getch();
                            }
                        }
                        break;

                    case 3 :
                        if (reprint_substn_rpt() == ERROR)
                        {
                            disp_err(REPRINT_ERROR);
                            getch();
                        }
                        break;

                    case 4 :
                        if (reprint_intermod2_rpt() == ERROR)
                        {
                            disp_err(REPRINT_ERROR);
                            getch();
                        }
                        break;

                    case 5 :
                        if (reprint_intermod3_rpt() == ERROR)
                        {
                            disp_err(REPRINT_ERROR);
                            getch();
                        }
                        break;

                    case 6 :
                        if (reprint_intermod_summary() == ERROR)
                        {
                            disp_err(REPRINT_ERROR);
                            getch();
                        }
                        break;

                }

                show_cursor();
                attrset(A_NORMAL);
                disp_space(23, 0, 80);
                refresh();
                attrset(A_REVERSE);
                
                for (j = NEXT_START_PT; j < item_cnt; j++)
                    init_field(&item[j], &curr_pos);
                beep();
            }
        }
    }

    attroff(A_BOLD);
    clear();
    endwin();
    user_logout();
    exit(0);


sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);

force_exit:
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    getch(); clear(); show_cursor(); refresh();
    endwin();
    exit(1);
}


/**************************************************************/
/* Display new entry screen or refresh current screen to user */
/**************************************************************/

disp_entry_scn(item_cnt, curr_cnt, mode)
int    item_cnt;
int    curr_cnt;
int    mode;
{
    char    tmp_str[80];
    register int    i;

    attrset(A_NORMAL);
    mvaddstr(5,  7,  "TX FREQ (MHz) :");
    mvaddstr(6,  7,  "RX FREQ (MHz) :");
    mvaddstr(7,  7,  "HILL-TOP CODE :");
    attrset(A_UNDERLINE);
    mvaddstr(4,  42, "CULLING FREQUENCY LIMITS");
    attrset(A_NORMAL);
    mvaddstr(6,  42, "2-SIGNAL INTERMOD (MHz) :");
    mvaddstr(7,  42, "3-SIGNAL INTERMOD (MHz) :");
    mvaddstr(10, 15, "0.   ANOTHER SET OF CO-SITE DATA");
    mvaddstr(12, 15, "1.   COMBINED SIGNALS INTERMODULATION ANALYSIS");
    mvaddstr(13, 15, "2.   SUB-DISTRICT STATION REPORTING"); 
    mvaddstr(14, 15, "3.   SUB-DISTRICT STATION REPORT REPRINTING"); 
    mvaddstr(15, 15, "4.   2-SIGNAL INTERMODULATION REPORT REPRINTING");
    mvaddstr(16, 15, "5.   3-SIGNAL INTERMODULATION REPORT REPRINTING");
    mvaddstr(17, 15, "6.   COMBINED SIGNALS INTERMODULATION SUMMARY REPRINTING");
    mvaddstr(19, 15, "YOUR SELECTION : ");
 
    sprintf(tmp_str, "%s",
"F1-Quit   F3-Refresh   F4-Clear field   UP-Prev   DOWN/<Tab>-next");
 /*
    sprintf(tmp_str, "%s",
"F1-Quit   F2-Confirm   F3-Refresh   F4-Clear field   UP-Prev   DOWN/<Tab>-next");
*/
    centre_msg(21, tmp_str, A_BOLD, A_REVERSE);

    if (mode == REFRESH)
    {
        for (i = 0; i < curr_cnt; i++)
        {
            disp_space(item[i].ypos, item[i].xpos, item[i].fld_len);
            mvaddstr(item[i].ypos, item[i].xpos, item[i].curr_str);
        }
        for (i = curr_cnt; i < item_cnt; i++)
            if (item[i].state == NEW)
                mvaddstr(item[i].ypos, item[i].xpos, item[i].init_str);
            else
            {
                disp_space(item[i].ypos, item[i].xpos, item[i].fld_len);
                mvaddstr(item[i].ypos, item[i].xpos, item[i].curr_str);
            }
    }
    else
        for (i = 0; i < item_cnt; i++)
            mvaddstr(item[i].ypos, item[i].xpos, item[i].init_str);

    refresh();

}


/**************************************************************/
/* call 'disp_entry_scn' to refresh current screen to user    */
/**************************************************************/

refresh_screen(item_cnt, curr_cnt)
int item_cnt;
int curr_cnt;
{
    clear();
    disp_heading(prog_id, screen_head, sys_date);
    disp_entry_scn(item_cnt, curr_cnt, REFRESH);
}


/*************************************************/
/* initialise current field to its initial value */
/*************************************************/

init_field(p_item, curr_pos)
FIELD *p_item;
int   *curr_pos;
{
    mvaddstr(p_item->ypos, p_item->xpos, p_item->init_str);
    if (is_sign(p_item))
    {
        p_item->curr_len = *curr_pos = 1;
        p_item->curr_str[1] = '\0';
    }
    else
    {
        p_item->curr_len = *curr_pos = 0;
        p_item->curr_str[0] = '\0';
    }

    if (!disp_only(p_item))
    {
        p_item->state = NEW;
        if (is_float(p_item))
            p_item->has_dot = FALSE;
    }
}


/**************************************************************/
/* check input frequency                                      */
/**************************************************************/

int    chk_freq(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{
    double  freq;
    long    i_freq;

    freq = atof(item[*curr_cnt].curr_str);
    if ((freq - MAX_FREQ) > FREQ_EPSILON)
    {
        sprintf(err_msg, "Frequency must be between 0 and %-11.5lf", MAX_FREQ);
        return ERROR;
    }

    freq = freq * FREQ_MULTIPLE;
    i_freq = (long)(freq + .5);

    if (abs(freq - i_freq) > FREQ_EPSILON)
    {
        sprintf(err_msg, 
                "Frequency not in multiple of min. channel separation");
        return ERROR;
    }

    if (i_freq % CHANNEL_SEP)
    {
        sprintf(err_msg, 
                "Frequency not in multiple of min. channel separation");
        return ERROR;
    }

    return OK;
}


/**************************************************************/
/* check input culling frequency                              */
/**************************************************************/

int    chk_cull_freq(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{
    double  freq;
    long    i_freq;

    freq = atof(item[*curr_cnt].curr_str);
    if ((freq - MAX_CULL_FREQ) > FREQ_EPSILON)
    {
        sprintf(err_msg, "Frequency must be between 0 and %-8.5lf",
                MAX_CULL_FREQ);
        return ERROR;
    }

    freq = freq * FREQ_MULTIPLE;
    i_freq = (long)(freq + .5);

    if (abs(freq - i_freq) > FREQ_EPSILON)
    {
        sprintf(err_msg, 
                "Frequency not in multiple of min. channel separation");
        return ERROR;
    }

    if (i_freq % CHANNEL_SEP)
    {
        sprintf(err_msg, 
                "Frequency not in multiple of min. channel separation");
        return ERROR;
    }

    return OK;
}


/**********************************************************************/
/* check that input sub-district code can be found in SUBDISTRICT_TAB */
/**********************************************************************/

int    chk_sub_dist(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{
/*    strcpy(o_subdistrict.arr, item[*curr_cnt].curr_str);
    o_subdistrict.len = strlen(o_subdistrict.arr); */
    strcpy((char *)o_subdistrict.arr, item[*curr_cnt].curr_str);
    o_subdistrict.len = strlen((char *)o_subdistrict.arr);
    
/*
system("echo \"esemcs0f 1\" >> /tmp/debug");
sprintf(msg, "echo \"%s\" >> /tmp/debug", o_subdistrict.arr );
system(msg);
*/

    EXEC SQL
         SELECT DISTRICT_TYPE
         INTO   :o_dist_type
         FROM   SUBDISTRICT_TAB
         WHERE  SUBDISTRICT = :o_subdistrict;

    if (sqlca.sqlcode == NOT_FOUND)
    {
        sprintf(err_msg, "Invalid sub-district code or not hill-top site");
        return ERROR;
    }

    if (o_dist_type != HILL_TOP_SITE)
    {
        sprintf(err_msg, "Invalid sub-district code or not hill-top site");
        return ERROR;
    }

    return OK;

sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    refresh(); beep();
    sleep(1); clear(); refresh();
    endwin();
    exit(1);
}


/********************************************************************/
/* check that input co-site analysis option must be < COSITE_OPTION */
/********************************************************************/

int    chk_cs_option(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{
    int    option;

    option = atoi(item[*curr_cnt].curr_str);
    if (option > COSITE_OPTION)
    {
        sprintf(err_msg, "Invalid option");
        return ERROR;
    }

    return OK;
}

    
/****************************************************************/
/* select from STATION stations that have the same sub-district */
/* code as the user-input sub-district code                     */
/****************************************************************/

int get_subdist_stn(err_msg)
char   *err_msg;
{
    register int   i, j, gap;
    EXIST_FREQ     *tmp_list;

char s[80];

    centre_msg(23, "Selecting stations ...", A_BLINK, A_REVERSE);
    EXEC SQL DECLARE C02 CURSOR FOR
         SELECT SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO,
                STATION_TYPE, EAST, NORTH, NVL(PW_SIGN, ' '), NVL(PW_DBW, 0.0),
                NVL(TO_CHAR(CANCEL_DATE), '-'), GAIN, HALT
         FROM   STATION
         WHERE  SUBDISTRICT = :o_subdistrict
         ORDER  BY LIC_TYPE, LIC_NO;

    EXEC SQL OPEN C02;

    EXEC SQL
         FETCH C02
         INTO  :o_sys_category, :o_sys_type, :o_sys_no, :o_sys_suffix,
               :o_base_no, :o_station_type, :o_east, :o_north,
               :o_pw_sign, :o_pw_dbw, :o_cancel_date, :o_ant_gain,
               :o_ant_height;

    if (sqlca.sqlcode == NOT_FOUND)
    {
        sprintf(err_msg, "No stations within %s found, prees any to exit", 
                o_subdistrict.arr);
        EXEC SQL CLOSE C02;
        return ERROR;
    }

    fq_cnt = 0;

    for (i = 0; i < MAX_EXIST; )
    {
        /*************************************************************/
        /*  if this is a cancelled station (CANCEL_DATE is not NULL) */
        /*  skip it                                                  */
        /*************************************************************/
/*
        if ((o_east == 0) || (o_north == 0))
*/
        if (o_cancel_date.arr[0] != '-')
        {
            EXEC SQL
                 FETCH C02
                 INTO  :o_sys_category, :o_sys_type, :o_sys_no, :o_sys_suffix,
                       :o_base_no, :o_station_type, :o_east, :o_north,
                       :o_pw_sign, :o_pw_dbw, :o_cancel_date, :o_ant_gain,
                       :o_ant_height;

            if (sqlca.sqlcode == NOT_FOUND)
            {
                EXEC SQL CLOSE C02;
                break;
            }

            continue;
        }

        if ((exist[i] = (EXIST *) malloc(sizeof(EXIST))) == (EXIST *) NULL)
        {
            sprintf(err_msg, 
                    "Cannot allocate space for 'exist', press any key to exit");
            return ERROR;
        }

        o_sys_type.arr[o_sys_type.len] = '\0';
        o_sys_no.arr[o_sys_no.len] = '\0';
        o_sys_suffix.arr[o_sys_suffix.len] = '\0';

        EXEC SQL DECLARE C03 CURSOR FOR
             SELECT NVL(TX_FREQ, 0.0), NVL(RX_FREQ, 0.0), UPPER(ACT_FLAG)
             FROM   BASE_CH
             WHERE  SYS_CATEGORY = :o_sys_category
             AND    SYS_TYPE = :o_sys_type
             AND    SYS_NO = :o_sys_no
             AND    SYS_SUFFIX = :o_sys_suffix
             AND    BASE_NO = :o_base_no
             ORDER BY TX_FREQ;

        EXEC SQL OPEN C03;

        EXEC SQL
             FETCH C03
             INTO  :o_tx_freq, :o_rx_freq, o_act_flag;

        if (sqlca.sqlcode == NOT_FOUND)
        {
            sprintf(err_msg, "No BASE_CH record for %c-%s-%s-%s base no. %d",
                    o_sys_category, o_sys_type.arr, o_sys_no.arr,
                    o_sys_suffix.arr, o_base_no);
            sprintf(err_msg, "%s, press any to continue", err_msg);
            EXEC SQL CLOSE C03;
            EXEC SQL CLOSE C02;
            return ERROR;
        }

        for ( ; ; )
        {
            fq_list[fq_cnt] = (EXIST_FREQ *) malloc(sizeof(EXIST_FREQ));
            s_fq_list[fq_cnt] = (EXIST_FREQ *) malloc(sizeof(EXIST_FREQ));
            if ((fq_list[fq_cnt] == (EXIST_FREQ *) NULL)
            ||  (fq_list[fq_cnt] == (EXIST_FREQ *) NULL))
            {
                sprintf(err_msg, 
                "Cannot allocate space for fq_list/s_fq_list, press any key to exit");
                return ERROR;
            }
            fq_list[fq_cnt]->tx_freq = o_tx_freq;
            fq_list[fq_cnt]->rx_freq = o_rx_freq;
            fq_list[fq_cnt]->tx_channel = (int)(o_tx_freq/MIN_CHANNEL_SEP + .5);
            fq_list[fq_cnt]->rx_channel = (int)(o_rx_freq/MIN_CHANNEL_SEP + .5);
            fq_list[fq_cnt]->act_flag = 
                (o_act_flag == 'N')? o_act_flag : ' ';
            fq_list[fq_cnt]->stn_node = i;
            s_fq_list[fq_cnt]->tx_freq = o_tx_freq;
            s_fq_list[fq_cnt]->rx_freq = o_rx_freq;
            s_fq_list[fq_cnt]->tx_channel = 
                (int)(o_tx_freq / MIN_CHANNEL_SEP + .5);
            s_fq_list[fq_cnt]->rx_channel = 
                (int)(o_rx_freq / MIN_CHANNEL_SEP + .5);
            s_fq_list[fq_cnt]->stn_node = i;
            fq_cnt++;

            if (fq_cnt == (MAX_EXIST*2 + 500))
            {
                sprintf(err_msg, "No. of channels exceeds %d\n", (MAX_EXIST*2 + 500));
                EXEC SQL CLOSE C02;
                EXEC SQL CLOSE C03;
                return ERROR;
            }

            EXEC SQL
                 FETCH C03
                 INTO  :o_tx_freq, :o_rx_freq;

            if (sqlca.sqlcode == NOT_FOUND)
            {
                EXEC SQL CLOSE C03;
                break;
            }
        }

        exist[i]->sys_category = o_sys_category;
/*        strcpy(exist[i]->sys_type,  o_sys_type.arr);
        strcpy(exist[i]->sys_no,  o_sys_no.arr);
        strcpy(exist[i]->sys_suffix,  o_sys_suffix.arr); */
        strcpy(exist[i]->sys_type,  (char *)o_sys_type.arr);
        strcpy(exist[i]->sys_no,  (char *)o_sys_no.arr);
        strcpy(exist[i]->sys_suffix,  (char *)o_sys_suffix.arr);

        exist[i]->base_no = o_base_no;
        exist[i]->station_type = o_station_type;
        exist[i]->east_grid = o_east;
        exist[i]->north_grid = o_north;
        exist[i]->ant_gain = o_ant_gain;
        exist[i]->ant_height = o_ant_height;
/*
sprintf(s,"east north: %d %d", exist[i]->east_grid, exist[i]->north_grid);
mvaddstr(23,0,s);refresh();getch();
*/

        exist[i]->pw_dbw = (o_pw_sign == MINUS) ? (-1 * o_pw_dbw) : o_pw_dbw;
        i++;

        EXEC SQL
             FETCH C02
             INTO  :o_sys_category, :o_sys_type, :o_sys_no, :o_sys_suffix,
                   :o_base_no, :o_station_type, :o_east, :o_north,
                   :o_pw_sign, :o_pw_dbw, :o_cancel_date, :o_ant_gain,
                   :o_ant_height;

        if (sqlca.sqlcode == NOT_FOUND)
            break;
    }

    EXEC SQL CLOSE C02;

    if (i == MAX_EXIST)
    {
        sprintf(err_msg, "No. of stations exceeds %d\n", MAX_EXIST);
        return ERROR;
    }

    cull_stn_cnt = i;

/**********************************************************************/
/*  sort all channels in ascending order using shell sort             */
/**********************************************************************/

    for (gap = fq_cnt / 2; gap > 0; gap /= 2)
        for (i = gap; i < fq_cnt; i++)
            for (j = i - gap; 
                (j >= 0) && (s_fq_list[j]->rx_freq > s_fq_list[j+gap]->rx_freq);
                 j -= gap)
            {
                tmp_list         = s_fq_list[j];
                s_fq_list[j]     = s_fq_list[j+gap];
                s_fq_list[j+gap] = tmp_list;
            }

    return OK;

sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    refresh(); beep();
    sleep(1); clear(); refresh();
    endwin();
    exit(1);
}


/***************************************/
/*    print intermod summary report    */
/***************************************/

print_intermod_summary(err_msg)
char   *err_msg;
{
    char    sfname[120];
    char    cmdline[150];
    FILE    *sfp;

    sprintf(sfname, "%s/cosite/summary/%s.%.5lf.%s", 
            emc_dir, emc_uid, prop_tx_freq, sub_district);
    if ((sfp = fopen(sfname, "w")) == (FILE *) NULL)
    {
        sprintf(err_msg, "Fail to open summary file, press any to exit");
        return ERROR;
    }

    fprintf(sfp, "RUN DATE: %s%14s", sys_date, "");
    fprintf(sfp,
           "*************************************************************"); 
    fprintf(sfp, "%22sPAGE   : 1\n", "");
    fprintf(sfp, "RUN TIME: %s%14s", sys_time, "");
    fprintf(sfp,
           "*                                                           *");
    fprintf(sfp, "%22sPROGRAM: esemcs0f\n", "");
    fprintf(sfp, "USER ID : %-19s%3s", emc_uid, "");
    fprintf(sfp,
      "*             CO-SITE ANALYSIS - INTERMODULATION            *\n");
    fprintf(sfp, "%32s", "");
    fprintf(sfp, 
      "*                       SUMMARY   LOG                       *\n");
    fprintf(sfp, "%32s*%59s*\n", "", "");
    fprintf(sfp, "%32s*%21sSUB-DISTRICT : %-3s%20s*\n", 
            "", "", sub_district, "");
    fprintf(sfp, "%32s*%59s*\n", "", "");
    fprintf(sfp, 
            "%32s*  RX FREQ (MHz) : %10.4lf   TX FREQ (MHz) : %10.4lf  *\n",
            "", prop_rx_freq, prop_tx_freq);
    fprintf(sfp, "%32s*%59s*\n", "", "");
    fprintf(sfp, "%32s", "");
    fprintf(sfp, 
            "*************************************************************\n");
    fprintf(sfp, "\n\n\n");
    fprintf(sfp, "** 2-signal Intermodulation Report");
    fprintf(sfp, "(Proposed Station as Victim)\n");
    fprintf(sfp, "   Number of Combinations : %d\n\n", intmod2_vict_tot);
    fprintf(sfp, "** 2-signal Intermodulation Report");
    fprintf(sfp, "(Proposed Station as Tx1)\n");
    fprintf(sfp, "   Number of Combinations : %d\n\n", intmod2_tx1_tot);
    fprintf(sfp, "** 2-signal Intermodulation Report");
    fprintf(sfp, "(Proposed Station as Tx2)\n");
    fprintf(sfp, "   Number of Combinations : %d\n\n\n", intmod2_tx2_tot);
    fprintf(sfp, "** 3-signal Intermodulation Report");
    fprintf(sfp, "(Proposed Station as Victim)\n");
    fprintf(sfp, "   Number of Combinations : %d\n\n", intmod3_vict_tot);
    fprintf(sfp, "** 3-signal Intermodulation Report");
    fprintf(sfp, "(Proposed Station as Tx1)\n");
    fprintf(sfp, "   Number of Combinations : %d\n\n", intmod3_tx1_tot);
    fprintf(sfp, "** 3-signal Intermodulation Report");
    fprintf(sfp, "(Proposed Station as Tx3)\n");
    fprintf(sfp, "   Number of Combinations : %d\f", intmod3_tx3_tot);
    fclose(sfp);

/*
    sprintf(cmdline, "cat %s >> xxx", sfname);
*/
    sprintf(cmdline, "lp -dprinter1 %s > /dev/null",sfname);
    system(cmdline);
    return OK;
}
    

/***************************************/
/* reprint sub-district station report */
/***************************************/

reprint_substn_rpt()
{
    char   cs_fname[120];
    char   cmdline[150];
    FILE   *ifp;

    sprintf(cs_fname, "%s/cosite/substn/%s.%s", 
            emc_dir, emc_uid, sub_district);
    if ((ifp = fopen(cs_fname, "r")) == (FILE *)NULL)
        return ERROR;
    fclose(ifp);
    centre_msg(23, "Printing ...", A_BLINK, A_REVERSE);
/*
    sprintf(cmdline, "cat %s >> xxx", cs_fname);
*/
    sprintf(cmdline, "lp -dprinter1 %s > /dev/null", cs_fname);
    system(cmdline);
    return OK;
}


/************************************/
/* reprint 2-signal intermod report */
/************************************/

reprint_intermod2_rpt()
{
    char   im_fname[120];
    int    found = FALSE;

    sprintf(im_fname, "%s/cosite/intermod2/%s.%.5lf.%s.v", 
            emc_dir, emc_uid, prop_tx_freq, sub_district);
    if (reprint_report(im_fname) == OK)
        found = TRUE;
    sprintf(im_fname, "%s/cosite/intermod2/%s.%.5lf.%s.t1", 
            emc_dir, emc_uid, prop_tx_freq, sub_district);
    if (reprint_report(im_fname) == OK)
        found = TRUE;
    sprintf(im_fname, "%s/cosite/intermod2/%s.%.5lf.%s.t2", 
            emc_dir, emc_uid, prop_tx_freq, sub_district);
    if (reprint_report(im_fname) == OK)
        found = TRUE;

    return ((found == TRUE) ? OK : ERROR);
}


/************************************/
/* reprint 3-signal intermod report */
/************************************/

reprint_intermod3_rpt()
{
    char   im_fname[120];
    int    found = FALSE;

    sprintf(im_fname, "%s/cosite/intermod3/%s.%.5lf.%s.v", 
            emc_dir, emc_uid, prop_tx_freq, sub_district);
    if (reprint_report(im_fname) == OK)
        found = TRUE;
    sprintf(im_fname, "%s/cosite/intermod3/%s.%.5lf.%s.t1", 
            emc_dir, emc_uid, prop_tx_freq, sub_district);
    if (reprint_report(im_fname) == OK)
        found = TRUE;
    sprintf(im_fname, "%s/cosite/intermod3/%s.%.5lf.%s.t3", 
            emc_dir, emc_uid, prop_tx_freq, sub_district);
    if (reprint_report(im_fname) == OK)
        found = TRUE;

    return ((found == TRUE) ? OK : ERROR);
}


/*****************************************/
/* actual reprint of the intermod report */
/*****************************************/

reprint_report(fname)
char   *fname;
{
    char   cmdline[150];
    FILE   *ifp;

    if ((ifp = fopen(fname, "r")) == (FILE *)NULL)
        return NOT_FOUND;
    fclose(ifp);
    centre_msg(23, "Printing ...", A_BLINK, A_REVERSE);
/*
    sprintf(cmdline, "cat %s >> xxx", fname);
*/
    sprintf(cmdline, "lp -dprinter1 %s > /dev/null", fname);
    system(cmdline);
    return OK;
}
    

/***************************************/
/* reprint intermod summary log        */
/***************************************/

reprint_intermod_summary()
{
    char   sfname[120];
    char   cmdline[150];
    FILE   *ifp;

    sprintf(sfname, "%s/cosite/summary/%s.%.5lf.%s", 
            emc_dir, emc_uid, prop_tx_freq, sub_district);
    if ((ifp = fopen(sfname, "r")) == (FILE *)NULL)
        return ERROR;
    fclose(ifp);
    centre_msg(23, "Printing ...", A_BLINK, A_REVERSE);
/*
    sprintf(cmdline, "cat %s >> xxx", sfname);
*/
    sprintf(cmdline, "lp -dprinter1 %s > /dev/null", sfname);
    system(cmdline);
    return OK;
}
