
/* Result Sets Interface */
#ifndef SQL_CRSR
#  define SQL_CRSR
  struct sql_cursor
  {
    unsigned int curocn;
    void *ptr1;
    void *ptr2;
    unsigned int magic;
  };
  typedef struct sql_cursor sql_cursor;
  typedef struct sql_cursor SQL_CURSOR;
#endif /* SQL_CRSR */

/* Thread Safety */
typedef void * sql_context;
typedef void * SQL_CONTEXT;

/* Object support */
struct sqltvn
{
  unsigned char *tvnvsn; 
  unsigned short tvnvsnl; 
  unsigned char *tvnnm;
  unsigned short tvnnml; 
  unsigned char *tvnsnm;
  unsigned short tvnsnml;
};
typedef struct sqltvn sqltvn;

struct sqladts
{
  unsigned int adtvsn; 
  unsigned short adtmode; 
  unsigned short adtnum;  
  sqltvn adttvn[1];       
};
typedef struct sqladts sqladts;

static struct sqladts sqladt = {
  1,0,0,
};

/* Binding to PL/SQL Records */
struct sqltdss
{
  unsigned int tdsvsn; 
  unsigned short tdsnum; 
  unsigned char *tdsval[1]; 
};
typedef struct sqltdss sqltdss;
static struct sqltdss sqltds =
{
  1,
  0,
};

/* File name & Package Name */
struct sqlcxp
{
  unsigned short fillen;
           char  filnam[12];
};
static const struct sqlcxp sqlfpn =
{
    11,
    "esemct0x.pc"
};


static unsigned int sqlctx = 149531;


static struct sqlexd {
   unsigned long  sqlvsn;
   unsigned int   arrsiz;
   unsigned int   iters;
   unsigned int   offset;
   unsigned short selerr;
   unsigned short sqlety;
   unsigned int   occurs;
      const short *cud;
   unsigned char  *sqlest;
      const char  *stmt;
   sqladts *sqladtp;
   sqltdss *sqltdsp;
   unsigned char  **sqphsv;
   unsigned long  *sqphsl;
            int   *sqphss;
            short **sqpind;
            int   *sqpins;
   unsigned long  *sqparm;
   unsigned long  **sqparc;
   unsigned short  *sqpadto;
   unsigned short  *sqptdso;
   unsigned int   sqlcmax;
   unsigned int   sqlcmin;
   unsigned int   sqlcincr;
   unsigned int   sqlctimeout;
   unsigned int   sqlcnowait;
            int   sqfoff;
   unsigned int   sqcmod;
   unsigned int   sqfmod;
   unsigned int   sqlpfmem;
   unsigned char  *sqhstv[4];
   unsigned long  sqhstl[4];
            int   sqhsts[4];
            short *sqindv[4];
            int   sqinds[4];
   unsigned long  sqharm[4];
   unsigned long  *sqharc[4];
   unsigned short  sqadto[4];
   unsigned short  sqtdso[4];
} sqlstm = {13,4};

/* SQLLIB Prototypes */
extern void sqlcxt (void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlcx2t(void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlbuft(void **, char *);
extern void sqlgs2t(void **, char *);
extern void sqlorat(void **, unsigned int *, void *);

/* Forms Interface */
static const int IAPSUCC = 0;
static const int IAPFAIL = 1403;
static const int IAPFTL  = 535;
extern void sqliem(unsigned char *, signed int *);

typedef struct { unsigned short len; unsigned char arr[1]; } VARCHAR;
typedef struct { unsigned short len; unsigned char arr[1]; } varchar;

/* cud (compilation unit data) array */
static const short sqlcud0[] =
{13,4242,1,0,0,
5,0,0,1,0,0,32,164,0,0,0,0,0,1,0,
20,0,0,2,0,0,32,172,0,0,0,0,0,1,0,
35,0,0,3,0,0,32,180,0,0,0,0,0,1,0,
50,0,0,4,117,0,5,190,0,0,4,4,0,1,0,1,4,0,0,1,9,0,0,1,4,0,0,1,4,0,0,
81,0,0,5,69,0,3,222,0,0,3,3,0,1,0,1,4,0,0,1,4,0,0,1,9,0,0,
108,0,0,6,0,0,30,237,0,0,0,0,0,1,0,
123,0,0,7,0,0,32,242,0,0,0,0,0,1,0,
};


/**********************************************************************/
/*                                                                    */
/*    Module Name   :  batch_traffic_update (esemct0x.pc)             */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. H. Mak                                      */
/*                                                                    */
/*    Callers       :  esemfp0f.c through 'system' statement          */  
/*                                                                    */
/*    Parameters    :  user id.                                       */
/*                  :  user password                                  */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  Read tx frequency and channel traffic from     */
/*                     'traffic.dat" to update the ORACLE table       */
/*                     CHANNEL_TRAFFIC.                               */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/


#include <stdlib.h>
#include <curses.h>
#include <string.h>
#include <stdio.h>
#include "../include/define.h"
#include "../include/emc.h"

//20180627 Cyrus add
#include <time.h>
#include <unistd.h>
#include "../include/global.h"


#ifndef ERROR
#define ERROR    1
#endif

#ifndef OK
#define OK       0
#endif

#ifndef  DUMMY
#define  DUMMY        -1
#endif

#define  TRAIL            0
#define  LEADING          1

#define  TRAFFIC_FILE     "traffic.dat"

#define  LOGIN_OK       0

#define  CTRL_Z         26
#define  DATE_LEN       8      /* date format in dd/mm/yy */


int get_sys_date_time(char *,char *,char *,char *);	                /*20180627 Cyrus Add */
int user_login    (char *,char *,char *);                           /*20180627 Cyrus Add */
int strip_blank   (char *,char *);									/*20180627 Cyrus Add */
int isnumber      (char *);                                           /*20180627 Cyrus Add */


char            *getenv();


/* EXEC SQL BEGIN DECLARE SECTION; */ 


    char    o_dummy;
    double  o_tx_freq;
    double  o_tx_freq_lo;
    double  o_tx_freq_hi;
    float   o_load_per_mobile;
    int     o_cnt;
    /* VARCHAR o_date[9]; */ 
struct { unsigned short len; unsigned char arr[9]; } o_date;


/* EXEC SQL END DECLARE SECTION; */ 


/* EXEC SQL INCLUDE SQLCA;
 */ 
/*
 * $Header: sqlca.h 24-apr-2003.12:50:58 mkandarp Exp $ sqlca.h 
 */

/* Copyright (c) 1985, 2003, Oracle Corporation.  All rights reserved.  */
 
/*
NAME
  SQLCA : SQL Communications Area.
FUNCTION
  Contains no code. Oracle fills in the SQLCA with status info
  during the execution of a SQL stmt.
NOTES
  **************************************************************
  ***                                                        ***
  *** This file is SOSD.  Porters must change the data types ***
  *** appropriately on their platform.  See notes/pcport.doc ***
  *** for more information.                                  ***
  ***                                                        ***
  **************************************************************

  If the symbol SQLCA_STORAGE_CLASS is defined, then the SQLCA
  will be defined to have this storage class. For example:
 
    #define SQLCA_STORAGE_CLASS extern
 
  will define the SQLCA as an extern.
 
  If the symbol SQLCA_INIT is defined, then the SQLCA will be
  statically initialized. Although this is not necessary in order
  to use the SQLCA, it is a good pgming practice not to have
  unitialized variables. However, some C compilers/OS's don't
  allow automatic variables to be init'd in this manner. Therefore,
  if you are INCLUDE'ing the SQLCA in a place where it would be
  an automatic AND your C compiler/OS doesn't allow this style
  of initialization, then SQLCA_INIT should be left undefined --
  all others can define SQLCA_INIT if they wish.

  If the symbol SQLCA_NONE is defined, then the SQLCA variable will
  not be defined at all.  The symbol SQLCA_NONE should not be defined
  in source modules that have embedded SQL.  However, source modules
  that have no embedded SQL, but need to manipulate a sqlca struct
  passed in as a parameter, can set the SQLCA_NONE symbol to avoid
  creation of an extraneous sqlca variable.
 
MODIFIED
    lvbcheng   07/31/98 -  long to int
    jbasu      12/12/94 -  Bug 217878: note this is an SOSD file
    losborne   08/11/92 -  No sqlca var if SQLCA_NONE macro set 
  Clare      12/06/84 - Ch SQLCA to not be an extern.
  Clare      10/21/85 - Add initialization.
  Bradbury   01/05/86 - Only initialize when SQLCA_INIT set
  Clare      06/12/86 - Add SQLCA_STORAGE_CLASS option.
*/
 
#ifndef SQLCA
#define SQLCA 1
 
struct   sqlca
         {
         /* ub1 */ char    sqlcaid[8];
         /* b4  */ int     sqlabc;
         /* b4  */ int     sqlcode;
         struct
           {
           /* ub2 */ unsigned short sqlerrml;
           /* ub1 */ char           sqlerrmc[70];
           } sqlerrm;
         /* ub1 */ char    sqlerrp[8];
         /* b4  */ int     sqlerrd[6];
         /* ub1 */ char    sqlwarn[8];
         /* ub1 */ char    sqlext[8];
         };

#ifndef SQLCA_NONE 
#ifdef   SQLCA_STORAGE_CLASS
SQLCA_STORAGE_CLASS struct sqlca sqlca
#else
         struct sqlca sqlca
#endif
 
#ifdef  SQLCA_INIT
         = {
         {'S', 'Q', 'L', 'C', 'A', ' ', ' ', ' '},
         sizeof(struct sqlca),
         0,
         { 0, {0}},
         {'N', 'O', 'T', ' ', 'S', 'E', 'T', ' '},
         {0, 0, 0, 0, 0, 0},
         {0, 0, 0, 0, 0, 0, 0, 0},
         {0, 0, 0, 0, 0, 0, 0, 0}
         }
#endif
         ;
#endif
 
#endif
 
/* end SQLCA */



int main(argc, argv)
int     argc;
char    **argv;
{
    char       instr[30];
    char       str[80];
    char       tf_name[150];
    char       err_msg[80];
    char       audit[120];
    char       s[80];
    char       passwd[20];
    FILE       *afp, *tfp;
    char       s_tx_freq[20], s_load_per_mobile[20], s_date[20];
    int        err_cnt = 0;
    int        upd_cnt = 0;
    int        ins_cnt = 0;
    int        cnt = 1;


    /* EXEC SQL WHENEVER NOT FOUND CONTINUE; */ 


    strcpy(user_id, argv[1]);
    strcpy(passwd, argv[2]);

    emc_dir = getenv("EMC");
    get_sys_date_time(sys_date, sys_time, yymmdd, hhmmss);

    sprintf(audit, "%s/log/traffic.%s", emc_dir, yymmdd);
    if ((afp = fopen(audit, "w")) == (FILE *)NULL)
    {
        printf("\nFail to open error log");
        exit(1);
    }

    if (user_login(user_id, passwd, err_msg) != LOGIN_OK)
    {
        strip_blank(err_msg, TRAIL);
        strcat(err_msg, "  ");
        strcat(err_msg, user_id);
        strcat(err_msg, passwd);
        goto force_exit;
    }

    /* EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn; */ 


    sprintf(tf_name, "%s/%s", emc_dir, TRAFFIC_FILE);
    if((tfp = fopen(tf_name, "r")) == (FILE *) NULL)
    {
        printf("Fail to open traffic data file");
        fclose(afp);
        exit(1);
    }

    if (fgets(instr, sizeof(instr), tfp) == (char *) NULL)
    {
        fprintf(afp, "Empty traffic data file");
        fclose(afp); fclose(tfp);
        exit(1);
    }

    if (instr[0] == CTRL_Z)
    {
        fprintf(afp, "Empty traffic data file");
        fclose(afp); fclose(tfp);
        exit(1);
    }

    o_date.len = DATE_LEN;

    do
    {
        if (instr[0] == CTRL_Z)    /* This is to cater for the ^Z at the */
            break;                 /* end of the uploaded file           */

        sscanf(instr, "%s %s %s", s_tx_freq, s_load_per_mobile, s_date);
        if (!isnumber(s_tx_freq))
        {
             fprintf(afp, "Line %d: Non-numeric tx frequency\f", cnt);
             /* EXEC SQL ROLLBACK RELEASE; */ 

{
             struct sqlexd sqlstm;
             sqlstm.sqlvsn = 13;
             sqlstm.arrsiz = 0;
             sqlstm.sqladtp = &sqladt;
             sqlstm.sqltdsp = &sqltds;
             sqlstm.iters = (unsigned int  )1;
             sqlstm.offset = (unsigned int  )5;
             sqlstm.cud = sqlcud0;
             sqlstm.sqlest = (unsigned char  *)&sqlca;
             sqlstm.sqlety = (unsigned short)4352;
             sqlstm.occurs = (unsigned int  )0;
             sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
             if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


             fclose(afp); fclose(tfp);
             exit(1);
        }

        if (!isnumber(s_load_per_mobile))
        {
             fprintf(afp, "Line %d: Non-numeric traffic loading\f", cnt);
             /* EXEC SQL ROLLBACK RELEASE; */ 

{
             struct sqlexd sqlstm;
             sqlstm.sqlvsn = 13;
             sqlstm.arrsiz = 0;
             sqlstm.sqladtp = &sqladt;
             sqlstm.sqltdsp = &sqltds;
             sqlstm.iters = (unsigned int  )1;
             sqlstm.offset = (unsigned int  )20;
             sqlstm.cud = sqlcud0;
             sqlstm.sqlest = (unsigned char  *)&sqlca;
             sqlstm.sqlety = (unsigned short)4352;
             sqlstm.occurs = (unsigned int  )0;
             sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
             if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


             fclose(afp); fclose(tfp);
             exit(1);
        }

        if (strlen(s_date) != DATE_LEN)
        {
             fprintf(afp, "Line %d: Invalid date format\f", cnt);
             /* EXEC SQL ROLLBACK RELEASE; */ 

{
             struct sqlexd sqlstm;
             sqlstm.sqlvsn = 13;
             sqlstm.arrsiz = 0;
             sqlstm.sqladtp = &sqladt;
             sqlstm.sqltdsp = &sqltds;
             sqlstm.iters = (unsigned int  )1;
             sqlstm.offset = (unsigned int  )35;
             sqlstm.cud = sqlcud0;
             sqlstm.sqlest = (unsigned char  *)&sqlca;
             sqlstm.sqlety = (unsigned short)4352;
             sqlstm.occurs = (unsigned int  )0;
             sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
             if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


             fclose(afp); fclose(tfp);
             exit(1);
        }

        sscanf(instr, "%lf %f %s", &o_tx_freq, &o_load_per_mobile, o_date.arr);
       
        o_tx_freq_lo = o_tx_freq - FREQ_EPSILON;
        o_tx_freq_hi = o_tx_freq + FREQ_EPSILON;

        /* EXEC SQL 
             UPDATE CHANNEL_TRAFFIC
             SET    LOAD_PER_MOBILE = :o_load_per_mobile,
                    LAST_UPDATE = TO_DATE(:o_date, 'DD/MM/YY')
             WHERE  TX_FREQ between :o_tx_freq_lo and :o_tx_freq_hi; */ 

{
        struct sqlexd sqlstm;
        sqlstm.sqlvsn = 13;
        sqlstm.arrsiz = 4;
        sqlstm.sqladtp = &sqladt;
        sqlstm.sqltdsp = &sqltds;
        sqlstm.stmt = "update CHANNEL_TRAFFIC  set LOAD_PER_MOBILE=:b0,LAST_\
UPDATE=TO_DATE(:b1,'DD/MM/YY') where TX_FREQ between :b2 and :b3";
        sqlstm.iters = (unsigned int  )1;
        sqlstm.offset = (unsigned int  )50;
        sqlstm.cud = sqlcud0;
        sqlstm.sqlest = (unsigned char  *)&sqlca;
        sqlstm.sqlety = (unsigned short)4352;
        sqlstm.occurs = (unsigned int  )0;
        sqlstm.sqhstv[0] = (unsigned char  *)&o_load_per_mobile;
        sqlstm.sqhstl[0] = (unsigned long )sizeof(float);
        sqlstm.sqhsts[0] = (         int  )0;
        sqlstm.sqindv[0] = (         short *)0;
        sqlstm.sqinds[0] = (         int  )0;
        sqlstm.sqharm[0] = (unsigned long )0;
        sqlstm.sqadto[0] = (unsigned short )0;
        sqlstm.sqtdso[0] = (unsigned short )0;
        sqlstm.sqhstv[1] = (unsigned char  *)&o_date;
        sqlstm.sqhstl[1] = (unsigned long )11;
        sqlstm.sqhsts[1] = (         int  )0;
        sqlstm.sqindv[1] = (         short *)0;
        sqlstm.sqinds[1] = (         int  )0;
        sqlstm.sqharm[1] = (unsigned long )0;
        sqlstm.sqadto[1] = (unsigned short )0;
        sqlstm.sqtdso[1] = (unsigned short )0;
        sqlstm.sqhstv[2] = (unsigned char  *)&o_tx_freq_lo;
        sqlstm.sqhstl[2] = (unsigned long )sizeof(double);
        sqlstm.sqhsts[2] = (         int  )0;
        sqlstm.sqindv[2] = (         short *)0;
        sqlstm.sqinds[2] = (         int  )0;
        sqlstm.sqharm[2] = (unsigned long )0;
        sqlstm.sqadto[2] = (unsigned short )0;
        sqlstm.sqtdso[2] = (unsigned short )0;
        sqlstm.sqhstv[3] = (unsigned char  *)&o_tx_freq_hi;
        sqlstm.sqhstl[3] = (unsigned long )sizeof(double);
        sqlstm.sqhsts[3] = (         int  )0;
        sqlstm.sqindv[3] = (         short *)0;
        sqlstm.sqinds[3] = (         int  )0;
        sqlstm.sqharm[3] = (unsigned long )0;
        sqlstm.sqadto[3] = (unsigned short )0;
        sqlstm.sqtdso[3] = (unsigned short )0;
        sqlstm.sqphsv = sqlstm.sqhstv;
        sqlstm.sqphsl = sqlstm.sqhstl;
        sqlstm.sqphss = sqlstm.sqhsts;
        sqlstm.sqpind = sqlstm.sqindv;
        sqlstm.sqpins = sqlstm.sqinds;
        sqlstm.sqparm = sqlstm.sqharm;
        sqlstm.sqparc = sqlstm.sqharc;
        sqlstm.sqpadto = sqlstm.sqadto;
        sqlstm.sqptdso = sqlstm.sqtdso;
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


/*
             WHERE  TX_FREQ = :o_tx_freq;
*/

  
        /********************************************************/
        /* If CHANNEL_TRAFFIC record does not exist for current */
        /* channel, then insert it into CHANNEL_TRAFFIC         */
        /********************************************************/
        if (sqlca.sqlcode == NOT_FOUND)
        {
/*
            EXEC SQL
                 SELECT 'X'
                 INTO   :o_dummy
                 FROM   MOBILE_CH
                 WHERE  RX_FREQ = :o_tx_freq;

            if (sqlca.sqlcode == NOT_FOUND)
            {
                fprintf(afp, "Line %d: %lf not found in mobile channel\n", 
                        cnt, o_tx_freq);
                err_cnt++;
                continue;
            }
*/

            /* EXEC SQL INSERT INTO CHANNEL_TRAFFIC
                 VALUES 
                 (:o_tx_freq, :o_load_per_mobile, TO_DATE(:o_date, 'DD/MM/YY')); */ 

{
            struct sqlexd sqlstm;
            sqlstm.sqlvsn = 13;
            sqlstm.arrsiz = 4;
            sqlstm.sqladtp = &sqladt;
            sqlstm.sqltdsp = &sqltds;
            sqlstm.stmt = "insert into CHANNEL_TRAFFIC  values (:b0,:b1,TO_D\
ATE(:b2,'DD/MM/YY'))";
            sqlstm.iters = (unsigned int  )1;
            sqlstm.offset = (unsigned int  )81;
            sqlstm.cud = sqlcud0;
            sqlstm.sqlest = (unsigned char  *)&sqlca;
            sqlstm.sqlety = (unsigned short)4352;
            sqlstm.occurs = (unsigned int  )0;
            sqlstm.sqhstv[0] = (unsigned char  *)&o_tx_freq;
            sqlstm.sqhstl[0] = (unsigned long )sizeof(double);
            sqlstm.sqhsts[0] = (         int  )0;
            sqlstm.sqindv[0] = (         short *)0;
            sqlstm.sqinds[0] = (         int  )0;
            sqlstm.sqharm[0] = (unsigned long )0;
            sqlstm.sqadto[0] = (unsigned short )0;
            sqlstm.sqtdso[0] = (unsigned short )0;
            sqlstm.sqhstv[1] = (unsigned char  *)&o_load_per_mobile;
            sqlstm.sqhstl[1] = (unsigned long )sizeof(float);
            sqlstm.sqhsts[1] = (         int  )0;
            sqlstm.sqindv[1] = (         short *)0;
            sqlstm.sqinds[1] = (         int  )0;
            sqlstm.sqharm[1] = (unsigned long )0;
            sqlstm.sqadto[1] = (unsigned short )0;
            sqlstm.sqtdso[1] = (unsigned short )0;
            sqlstm.sqhstv[2] = (unsigned char  *)&o_date;
            sqlstm.sqhstl[2] = (unsigned long )11;
            sqlstm.sqhsts[2] = (         int  )0;
            sqlstm.sqindv[2] = (         short *)0;
            sqlstm.sqinds[2] = (         int  )0;
            sqlstm.sqharm[2] = (unsigned long )0;
            sqlstm.sqadto[2] = (unsigned short )0;
            sqlstm.sqtdso[2] = (unsigned short )0;
            sqlstm.sqphsv = sqlstm.sqhstv;
            sqlstm.sqphsl = sqlstm.sqhstl;
            sqlstm.sqphss = sqlstm.sqhsts;
            sqlstm.sqpind = sqlstm.sqindv;
            sqlstm.sqpins = sqlstm.sqinds;
            sqlstm.sqparm = sqlstm.sqharm;
            sqlstm.sqparc = sqlstm.sqharc;
            sqlstm.sqpadto = sqlstm.sqadto;
            sqlstm.sqptdso = sqlstm.sqtdso;
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


            ins_cnt++;
        }
        else
            upd_cnt++;

        cnt++;
    }
    while (fgets(instr, sizeof(instr), tfp) != (char *) NULL);

    fprintf(afp, "\nNo. of traffic records updated: %d\n", upd_cnt);
    fprintf(afp, "No. of new traffic records added: %d\f", ins_cnt);
    fclose(afp);
    /* EXEC SQL COMMIT WORK RELEASE; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 4;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )108;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


    exit(0);


sqlerr_rtn:
    /* EXEC SQL ROLLBACK WORK RELEASE; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 4;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )123;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



force_exit:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    fprintf(afp, "%s\n", err_msg);
    fclose(afp);
    exit(1);

}
