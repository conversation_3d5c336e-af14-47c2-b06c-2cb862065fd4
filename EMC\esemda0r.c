/**********************************************************************/
/*                                                                    */
/*    Module Name   :  desensit_analysis (esemda0r.c)                 */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. <PERSON>. Mak                                      */
/*                                                                    */
/*    Callers       :  main (esemba0x.c)                              */  
/*                                                                    */
/*    Parameters    :                                                 */
/*                                                                    */
/*    Called Modules:  select_station (esemss0x.pc)                   */
/*                     desensit_interference (esemdc0r.c)             */
/*                                                                    */
/*    Purpose       :  Desensitisation analysis control module.       */
/*                     Call 'select_station' to select stations       */
/*                     that are within culling distance and pass      */
/*                     them to 'desensit_interference' for actual     */
/*                     desensitisation analysis.                      */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>    /* 20170703 Cyrusma Added*/
#include <stdio.h>
#include  "../include/define.h"
#include "../include/emcext.h"
#include "../include/refext.h"
#include "../include/propext.h"
#include "../include/existext.h"
#include "../include/desen.h"

#include <string.h>    /* 20170703 Cyrusma Added*/
#include <unistd.h>    /* 20170703 Cyrusma Added*/

#ifndef ERROR
#define ERROR    1
#endif

#ifndef OK
#define OK       0
#endif

#define  DESENSIT_LINES    28
int desensit_analysis();
int select_station();
int  set_band_mode(char); /* 20170703 Cyrusma Added */
void desensit_interference(FILE* , int* , int*);

extern char msg[];

int desensit_analysis()
{
    char       da_fname[120];
    char       cmdline[140];
    FILE       *dfp;
    int        line_cnt = 0;
    int        page_cnt = 1;
    int        skip_lines;
    int        status;
    int        i, j;


#ifdef DEBUG



    if (interactive == FALSE)
    {
        printf("\n\n\n***  DESENSIT ANALYSIS\n\n");
        printf("Formula used:\n");
        printf("if (atten > user defined level), then set flag to TRUE\n");
        printf("atten = Pin - min sig + 6.0\n");
        printf("Pin = erp - feed_loss + ant - off - sfx - prop\n");;
        printf("prop = 28.1 + a + b + diff loss + CLUTTER_LOSS\n");
        printf("where a = 20.0*log10(intr.tx_freq), b = 20.0*log_delta_dist\n\n");
        printf(" intr.       vict.\n");
        printf("tx freq     rx freq     atten   min sig   Pin     erp    feed   ant    off     sfx     prop     a       b     diff loss  flag\n");
        printf("==========  ==========  ======  =======  ======  ======  ====  =====  ======  ======  ======  ======  ======  =========  ====\n");
		printf("            **********         \n");
    }

#endif


    fprintf(afp, "\n** Desensitisation Analysis Report\n");

	
    status = select_station();
	
    if (status == ERROR)
        return ERROR;

    if (cull_stn_cnt == 0)
    {
        fprintf(afp, "No stations exist around culling distance of %d m\n",
		subdist[prop->dist_index].cull_dist[prop->band][0]);
        return (0);
    }
    else
    {
        sprintf(da_fname, "%s/desensit/%.5lf.%s", emc_dir, prop_tx_freq,
                hhmmss);
        if ((dfp = fopen(da_fname, "w")) == (FILE *) NULL)
        {
            printf("Fatal error: fail to open desensitisation report : %s\n",
                   da_fname);
            fflush(afp);
            fclose(afp);
            exit(1);
        }

        max_att_req = -9999; min_att_req = 9999;




	

	
//-------------------------------------------			
			
        for (j = 0; j < fq_cnt; j++)
        {

            if (prop->stn_node != s_fq_list[j]->stn_node)
            {
			
			
                i = e_idx = s_fq_list[j]->stn_node;
				
/*
        printf("\n %d EXIST %2s%-7s %d %d %s %c %s %d %f %d %d\n", i,
        exist[i]->lic_type, exist[i]->lic_no,
        exist[i]->east_grid, exist[i]->north_grid, exist[i]->sub_district,
        exist[i]->station_type, exist[i]->antenna, exist[i]->ant_height,
        exist[i]->pw_dbw, exist[i]->az_max_rad, exist[i]->feed_loss);
		
        printf("%s %f %c %d %d %c %d\n", 
	exist[i]->sfx_filter, exist[i]->height_asl, exist[i]->mode, 
        exist[i]->band, exist[i]->noise_code, 
        exist[i]->dist_type, exist[i]->dist_index);
		
        printf("freq channel: %lf %lf %d %d\n", s_fq_list[j]->tx_freq, s_fq_list[j]->rx_freq,
               s_fq_list[j]->tx_channel, s_fq_list[j]->rx_channel);
*/
                exist_tx_freq = s_fq_list[j]->tx_freq; 
                exist_rx_freq = s_fq_list[j]->rx_freq; 
			
                set_band_mode(IS_EXIST);


                desensit_interference(dfp, &line_cnt, &page_cnt);

            }

        }

        /*********************************************************/
        /* bacause if there are desensitisations found, page_cnt */
        /* will be at least incremented to 2                     */
        /*********************************************************/
        if (page_cnt == 1)
        {
            fprintf(afp, "No desensitisation found for proposed station\n");
            unlink(da_fname);
        }
        else
        {
            skip_lines = 5 + (DESENSIT_LINES - line_cnt);
            for (i = 0; i < skip_lines; i++)
                fprintf(dfp, "\n");

            fprintf(dfp, "%47sNUMBER OF INTERFERENCES        :    %3d\n", "",
                    desen_tot);
            fprintf(dfp, "%47sMAX. ATTENUATION REQUIRED (DB) : %6.2f\n", "",
                    max_att_req);
            fprintf(dfp, "%47sMIN. ATTENUATION REQUIRED (DB) : %6.2f\n\f\n", "",
                    min_att_req);
            fclose(dfp);

/*
            sprintf(cmdline, "lp -dprinter1 %s > /dev/null", da_fname);
*/
            sprintf(cmdline, "cat %s >> %s", da_fname, print_file);
            system(cmdline);
        }

    }

    return (0);
}
