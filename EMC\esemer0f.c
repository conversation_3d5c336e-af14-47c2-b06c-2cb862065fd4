/**********************************************************************/
/*                                                                    */
/*    Module Name   :  main (esemer0f.c)                              */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  <PERSON><PERSON> <PERSON>. Mak                                      */
/*                                                                    */
/*    Callers       :  esemcn0f.c through 'system' statement          */  
/*                                                                    */
/*    Parameters    :  printer id.                                    */
/*                                                                    */
/*    Called Modules:  screen functions from 'screen.c'               */
/*                     (centre_msg, disp_err, disp_space)             */
/*                     screen function from 'cursesX'                 */
/*                                                                    */
/*    Purpose       :  Accept user input EMC analysis data, validate  */
/*                     the data the input data, then write to EMC     */
/*                     analysis batch file.                           */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <time.h>
#include <curses.h>
#include "../include/define.h"
#include "../include/emc.h"
#include "../include/global.h"

#ifndef ERROR
#define ERROR    1
#endif

#ifndef OK
#define OK       0
#endif

#ifndef SELECT_ERROR
#define SELECT_ERROR    -2
#endif

#ifndef  DUMMY
#define  DUMMY   -1
#endif

#define  TRAIL          0
#define  LEADING        1

#define  EXIT           -1

#define  LINES          12
#define  COLS           34

#define  REPORT_NOT_FOUND "Report not found, press any key to continue"

#define  COCHANNEL_DIR    "cochannel"
#define  DESENSIT_DIR     "desensit"
#define  INTERMOD2_DIR    "intermod2"
#define  INTERMOD3_DIR    "intermod3"
#define  SUMMARY_DIR      "summary"

     
char   *prog_id = "emer0f_01";
char   *screen_head = "EMC ANALYSIS REPORT REPRINTING";

double tx_freq;

extern double   atof();
char   *getenv();


main(argc, argv)
int    argc;
char   **argv;
{
    char    er_fname[150];
    char    err_msg[80];
    int     xpos, ypos;
    int     term_code;
    char    option[3];
    int     i;
    register int     j;


    initscr();
    raw();

/* added by Chen Yung */    
    noecho();
/* -- */      
    
    if (argc != 3)
    {
        sprintf(err_msg, "Usage: esemer0f -P printer_id");
        goto force_exit;
    }

    if (strcmp(argv[1], "-P"))
    {
        sprintf(err_msg, "Usage: esemer0f -P printer_id");
        goto force_exit;
    }
    
    strcpy(printer_id, argv[2]);
    get_sys_date_time(sys_date, sys_time, yymmdd, hhmmss);
    emc_dir = getenv("EMC");


    attrset(A_NORMAL);
    disp_heading(prog_id, screen_head, sys_date);
    mvaddstr(7,  5, "0.   EXIT");
    mvaddstr(9,  5, "1.   CO-CHANNEL INFORMATION REPORT");
    mvaddstr(10, 5, "2.   DESENSITISATION REPORT"); 
    mvaddstr(11, 5, "3.   2-SIGNAL INTERMODULATION REPORTS");
    mvaddstr(12, 5, "4.   3-SIGNAL INTERMODULATION REPORTS");
    mvaddstr(13, 5, "5.   SUMMARY LOG");
    mvaddstr(16, 5, "YOUR SELECTION : ");
    refresh();
    getyx(stdscr, ypos, xpos);

    do
    {
        strcpy(option, " ");
        attrset(A_REVERSE);
        disp_space(ypos, xpos, 1);
        move(ypos, xpos);
        refresh();

	    read_str(option, "", 2, 0, 10, &term_code, A_REVERSE);

        while ((option[0] < '0') || (option[0] > '5'))
        {
            disp_err("Invalid option");
            attrset(A_REVERSE);
            move(ypos, xpos);
            refresh();
	        read_str(option, "", 2, 0, 10, &term_code, A_REVERSE);
        }

        hide_cursor();
        clear_err();

        switch (option[0])
        {
            case '1' :
                if (reprint_emc_rpt(COCHANNEL_DIR, err_msg) != OK)
                {
                    disp_err(err_msg);
                    getch();
                }
                break;

            case '2' :
                if (reprint_emc_rpt(DESENSIT_DIR, err_msg) != OK)
                {
                    disp_err(err_msg);
                    getch();
                }
                break;

            case '3' :
                if (reprint_emc_rpt(INTERMOD2_DIR, err_msg) != OK)
                {
                    disp_err(err_msg);
                    getch();
                }
                break;

            case '4' :
                if (reprint_emc_rpt(INTERMOD3_DIR, err_msg) != OK)
                {
                    disp_err(err_msg);
                    getch();
                }
                break;

            case '5' :
                if (reprint_emc_rpt(SUMMARY_DIR, err_msg) != OK)
                {
                    disp_err(err_msg);
                    getch();
                }
                break;
        }

        show_cursor();
        attrset(A_NORMAL);
        disp_space(23, 0, 80);
        refresh();
    } 
    while (option[0] != '0');

    endwin();
    exit(0);

force_exit:
    disp_err(err_msg);
    getch(); clear(); refresh();
    show_cursor(); endwin();
    exit(1);
}


/*************************************************************/
/* reprint EMC reports using 'rpt_dir' to the type of report */
/* to be reprinted                                           */
/*************************************************************/

reprint_emc_rpt(rpt_dir, err_msg)
char   *rpt_dir;
char   *err_msg;
{
    char   rpt_fname[120];
    int    status;

    status = reprint_report(rpt_dir, err_msg);
    if (status == ERROR)
        strcat(err_msg, ", press any key to continue");
    if (status == NOT_FOUND)
        strcpy(err_msg, REPORT_NOT_FOUND);

    return status;
}


/********************************/
/* actual reprint of the report */
/********************************/

#define MAX_ARRAY_LEN    33

/* added by Chen Yung */
FILE	*popen();

reprint_report(rpt_dir, err_msg)
char   *rpt_dir;
char   *err_msg;
{
    char   cmdline[150];
    char   curr_dir[100];
    char   instr[MAX_ARRAY_LEN];
    char   fname[MAX_ARRAY_LEN];
    char   *rpt_array[500];
    FILE   *ifp;
    int    y1 = 6, x1 = 45, y2, x2;
    int    entry;
    register int   i = 0;
    WINDOW *wp;
char s[80];

    sprintf(curr_dir, "%s/%s", emc_dir, rpt_dir);
    chdir(curr_dir);
    strcpy(cmdline, "/bin/ls -1");
    if ((ifp = popen(cmdline, "r")) == (FILE *)NULL)
    {
        sprintf(err_msg, "Fail to open process return input");
        return ERROR;
    }
    if (fgets(instr, sizeof(instr), ifp) == (char *)NULL)
    {
        pclose(ifp);
        return NOT_FOUND;
    }
    do
    {
        instr[strlen(instr) - 1] = '\0';    /* strip <newline> */
        if ((rpt_array[i] = (char *) malloc(MAX_ARRAY_LEN)) == (char *) NULL)
        {
            sprintf(err_msg, "Fail to allocate memory for rpt_array");
            goto force_exit;
        }
        strcpy(rpt_array[i], instr);
        pad_space(rpt_array[i], MAX_ARRAY_LEN);
        i++;
    }
    while (fgets(instr, sizeof(instr), ifp) != (char *)NULL);

    pclose(ifp);
        
    wp = newwin(LINES, COLS, y1, x1);
    keypad(wp, TRUE);

    y2 = y1 + LINES - 1;
    x2 = x1 + COLS - 1;

    entry = select_entry(wp, TRUE, y1, x1, y2, x2, rpt_array,
                         (char **)NULL, MAX_ARRAY_LEN, TRUE, i, err_msg);

    if (entry == SELECT_ERROR)
        goto force_exit;

    for (; entry != EXIT; )
    {
        centre_msg(23, "Printing ...", A_BLINK, A_NORMAL);
        strcpy(fname, rpt_array[entry]);
        strip_blank(fname, TRAIL);
/*
        sprintf(cmdline, "cat %s >> xxx", fname);
*/
        sprintf(cmdline, "lp -dprinter1 %s > /dev/null", fname);
        system(cmdline);
        disp_space(23, 0, 80);
        entry = select_entry(wp, TRUE, y1, x1, y2, x2, 
                             rpt_array, (char **)NULL, MAX_ARRAY_LEN, TRUE, i);
    }

    delwin(wp);
    clear_border(y1, x1, y2, x2);

    return OK;


force_exit:
    disp_err(err_msg);
    getch(); clear(); refresh();
    show_cursor(); endwin();
    exit(1);
}
