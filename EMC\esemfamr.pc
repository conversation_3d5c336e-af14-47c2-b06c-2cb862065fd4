/**********************************************************************/
/*                                                                    */
/*    Module Name   :  main (esemfamr.pc)                             */
/*    Date Written  :  July, 1993                                     */
/*    Author        :  <PERSON><PERSON> <PERSON>. <PERSON>                                      */
/*                                                                    */
/*    Callers       :  main (esemep0f.c)                              */  
/*                                                                    */
/*    Parameters    :  error message                                  */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  Prints attenuation of all SFX filter at a      */
/*                     frequency offset range of -3MHz to +3MHz.      */
/*                     The report is sorted by SFX filter type.       */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Jul-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>

//20180627 Cyrus add
#include <time.h>
#include <unistd.h>
#include <curses.h>
#include "../include/define.h"
#include "../include/emc.h"


#ifndef ERROR
#define ERROR    1
#endif

#ifndef OK
#define OK       0
#endif

#ifndef LOGIN_OK
#define LOGIN_OK    0
#endif

#ifndef NOT_FOUND
#define NOT_FOUND   1403
#endif

#define  MAX_RPT_LINES       57
#define  ITEMS_PER_LINE      5
#define  HEADLINES           4
#define  COLUMN_HEADLINES    6

/*   strip blank mode   */
#define  TRAIL          0
#define  LEADING        1

int get_sys_date_time(char *,char *,char *,char *);	                /*20180627 Cyrus Add */
int user_login    (char *,char *,char *);                           /*20180627 Cyrus Add */
int strip_blank   (char *,char *);									/*20180627 Cyrus Add */
void print_rpt_head (FILE *, int);                                  /*20180627 Cyrus Add */
void print_rpt_col  (FILE *);                                       /*20180627 Cyrus Add */


char    user_id[20];        /* user login name       */
char	sys_date[9];        /* system date           */
char	sys_time[9];        /* system time           */
char	yymmdd[7];          /* system date in yymmdd */
char	hhmmss[7];          /* system time in hhmmss */
char	printer_id[15];     /* destinated printer id */
char    err_msg[133];

char       *getenv();
struct tm  *get_date_time();


EXEC SQL BEGIN DECLARE SECTION;

    float    o_freq_offset;
    float    o_att_db;
    VARCHAR  o_filter[15];
    char     select1[256];

EXEC SQL END DECLARE SECTION;

EXEC SQL INCLUDE SQLCA;


int main(argc, argv)
int    argc;
char   **argv;
{
    char   *rpt_dir;
    char   rpt_line[133];
    char   sub_rpt_line[35];
    char   fname[120];
    char   cmdline[100];
    char   prev_filter[15];
    FILE   *rfp;
    int    line_cnt = 0;
    int    page_cnt = 1;
    int    item_cnt = 0;


    EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn;

    if (argc != 5)
    {
        printf("Fatal error: invalid no. of arguments passed to %s\n", argv[0]);
        printf("Usage: esemfamr username password -P printer_id\n");
        exit(1);
    }

    if (strcmp(argv[3], "-P"))
    {
        printf("Fatal error: invalid no. of arguments passed to %s\n", argv[0]);
        printf("Usage: esemfamr username password -P printer_id\n");
        exit(1);
    }

    strcpy(user_id, argv[1]);
    rpt_dir = getenv("REPORT_DIR");
    sprintf(fname, "%s/%s.%s", rpt_dir, argv[0], user_id);
    if((rfp = fopen(fname, "w")) == (FILE *) NULL)
    {
        printf("Fail to open %s, press any key to exit", fname);
        getchar();
        goto force_exit;
    }

    if (user_login(user_id, argv[2], err_msg) != LOGIN_OK)
    {
        strip_blank(err_msg, TRAIL);
        strcat(err_msg, "  ");
        strcat(err_msg, argv[1]);
        strcat(err_msg, argv[2]);
        fprintf(rfp, "%s\n", err_msg);
        fclose(rfp);
        goto force_exit;
    }

    strcpy(printer_id, argv[4]);
    sprintf(select1, "SELECT FILTER_TYPE, CHANNEL_SEPARATION, ATTENUATION_IN_DB ");
    strcat(select1, "FROM SFX_FILTER_ATT ORDER BY FILTER_TYPE, CHANNEL_SEPARATION");

    EXEC SQL PREPARE S1 FROM :select1;
    EXEC SQL DECLARE C1 CURSOR FOR S1;

    EXEC SQL OPEN C1;

    EXEC SQL FETCH C1 INTO :o_filter, :o_freq_offset, :o_att_db;
    if (sqlca.sqlcode == NOT_FOUND)
    {
        fprintf(rfp, "Fatal error: no record found in SFX_FILTER_ATT");
        EXEC SQL CLOSE C1;
        fclose(rfp);
        goto force_exit;
    }

    get_sys_date_time(sys_date, sys_time, yymmdd, hhmmss);

    print_rpt_head(rfp, page_cnt); 
    page_cnt++;
    line_cnt = HEADLINES + COLUMN_HEADLINES;
    rpt_line[0] = prev_filter[0] = '\0';

    for (; ;)
    {
        o_filter.arr[o_filter.len] = '\0';

/*        if (strcmp(o_filter.arr, prev_filter)) */
        if (strcmp((char *)o_filter.arr, prev_filter))
        {
            if (prev_filter[0] != '\0')
            {
                fprintf(rfp, "%s\n", rpt_line);
                line_cnt++;
                rpt_line[0] = '\0';
                item_cnt = 0;
                if ((line_cnt + COLUMN_HEADLINES) >= MAX_RPT_LINES)
                {
                    print_rpt_head(rfp, page_cnt); 
                    page_cnt++;
                    line_cnt = HEADLINES + COLUMN_HEADLINES;
                }
                else
                {
                    print_rpt_col(rfp); 
                    line_cnt += COLUMN_HEADLINES;
                }
            }
/*            strcpy(prev_filter, o_filter.arr); */
            strcpy(prev_filter, (char *)o_filter.arr);
        }

        sprintf(sub_rpt_line, " %9.5f      %5.1f      ", o_freq_offset,
                o_att_db);
        strcat(rpt_line, sub_rpt_line);

        item_cnt = (item_cnt + 1) % ITEMS_PER_LINE;
        if (item_cnt == 0)
        {
            if (line_cnt == MAX_RPT_LINES)
            {
                print_rpt_head(rfp, page_cnt); 
                page_cnt++;
                line_cnt = HEADLINES + COLUMN_HEADLINES;
            }
            fprintf(rfp, "%s\n", rpt_line);
            rpt_line[0] = '\0';
            line_cnt++;
        }

        EXEC SQL FETCH C1 INTO :o_filter, :o_freq_offset, :o_att_db;

        if (sqlca.sqlcode == NOT_FOUND)
        {
            if (item_cnt != 0)
                fprintf(rfp, "%s\n", rpt_line);

            break;
        }
    }
    
    EXEC SQL CLOSE C1;

    fprintf(rfp, "\n\n%57s** END OF REPORT **", "");
    fclose(rfp);

    sprintf(cmdline, "lp -dprinter1 %s > /dev/null", fname);
    system(cmdline);
/*
    unlink(fname);
*/

    exit(0);

sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    fprintf(rfp, "%s\n", err_msg);
    fclose(rfp);

force_exit:
    exit(1);

}


/**********************************************************************/
/*  print SFX filter attenuation report column heading                */
/**********************************************************************/

void print_rpt_col(rfp)
FILE   *rfp;
{
    int    i;

    fprintf(rfp, "\n\nFILTER TYPE : %s\n\n", o_filter.arr);
    for (i = 0; i < ITEMS_PER_LINE - 1; i++)
        fprintf(rfp, "FREQ. OFFSET   ATT.(dB)    ");
    fprintf(rfp, "FREQ. OFFSET   ATT.(dB)\n");
    for (i = 0; i < ITEMS_PER_LINE - 1; i++)
        fprintf(rfp, "   (MHz)                   ");
    fprintf(rfp, "   (MHz)               \n");
    for (i = 0; i < ITEMS_PER_LINE - 1; i++)
        fprintf(rfp, "============   ========    ");
    fprintf(rfp, "============   ========\n");
}


/**********************************************************************/
/*  print SFX filter attenuation report heading                       */
/**********************************************************************/

void print_rpt_head(rfp, page_cnt)
FILE    *rfp;
int     page_cnt;
{
   if (page_cnt > 1)
       fprintf(rfp, "\f");
   fprintf(rfp, "USER ID  : %-15s%26s", user_id, "");
   fprintf(rfp, "ENHANCED SPECTRUM MANAGEMENT SYSTEM");
   fprintf(rfp, "%27sPAGE    : %-d\n", "", page_cnt);
   fprintf(rfp, "RUN DATE : %s", sys_date);
   fprintf(rfp, "%29sSFX FILTER ATTENUATION MAINTENANCE REPORT ", "");
   fprintf(rfp, "%24sPROGRAM : esemfamr\n", "");
   fprintf(rfp, "%50s(SORTED BY FILTER TYPE, FREQ. OFFSET)\n\n","");  //20180627 Cyrus Add ,"" at the end , cause  ‘%s’ expects a matching ‘char *’ argument
   print_rpt_col(rfp);
}
