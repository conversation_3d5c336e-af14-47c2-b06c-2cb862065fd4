/**********************************************************************/
/*                                                                    */
/*    Module Name   :  freqsel_main_menu (esemfp0f.c)                 */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  <PERSON><PERSON> <PERSON><PERSON>                                      */
/*                                                                    */
/*    Callers       :  esemcn0f                                       */  
/*                                                                    */
/*    Parameters    :  emc user id.                                   */
/*                  :  user password                                  */
/*                                                                    */
/*    Called Modules:  esemct0x.pc                                    */
/*                     esemsc0f.pc                                    */
/*                     esemvc0f.pc                                    */
/*                                                                    */
/*    Purpose       :  Accept user input option and perform the       */
/*                     following functions accordingly:               */
/*                         channel traffic updating (esemct0x.pc)     */
/*                         shared channel pre-selection analysis      */
/*                         (esemsc0f.pc)                              */
/*                         vacant channel pre-selection analysis      */
/*                         (esemvc0f.pc)                              */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>													/*20170614 Cyrus [Add] */
#include <stdio.h>
#include <string.h>                                    				/*20170704 Cyrus [Add] */
#include <curses.h>
#include <time.h>
#include "../include/emcext.h"
#include "../include/global.h"
#include <unistd.h>													/*20170614 Cyrus [Add] */

#ifndef ERROR
#define ERROR    1
#endif

#ifndef OK
#define OK       0
#endif

#ifndef  DUMMY
#define  DUMMY        -1
#endif

#define  TRAIL          0
#define  LEADING        1

#define  LOGIN_OK	0

#define  UPDATE_FINISH_MSG    "Update finished, press any key to continue"

/* commented out by Chen Yung
extern char    *src_dir;
*/

void show_cursor();        											/*20170704 Cyrus [Add] */
int disp_heading (char *,char *,char *);							/*20170707 Cyrus [Add] */
void disp_space(int,int,int);										/*20170707 Cyrus [Add] */
int read_str(char *, char *, int, int, int, int *, int);			/*20170707 Cyrus Add */
void disp_err(char *);												/*20170707 Cyrus Add */
int clear_err();													/*20170707 Cyrus Add */
void hide_cursor();													/*20170707 Cyrus Add */
int centre_msg(int, char *, int, int);								/*20170707 Cyrus Add */

/* added by Chen Yung */
char       *getenv();

int freqsel_main_menu(emc_uid, passwd)
char    *emc_uid;
char    *passwd;
{
/*    char*   src_dir; */
    char    *src_dir;
    char    err_msg[80];
    char    cmdline[150];
    char    errlog[120];
    char    option[3];
    int     i = 0;
    int     cnt = 1;
    int     field_no;
    int     term_code;
    int     y_pos[2], x_pos[2];

    struct tm  *tt;


    clear();

    src_dir = getenv("SRC");

    do
    {
        raw();
        strcpy(option, "-");
	    attrset(A_NORMAL);
        show_cursor();
        clear();
        disp_heading("emfp0f_01","FREQUENCY  PRE-SELECTION  ANALYSIS",sys_date);
        mvaddstr(9,  15, "0.   Exit");
        mvaddstr(11, 15, "1.   PERIODIC MRS CHANNEL TRAFFIC UPDATING");
        mvaddstr(12, 15, "2.   SHARED CHANNEL ALLOCATION REPORTING");
        mvaddstr(13, 15, "3.   VACANT CHANNEL REPORTING");
        mvaddstr(14, 15, "4.   MRS CHANNEL TRAFFIC UPDATING LOG REPRINTING");
        mvaddstr(16, 15, "YOUR SELECTION : ");
        refresh();
        getyx(stdscr, y_pos[0], x_pos[0]);
        attrset(A_REVERSE);
        disp_space(y_pos[0], x_pos[0], 1);
        move(y_pos[0], x_pos[0]);
        refresh();

	read_str(option, "", 2, 0, 10, &term_code, A_REVERSE);

        while ((option[0] < '0') || (option[0] > '4'))
        {
            disp_err("Invalid option");
            move(y_pos[0], x_pos[0]);
            refresh();
	    read_str(option, "", 2, 0, 10, &term_code, A_REVERSE);
        }

        clear_err();

        switch (option[0])
        {
            case '0' : 
                break;
            case '1' : 
/*
                if (batch_traffic_update(err_msg) == ERROR)
                {
                    strcat(err_msg, ", press any key to continue");
                    disp_err(err_msg);
                    refresh(); getch(); show_cursor();
                }
*/
                hide_cursor();
                centre_msg(23, "Processing ...", A_BLINK, A_REVERSE);
                sprintf(cmdline, "%s/esemct0x %s %s", src_dir, user_id, passwd);
                system(cmdline); 
                centre_msg(23, UPDATE_FINISH_MSG, A_BOLD, A_REVERSE);
                beep();
                getch();
                break;
            case '2' : 
                sprintf(cmdline, "%s/esemsc0f %s %s %s -P %s", src_dir, user_id,
                        passwd, emc_uid, printer_id);
                system(cmdline); 
                break;
            case '3' : 
                sprintf(cmdline, "%s/esemvc0f %s %s %s -P %s", src_dir, user_id,
                        passwd, emc_uid, printer_id);
                system(cmdline); 
                break;
            case '4' : 
                sprintf(cmdline, "lp -dprinter1  %s/log/traffic.%s > /dev/null", emc_dir, yymmdd);
                system(cmdline); 
                break;
        }
    } while (option[0] != '0');										/*20170707 Cyrus Add [ control reaches end of non-void function [-Wreturn-type]]*/
	
	return(0);  
}
