/**********************************************************************/
/*                                                                    */
/*    Module Name   :  get_district (esemgd0x.c)                      */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. <PERSON>. Mak                                      */
/*                                                                    */
/*    Callers       :  main (esemba0x.c)                              */  
/*                     select_station (esemss0x.pc)                   */
/*                                                                    */
/*    Parameters    :  sub_district code                              */
/*                     district type                                  */
/*                     noise code                                     */
/*                     subdist array index                            */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  Determine the district type, noise code,       */
/*                     subdist array index, given a sub-district      */
/*                     code.                                          */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/


#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include "../include/refext.h"

#ifndef ERROR
#define ERROR    1
#endif

#ifndef OK
#define OK       0
#endif

int get_district(char *, char *, int *, int*);

int get_district(sub_district, dist_type, noise_code, dist_index)
char   *sub_district, *dist_type;
int    *noise_code, *dist_index;
{
    int    comp_status;
    int    i;


/*
#ifdef DEBUG
    printf("get_district\n");
#endif
*/

    for (i = 0; i < subdist_cnt; i++)
    {
	comp_status = strcmp(subdist[i].sub_district, sub_district);
	if (comp_status >= 0)
            break;
    }

    if ((comp_status > 0) || (i == subdist_cnt))
        return ERROR;

    *dist_type  = subdist[i].dist_type;
    *noise_code = subdist[i].noise_code;
    *dist_index = i;

    return OK;
}
