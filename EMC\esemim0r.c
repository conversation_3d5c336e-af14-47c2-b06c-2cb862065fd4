/**********************************************************************/
/*                                                                    */
/*    Module Name   :  intermod_2 (esemim0r.c)                        */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  <PERSON>. <PERSON>. Mak                                      */
/*                                                                    */
/*    Callers       :  main of batch EMC module (esemba0x.c)          */  
/*                     main of co-site analysis module (esemcs0f.c)   */
/*                                                                    */
/*    Parameters    :                                                 */
/*                                                                    */
/*    Called Modules:  base_to_base (esemin0x.pc)                     */
/*                                                                    */
/*    Purpose       :  Perform 2-signal intermodulation analysis      */
/*                     together with base-to-base interference        */
/*                     analysis to proposed and existing station.     */
/*                     Report intermod details if interference power  */
/*                     is above pre-defined level.                    */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include "../include/macros.h"
#include "../include/define.h"
#include "../include/modeband.h"
#include "../include/emcext.h"
#include "../include/refext.h"
#include "../include/propext.h"
#include "../include/existext.h"
#include "../include/presuext.h"
#include "../include/intmod.h"

#ifdef BATCHED_EMC


#define EMC_INTERMOD2_LINES       28
#define EMC_INTERMOD3_LINES       11

#endif


#ifdef COSITE

#define COSITE_INTERMOD2_LINES    35
#define COSITE_INTERMOD3_LINES    35

extern char   sub_district[];
extern char   emc_uid[];
extern double prop_tx_freq, prop_rx_freq;
extern int    freq_band;
extern int    tx_mode;
extern int    intmod2_vict_tot;
extern int    intmod2_tx1_tot;
extern int    intmod2_tx2_tot;
extern int    intmod3_vict_tot;
extern int    intmod3_tx1_tot;
extern int    intmod3_tx3_tot;

#endif


/*
#define  MIN_CURVE_LEVEL    .025
*/

#define  MIN_CURVE_LEVEL    .03

float   cal_dist();

/*
FILE *tfp;
*/

intermod_2()
{
    char       im2_fname[120];
    char       cmdline[140];
    FILE       *mfp;
    int        p_tx_channel, p_rx_channel;
    int        cull_channel;
    int        channel1;
    int        channel_diff;
    int        bw3db_channel;
    int        harmonic_hi, harmonic_lo;
    int        harmonic;
    int        intrfr_flag1, intrfr_flag2;
    int        i, j;
    int        line_cnt;
    int        page_cnt;


#ifdef BATCHED_EMC

    if (interactive == FALSE)
    {
        printf("\n\n\n***  INTERMOD_2\n\n");
        printf("Formula used:\n");
/*
printf("if (Pin - min sig > INTERMOD_DB), then set flag to TRUE\n");
printf("where INTERMOD_DB = -14dB currently\n");
printf("Pin = erp - feed_loss + ant - off - sfx - prop\n");
*/
        printf("if (Pin > intmod_att_db + curve_adj), ");
        printf("then set flag to TRUE\n");
        printf("intmod_att_db = %-6.2f\n", prop->intmod_att_db);
        printf("Pin = erp - feed_loss + ant - sfx - prop\n");;
        printf("prop = 28.1 + a + b + diff loss + CLUTTER_LOSS\n");
        printf("where a = 20.0*log10(intr.tx_freq), b = 20.0*log_delta_dist\n\n");
        printf(" intr.       vict.\n");
        printf("tx freq    rx freq    min sig  Pin    erp   feed  ant  curve_adj  sfx    prop    a      b    diff loss flag\n");
        printf("========== ========== ======= ====== ====== ==== ===== ========= ====== ====== ====== ====== ========= ====\n");
    }

#endif

    max_delta_freq = -999; min_delta_freq = 999;
    max_power_intr = -999; min_power_intr = 999;

    cull_channel = (int)(intermod2_cull_freq/MIN_CHANNEL_SEP + .5);

    p_tx_channel = (int)(prop_tx_freq/MIN_CHANNEL_SEP + .5);
    p_rx_channel = (int)(prop_rx_freq/MIN_CHANNEL_SEP + .5);


#ifdef BATCHED_EMC
    bw3db_channel = (int)(bw3db[prop->band]/MIN_CHANNEL_SEP + .005);
#endif

#ifdef COSITE
    bw3db_channel = (int)(bw3db[freq_band]/MIN_CHANNEL_SEP + .005);
#endif


/*
#ifdef DEBUG
    printf("p_tx_channel p_rx_channel : %d %d\n", p_tx_channel, p_rx_channel);
    printf("intermod_2 : %d %d %d\n", cull_stn_cnt, cull_channel, bw3db_channel);
#endif
*/

    /*************************************************************/
    /*     Intermodulation with proposed station as victim       */
    /*************************************************************/

/*
        printf("\n** 2-signal Intermodulation report for channel:");
        printf("%f/%f MHz(victim)\n", prop_tx_freq, prop_rx_freq);
*/


#ifdef BATCHED_EMC

    fprintf(afp, "\n** 2-signal Intermodulation Report (victim)\n");
    if (prop->mode == TX_ONLY)
    {
        fprintf(afp, "Proposed station is TX_ONLY station, ");
        fprintf(afp, "no intermod for proposed station as victim\n");
        goto prop_tx1;
    }
    sprintf(im2_fname, "%s/intermod2/%.5lf.%s.v", emc_dir, prop_tx_freq,
            hhmmss);
    if ((mfp = fopen(im2_fname, "w")) == (FILE *) NULL)
    {
        fprintf(afp, "Fatal error: fail to open 2-signal intermod ");
        fprintf(afp, "report : %s\n", im2_fname);
        exit(1);
    }

#endif


#ifdef COSITE

    if (tx_mode == TX_ONLY)
        goto prop_tx1;
    
    sprintf(im2_fname, "%s/cosite/intermod2/%s.%.5lf.%s.v", emc_dir, emc_uid,
            prop_tx_freq, sub_district);
    if ((mfp = fopen(im2_fname, "w")) == (FILE *) NULL)
        return(FILE_OPEN_ERROR);

#endif


    harmonic_hi = p_rx_channel + bw3db_channel;
    harmonic_lo = p_rx_channel - bw3db_channel;

/*
fprintf(tfp,"rx fq bw3db cull hi ho: %d %d %d %d %d %d\n",p_rx_channel,fq_cnt,
bw3db_channel,cull_channel,harmonic_hi,harmonic_lo);
fflush(tfp);
*/

    intermod_cnt = 0; line_cnt = 0; page_cnt = 1;
    for (i = 0; i < fq_cnt; i++)
    {
        if (fq_list[i]->stn_node == prop->stn_node)
            continue;

        channel_diff = abs(p_rx_channel - fq_list[i]->tx_channel);
/*
fprintf(tfp,"freq ch channel_diff: %lf %d %d\n",fq_list[i]->tx_freq,
fq_list[i]->tx_channel, channel_diff);
fflush(tfp);
printf("V 2nd loop: %lf %d\n", fq_list[i]->tx_freq, fq_list[i]->tx_channel);

        if (channel_diff > cull_channel)
                continue;
*/
        /***********************************************************/
        /* if channel difference between tx station and rx station */
        /* is greater than culling frequency or the 2 stations are */
        /* co-channel stations, skip this tx station               */
        /***********************************************************/
        if ((channel_diff > cull_channel) || (channel_diff == 0))
            continue;

        eptr1 = exist[fq_list[i]->stn_node];
        channel1 = fq_list[i]->tx_channel * 2;

        for (j = 0; j < fq_cnt; j++)
        {
            if (i == j)
                continue;

            if (fq_list[j]->stn_node == prop->stn_node)
                continue;

            harmonic = channel1 - fq_list[j]->tx_channel;
/*
printf("harmonic channel1 channel2 lo hi: %d %d %d %d %d\n", harmonic, channel1, fq_list[j]->tx_channel, harmonic_lo, harmonic_hi);
fprintf(tfp,"harmonic channel1 channel2 lo hi: %d %d %d %d %d\n", 
harmonic, channel1, fq_list[j]->tx_channel, harmonic_lo, harmonic_hi);
*/
            if ((harmonic >= harmonic_lo)
            &&  (harmonic <= harmonic_hi))
            {
/*
fprintf(tfp, "within harmonic\n");
*/
                eptr2 = exist[fq_list[j]->stn_node];

#ifdef BATCHED_EMC
                intrfr_flag1 = intrfr_flag2 = FALSE;
                e_idx = fq_list[i]->stn_node;
                exist_tx_freq = fq_list[i]->tx_freq;
                set_victim_propose();
                base_to_base(&intrfr_flag1, INTERMOD);
                power_intr1 = power_intr;
                e_idx = fq_list[j]->stn_node;
                exist_tx_freq = fq_list[j]->tx_freq;
                set_victim_propose();
                base_to_base(&intrfr_flag2, INTERMOD);
                power_intr2 = power_intr;
                if (intrfr_flag1 && intrfr_flag2)
                {
                    print_intermod2_line(mfp, 
                                         &line_cnt,
                                         &page_cnt,
                                         fq_list[i]->tx_freq,
                                         fq_list[j]->tx_freq,
                                         prop_rx_freq,
                                         harmonic,
                                         RECEIVER);
                    intmod2_vict_tot++;
                }
#endif

#ifdef COSITE
                print_intermod2_line(mfp, 
                                     &line_cnt,
                                     &page_cnt,
                                     fq_list[i]->tx_freq,
                                     fq_list[j]->tx_freq,
                                     prop_rx_freq,
                                     harmonic,
                                     RECEIVER);
                intmod2_vict_tot++;
#endif

            }
        }
    }

#ifdef BATCHED_EMC
    if (intermod_cnt == 0)
    {
        fprintf(afp, "No intermod found\n");
        fclose(mfp);
        unlink(im2_fname);
    }
    else
    {
        print_intermod_total(line_cnt, EMC_INTERMOD2_LINES, mfp);
        fclose(mfp);
/*
        sprintf(cmdline, "lp -dprinter1 %s > /dev/null", im2_fname);
*/
        sprintf(cmdline, "cat %s >> %s", im2_fname, print_file);
        system(cmdline);
    }
#endif

#ifdef COSITE
    if (intermod_cnt == 0)
    {
        fclose(mfp);
        unlink(im2_fname);
    }
    else
    {
        print_intermod_total(line_cnt, COSITE_INTERMOD2_LINES, mfp);
        fclose(mfp);
        sprintf(cmdline, "lp -dprinter1 %s > /dev/null", im2_fname);
        system(cmdline);
    }
#endif


prop_tx1:

    /*************************************************************/
    /*  Intermodulation with proposed station as transmitter #1  */
    /*************************************************************/


#ifdef BATCHED_EMC

    fprintf(afp, "\n** 2-signal Intermodulation Report (Tx1)\n");
    if (prop->mode == RX_ONLY)
    {
        fprintf(afp, "Proposed station is RX_ONLY station, no ");
        fprintf(afp, "intermod for proposed station as transmitter\n");
        goto intermod2_end;
    }
    sprintf(im2_fname, "%s/intermod2/%.5lf.%s.t1", emc_dir, 
            prop_tx_freq, hhmmss);
    if ((mfp = fopen(im2_fname, "w")) == (FILE *) NULL)
    {
        fprintf(afp, "Fatal error: fail to open 2-signal intermod ");
        fprintf(afp, "report : %s\n", im2_fname);
        exit(1);
    }

#endif


#ifdef COSITE

    if (tx_mode == RX_ONLY)
        goto intermod2_end;
    
    sprintf(im2_fname, "%s/cosite/intermod2/%s.%.5lf.%s.t1", emc_dir, emc_uid,
            prop_tx_freq, sub_district);
    if ((mfp = fopen(im2_fname, "w")) == (FILE *) NULL)
        return(FILE_OPEN_ERROR);

#endif


    max_delta_freq = -999; min_delta_freq = 999;
    max_power_intr = -999; min_power_intr = 999;

    channel1 = 2 * p_tx_channel;
    intermod_cnt = 0; line_cnt = 0; page_cnt = 1;
    for (i = 0; i < fq_cnt; i++)
    {
        if (fq_list[i]->stn_node == prop->stn_node)
            continue;
/*
printf("TX1 2nd loop:  %d %lf %d\n", i, fq_list[i]->tx_freq, fq_list[i]->tx_channel);
*/

        channel_diff = abs(p_tx_channel - fq_list[i]->tx_channel);

/*
        if (channel_diff > cull_channel)
                continue;
*/
        /***********************************************************/
        /* if channel difference between tx station and rx station */
        /* is greater than culling frequency or the 2 stations are */
        /* co-channel stations, skip this tx station               */
        /***********************************************************/
        if ((channel_diff > cull_channel) || (channel_diff == 0))
            continue;

        eptr1 = exist[fq_list[i]->stn_node];
        harmonic = channel1 - fq_list[i]->tx_channel;

        for (j = 0; j < fq_cnt; j++)
        {
            if (j == i)
                continue;
 
            if (fq_list[j]->stn_node == prop->stn_node)
                continue;

/*
printf("j TX1 freq: %d %lf %lf\n", j, fq_list[j]->tx_freq, fq_list[j]->rx_freq);
fflush(stdin);
{
int il,ih;
            il = fq_list[j]->rx_channel - bw3db_channel;
            ih = fq_list[j]->rx_channel + bw3db_channel;
printf("TX1 freq c1 c2 harmonic: %lf %d %d %d\n", 
       fq_list[j]->rx_freq, channel1, fq_list[j]->rx_channel, harmonic);
fflush(stdin);
}
*/
            if ((harmonic >= (fq_list[j]->rx_channel - bw3db_channel))
            &&  (harmonic <= (fq_list[j]->rx_channel + bw3db_channel)))
            {
/*
printf("within harmonic\n");
fflush(stdin);
*/
                eptr2 = exist[fq_list[j]->stn_node];

#ifdef BATCHED_EMC
                intrfr_flag1 = intrfr_flag2 = FALSE;
                e_idx = fq_list[j]->stn_node;
                exist_rx_freq = fq_list[j]->rx_freq;
                set_victim_exist();
                base_to_base(&intrfr_flag1, INTERMOD);
                power_intr1 = power_intr;
                set_intr_exist(i);
                base_to_base(&intrfr_flag2, INTERMOD);
                power_intr2 = power_intr;
                if (intrfr_flag1 && intrfr_flag2)
                {
                    print_intermod2_line(mfp,
                                         &line_cnt,
                                         &page_cnt,
                                         prop_tx_freq,
                                         fq_list[i]->tx_freq,
                                         fq_list[j]->rx_freq,
                                         harmonic,
                                         TRANSMITTER_1);
                    intmod2_tx1_tot++;
                }
#endif

#ifdef COSITE
                print_intermod2_line(mfp,
                                     &line_cnt,
                                     &page_cnt,
                                     prop_tx_freq,
                                     fq_list[i]->tx_freq,
                                     fq_list[j]->rx_freq,
                                     harmonic,
                                     TRANSMITTER_1);
                intmod2_tx1_tot++;
#endif

            }
        }
    }


#ifdef BATCHED_EMC
    if (intermod_cnt == 0)
    {
        fprintf(afp, "No intermod found\n");
        fclose(mfp);
        unlink(im2_fname);
    }
    else
    {
        print_intermod_total(line_cnt, EMC_INTERMOD2_LINES, mfp);
        fclose(mfp);
/*
        sprintf(cmdline, "lp -dprinter1 %s > /dev/null", im2_fname);
*/
        sprintf(cmdline, "cat %s >> %s", im2_fname, print_file);
        system(cmdline);
    }
#endif

#ifdef COSITE
    if (intermod_cnt == 0)
    {
        fclose(mfp);
        unlink(im2_fname);
    }
    else
    {
        print_intermod_total(line_cnt, COSITE_INTERMOD2_LINES, mfp);
        fclose(mfp);
        sprintf(cmdline, "lp -dprinter1 %s > /dev/null", im2_fname);
        system(cmdline);
    }
#endif


    /*************************************************************/
    /*  Intermodulation with proposed station as transmitter #2  */
    /*************************************************************/


#ifdef BATCHED_EMC

    fprintf(afp, "\n** 2-signal Intermodulation Report (Tx2)\n");
    sprintf(im2_fname, "%s/intermod2/%.5lf.%s.t2", emc_dir,
            prop_tx_freq, hhmmss);
    if ((mfp = fopen(im2_fname, "w")) == (FILE *) NULL)
    {
        fprintf(afp, "Fatal error: fail to open 2-signal intermod ");
        fprintf(afp, "report : %s\n", im2_fname);
        exit(1);
    }

#endif


#ifdef COSITE

    sprintf(im2_fname, "%s/cosite/intermod2/%s.%.5lf.%s.t2", emc_dir, emc_uid,
            prop_tx_freq, sub_district);
    if ((mfp = fopen(im2_fname, "w")) == (FILE *) NULL)
        return(FILE_OPEN_ERROR);

#endif


    max_delta_freq = -999; min_delta_freq = 999;
    max_power_intr = -999; min_power_intr = 999;

    channel1 = p_tx_channel;
    intermod_cnt = 0; line_cnt = 0; page_cnt = 1;
    for (i = 0; i < fq_cnt; i++)
    {
        if (fq_list[i]->stn_node == prop->stn_node)
            continue;

/*
printf("TX2 2nd loop: %lf %d\n", fq_list[i]->tx_freq, fq_list[i]->tx_channel);
*/
        channel_diff = abs(p_tx_channel - fq_list[i]->tx_channel);
/*
        if (channel_diff > cull_channel)
            continue;
*/
        /***********************************************************/
        /* if channel difference between tx station and rx station */
        /* is greater than culling frequency or the 2 stations are */
        /* co-channel stations, skip this tx station               */
        /***********************************************************/
        if ((channel_diff > cull_channel) || (channel_diff == 0))
            continue;


        eptr1 = exist[fq_list[i]->stn_node];
        harmonic = 2 * fq_list[i]->tx_channel - channel1;

        for (j = 0; j < fq_cnt; j++)
        {
            if (fq_list[j]->stn_node == prop->stn_node)
                continue;

            if (fq_list[j]->rx_channel == p_tx_channel)
                continue;

            if (j == i)
                continue;


/*
{
int il,ih;
            il = fq_list[j]->rx_channel - bw3db_channel;
            ih = fq_list[j]->rx_channel + bw3db_channel;
printf("TX2 freq c1 c2 lo hi: %lf %d %d %d %d\n", fq_list[j]->rx_freq, channel1, fq_list[j]->rx_channel, il, ih);
}
*/
            if ((harmonic >= (fq_list[j]->rx_channel - bw3db_channel))
            &&  (harmonic <= (fq_list[j]->rx_channel + bw3db_channel)))
            {
/*
printf("within harmonic\n");
*/
                eptr2 = exist[fq_list[j]->stn_node];

#ifdef BATCHED_EMC
                intrfr_flag1 = intrfr_flag2 = FALSE;
                e_idx = fq_list[j]->stn_node;
                exist_rx_freq = fq_list[j]->rx_freq;
                set_victim_exist();
                base_to_base(&intrfr_flag1, INTERMOD);
                power_intr2 = power_intr;
                set_intr_exist(i);
                base_to_base(&intrfr_flag2, INTERMOD);
                power_intr1 = power_intr;
                if (intrfr_flag1 && intrfr_flag2)
                {
                    print_intermod2_line(mfp,
                                         &line_cnt,
                                         &page_cnt,
                                         fq_list[i]->tx_freq,
                                         prop_tx_freq,
                                         fq_list[j]->rx_freq,
                                         harmonic,
                                         TRANSMITTER_2);
                    intmod2_tx2_tot++;
                }
#endif

#ifdef COSITE 
                print_intermod2_line(mfp,
                                     &line_cnt,
                                     &page_cnt,
                                     fq_list[i]->tx_freq,
                                     prop_tx_freq,
                                     fq_list[j]->rx_freq,
                                     harmonic,
                                     TRANSMITTER_2);
                intmod2_tx2_tot++;
#endif

            }
        }
    }


#ifdef BATCHED_EMC
    if (intermod_cnt == 0)
    {
        fprintf(afp, "No intermod found\n");
        fclose(mfp);
        unlink(im2_fname);
    }
    else
    {
        print_intermod_total(line_cnt, EMC_INTERMOD2_LINES, mfp);
        fclose(mfp);
/*
        sprintf(cmdline, "lp -dprinter1 %s > /dev/null", im2_fname);
*/
        sprintf(cmdline, "cat %s >> %s", im2_fname, print_file);
        system(cmdline);
    }
#endif

#ifdef COSITE
    if (intermod_cnt == 0)
    {
        fclose(mfp);
        unlink(im2_fname);
    }
    else
    {
        print_intermod_total(line_cnt, COSITE_INTERMOD2_LINES, mfp);
        fclose(mfp);
        sprintf(cmdline, "lp -dprinter1 %s > /dev/null", im2_fname);
        system(cmdline);
    }
#endif


intermod2_end:
    ;

}


print_intermod2_line(mfp, line_cnt, page_cnt, tx_freq1, tx_freq2, rx_freq, 
                     harmonic, mode)
FILE    *mfp;
int     *line_cnt;
int     *page_cnt;
double  tx_freq1;
double  tx_freq2;
double  rx_freq;
int     harmonic;
int     mode;
{
    float   tx_grid[2], rx_grid[2];
    char    e1_system_id[15], e2_system_id[15];


/*
#ifdef DEBUG
    printf("print_intermod2_line\n");
#endif
*/

    if (*line_cnt == 0)
    {
        print_intermod2_head(mfp, page_cnt, mode);
        (*page_cnt)++;
    }
fflush(mfp);

    delta_freq1 = abs(tx_freq1 - rx_freq);
    delta_freq2 = abs(tx_freq2 - rx_freq);
    sprintf(e1_system_id, "%c%s%s-%s", eptr1->sys_category, eptr1->sys_type,
            eptr1->sys_no, eptr1->sys_suffix);
    sprintf(e2_system_id, "%c%s%s-%s", eptr2->sys_category, eptr2->sys_type,
            eptr2->sys_no, eptr2->sys_suffix);


#ifdef BATCHED_EMC

    switch (mode)
    {
        case RECEIVER:
        {
	    tx_grid[0]  = (float)eptr1->east_grid / 10;
	    tx_grid[1]  = (float)eptr1->north_grid / 10;
            rx_grid[0]  = (float)prop->east_grid / 10;
            rx_grid[1]  = (float)prop->north_grid / 10;
            delta_dist1 = cal_dist(rx_grid, tx_grid);

	    tx_grid[0]  = (float)eptr2->east_grid / 10;
	    tx_grid[1]  = (float)eptr2->north_grid / 10;
            rx_grid[0]  = (float)prop->east_grid / 10;
            rx_grid[1]  = (float)prop->north_grid / 10;
            delta_dist2 = cal_dist(rx_grid, tx_grid);

            break;
        }
        case TRANSMITTER_1:
        {
            tx_grid[0]  = (float)prop->east_grid / 10;
            tx_grid[1]  = (float)prop->north_grid / 10;
	    rx_grid[0]  = (float)eptr2->east_grid / 10;
	    rx_grid[1]  = (float)eptr2->north_grid / 10;
            delta_dist1 = cal_dist(rx_grid, tx_grid);

	    tx_grid[0]  = (float)eptr1->east_grid / 10;
	    tx_grid[1]  = (float)eptr1->north_grid / 10;
	    rx_grid[0]  = (float)eptr2->east_grid / 10;
	    rx_grid[1]  = (float)eptr2->north_grid / 10;
            delta_dist2 = cal_dist(rx_grid, tx_grid);

            break;
        }
        case TRANSMITTER_2:
        {
	    tx_grid[0]  = (float)eptr1->east_grid / 10;
	    tx_grid[1]  = (float)eptr1->north_grid / 10;
	    rx_grid[0]  = (float)eptr2->east_grid / 10;
	    rx_grid[1]  = (float)eptr2->north_grid / 10;
            delta_dist1 = cal_dist(rx_grid, tx_grid);

            tx_grid[0]  = (float)prop->east_grid / 10;
            tx_grid[1]  = (float)prop->north_grid / 10;
	    rx_grid[0]  = (float)eptr2->east_grid / 10;
	    rx_grid[1]  = (float)eptr2->north_grid / 10;
            delta_dist2 = cal_dist(rx_grid, tx_grid);

            break;
        }
    }

    /**********************************************************/
    /* for prop. station as TRANSMITTER_1, tx_freq1 belongs   */
    /* to prop station, tx_freq2 belongs to station1 (eptr1)  */
    /**********************************************************/
    if (mode == TRANSMITTER_1)
        fprintf(mfp, "%-14s %4d %11.5lf",
                e1_system_id, eptr1->base_no, tx_freq2);
    else
        fprintf(mfp, "%-14s %4d %11.5lf",
                e1_system_id, eptr1->base_no, tx_freq1);

    fprintf(mfp, "%3s%-14s %4d ", "", e2_system_id, eptr2->base_no);

    if (mode == RECEIVER)
        fprintf(mfp, "%11.5lf", tx_freq2);

    if ((mode == TRANSMITTER_1) || (mode == TRANSMITTER_2))
        fprintf(mfp, "%11.5lf", rx_freq);

    fprintf(mfp, "%4s%4.2f%3s%4.2f", "", delta_dist1, "", delta_dist2);
    fprintf(mfp, "%3s%7.5f%2s%7.5f%2s%11.5lf%3s%6.1f%2s%6.1f",
            "", delta_freq1, "", delta_freq2, "",
            ((double)harmonic * MIN_CHANNEL_SEP), "", power_intr1,
            "", power_intr2);

    /**************************************/
    /* highlight adjacent channel, if any */
    /**************************************/
    if ((delta_freq1 <= MIN_CURVE_LEVEL + FREQ_EPSILON) 
    ||  (delta_freq2 <= MIN_CURVE_LEVEL + FREQ_EPSILON))
        fprintf(mfp, "#\n");
    else
        fprintf(mfp, "\n");

    *line_cnt = (*line_cnt + 1) % EMC_INTERMOD2_LINES;

    max_delta_freq = max(delta_freq1, max_delta_freq);
    max_delta_freq = max(delta_freq2, max_delta_freq);
    min_delta_freq = min(delta_freq1, min_delta_freq);
    min_delta_freq = min(delta_freq2, min_delta_freq);

    max_power_intr = max(power_intr1, max_power_intr);
    max_power_intr = max(power_intr2, max_power_intr);
    min_power_intr = min(power_intr1, min_power_intr);
    min_power_intr = min(power_intr2, min_power_intr);

#endif


#ifdef COSITE

    /**********************************************************/
    /* for prop. station as TRANSMITTER_1, tx_freq1 belongs   */
    /* to prop station, tx_freq2 belongs to station1 (eptr1)  */
    /**********************************************************/
    if (mode == TRANSMITTER_1)
        fprintf(mfp, "%-14s%3s%4d%3s%11.5lf",
                e1_system_id, "", eptr1->base_no, "", tx_freq2);
    else
        fprintf(mfp, "%-14s%3s%4d%3s%11.5lf",
                e1_system_id, "", eptr1->base_no, "", tx_freq1);

    fprintf(mfp, "%5s%-14s%3s%4d%3s", "", e2_system_id, "", eptr2->base_no, "");

    if (mode == RECEIVER)
        fprintf(mfp, "%11.5lf", tx_freq2);

    if ((mode == TRANSMITTER_1) || (mode == TRANSMITTER_2))
        fprintf(mfp, "%11.5lf", rx_freq);

    fprintf(mfp, "%5s%7.5f%5s%7.5f%5s%11.5lf",
            "", delta_freq1, "", delta_freq2, "",
            ((double)harmonic * MIN_CHANNEL_SEP));

    /**************************************/
    /* highlight adjacent channel, if any */
    /**************************************/
    if ((delta_freq1 <= MIN_CURVE_LEVEL) || (delta_freq2 <= MIN_CURVE_LEVEL))
        fprintf(mfp, "#\n");
    else
        fprintf(mfp, "\n");

    *line_cnt = (*line_cnt + 1) % COSITE_INTERMOD2_LINES;

    max_delta_freq = max(delta_freq1, max_delta_freq);
    max_delta_freq = max(delta_freq2, max_delta_freq);
    min_delta_freq = min(delta_freq1, min_delta_freq);
    min_delta_freq = min(delta_freq2, min_delta_freq);

#endif


    intermod_cnt++;
}


/*****************************************************************/
/* print 1st part of 2-signal intermod report heading            */
/*****************************************************************/

print_intermod2_head(mfp, page_cnt, mode)
FILE    *mfp;
int     *page_cnt;
int     mode;
{

/*
#ifdef DEBUG
    printf("print_intermod2_head\n");
#endif
*/

   if (*page_cnt > 1)
       fprintf(mfp, "\f");

   fprintf(mfp, "RUN DATE: %s%14s", sys_date, "");
   fprintf(mfp,
          "*************************************************************"); 
   fprintf(mfp, "%22sPAGE   : %-d\n", "", *page_cnt);
   fprintf(mfp, "RUN TIME: %s%14s", sys_time, "");
   fprintf(mfp,
          "*                                                           *");
   fprintf(mfp, "%22sPROGRAM: esemim0r\n", "");
   fprintf(mfp, "USER ID : %-19s%3s", emc_uid, "");


#ifdef BATCHED_EMC

   fprintf(mfp,
     "*               EMC ANALYSIS - INTERMODULATION              *\n");
   fprintf(mfp, "%32s", "");
   fprintf(mfp,
     "*            2-SIGNAL FREQUENCY COMBINATION REPORT          *\n");
   fprintf(mfp, "%32s", "");
   switch (mode)
   {
       case RECEIVER:
           emcmod2_vict_head(mfp);
           break;
       case TRANSMITTER_1:
           emcmod2_tx1_head(mfp);
           break;
       case TRANSMITTER_2:
           emcmod2_tx2_head(mfp);
           break;
   }

#endif


#ifdef COSITE

   fprintf(mfp,
     "*             CO-SITE ANALYSIS - INTERMODULATION            *\n");
   fprintf(mfp, "%32s", "");
   fprintf(mfp, 
     "*           2-SIGNAL FREQUENCY COMBINATION REPORT           *\n");
   fprintf(mfp, "%32s", "");
   switch (mode)
   {
       case RECEIVER:
           cosmod2_vict_head(mfp);
           break;
       case TRANSMITTER_1:
           cosmod2_tx1_head(mfp);
           break;
       case TRANSMITTER_2:
           cosmod2_tx2_head(mfp);
           break;
   }

#endif


}


#ifdef BATCHED_EMC


/**********************************************************************/
/* print 2-signal intermod report heading (prop. station as RECEIVER) */
/**********************************************************************/

emcmod2_vict_head(mfp)
FILE    *mfp;
{

/*
#ifdef DEBUG
    printf("emcmod2_vict_head\n");
#endif
*/

    fprintf(mfp, "*%15s(PROPOSED STATION IS RECEIVER)%14s*\n", "", "");
    fprintf(mfp, "%32s*%59s*\n", "", "");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, "*%16sPROPOSED STATION INFORMATION%15s*\n", "", "");
    fprintf(mfp, "%32s*%59s*\n", "", "");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, "* PROP. SYSTEM    : %c%s%s-%s%29s*\n", prop->sys_category,
           prop->sys_type, prop->sys_no, prop->sys_suffix, "");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, "* RX FREQ (MHz)   :%11.5lf  TX FREQ (MHz)  :%11.5lf *",
            prop_rx_freq, prop_tx_freq);
    fprintf(mfp, "  DELTA_1 = ABS(TX_1 - PROP RX FREQ)\n");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, "* GRID EAST       :      %5d  GRID NORTH     :      %5d *",
            prop->east_grid, prop->north_grid);
    fprintf(mfp, "  DELTA_2 = ABS(TX_2 - PROP RX FREQ)\n");
    fprintf(mfp, "'#' : DELTA_1/DELTA_2 < 30KHz   ");
    fprintf(mfp, "* SUB-DISTRICT    :%8s%3s  ERP (dBW)      :%5s%6.2f *\n",
            "", prop->sub_district, "", prop->pw_dbw);
    fprintf(mfp, "DIST1(KM) = DIST. %% TX1 AND     ");
    fprintf(mfp, "* ANTENNA HT (M)  :%8s%3d  TERRAIN HT (M) :%6s%5.1f *",
            "", prop->ant_height, "", prop->height_asl);
    fprintf(mfp, "  PIN_1 = INTERFERENCE POWER OF\n");
    fprintf(mfp, "            PROP. STATION       ");
    fprintf(mfp, "* ANTENNA TYPE    :%9s%2s  AZ OF MAX RAD  :        %3d *",
            "", prop->antenna, prop->az_max_rad);
    fprintf(mfp, "          TX_1 TO PROP RX FREQ\n");
    fprintf(mfp, "DIST2(KM) = DIST. %% TX2 AND     ");
    (prop->sfx_filter[0] != '\0') ?
       fprintf(mfp, "* FEED LOSS (DB)  :        %3d  SFX FILTER     : %10s *",
               prop->feed_loss, prop->sfx_filter):
       fprintf(mfp, "* FEED LOSS (DB)  :        %3d%30s*", prop->feed_loss, "");
    fprintf(mfp, "  PIN_2 = INTERFERENCE POWER OF\n");
    fprintf(mfp, "            PROP. STATION       ");
    fprintf(mfp, "* INTMOD ADJ (dB) :%5s%6.1f  STATION TYPE   : %9s%c *", 
            "", prop->intmod_att_db, "", prop->station_type);
    fprintf(mfp, "          TX_2 TO PROP RX FREQ\n");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, 
            "*************************************************************\n");
    fprintf(mfp, "\n\n");
    fprintf(mfp, "%5sTRANSMITTER #1 (TX1)%14sTRANSMITTER #2 (TX2)\n", "", "");
    fprintf(mfp, "===============================");
    fprintf(mfp, "%3s===============================\n", "");
    fprintf(mfp, "SYSTEM-ID%6sBASE%3sTX FREQ 1", "", "");
    fprintf(mfp, "%3sSYSTEM-ID%6sBASE%3sTX FREQ 2", "", "", "");
    fprintf(mfp, "%3sDIST1%2sDIST2", "", "");
    fprintf(mfp, "%3sDELTA_1%2sDELTA_2%5sINTERMOD", "", "", "");
    fprintf(mfp, "%4sPIN_1%3sPIN_2\n", "", "");
    fprintf(mfp, "%16sNO.%5s(TX_1)", "", "");
    fprintf(mfp, "%20sNO.%5s(TX_2)", "", "");
    fprintf(mfp, "%20s(MHz)%4s(MHz)%7sPRODUCT", "", "", "");
    fprintf(mfp, "%4s(dBW)%3s(dBW)\n\n", "", "");
}


/***************************************************************************/
/* print 2-signal intermod report heading (prop. station as TRANSMITTER_1) */
/***************************************************************************/

emcmod2_tx1_head(mfp)
FILE    *mfp;
{

/*
#ifdef DEBUG
    printf("emcmod2_tx1_head\n");
#endif
*/

    fprintf(mfp, "*%12s(PROPOSED STATION IS TRANSMITTER  #1)%10s*\n", "", "");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, "*%59s*\n", "");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, "*%16sPROPOSED STATION INFORMATION%15s*\n", "", "");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, "*%59s*\n", "");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, "* PROP. SYSTEM    : %c%s%s-%s%29s*\n", prop->sys_category,
           prop->sys_type, prop->sys_no, prop->sys_suffix, "");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, "* RX FREQ (MHz)   :%11.5lf  TX FREQ (MHz)  :%11.5lf *",
            prop_rx_freq, prop_tx_freq);
    fprintf(mfp, "  DELTA_1 = ABS(PROP TX FREQ - RX FREQ)\n");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, "* GRID EAST       :      %5d  GRID NORTH     :      %5d *",
            prop->east_grid, prop->north_grid);
    fprintf(mfp, "  DELTA_2 = ABS(TX_2 - RX FREQ)\n");
    fprintf(mfp, "'#' : DELTA_1/DELTA_2 < 30KHz   ");
    fprintf(mfp, "* SUB-DISTRICT    :%8s%3s  ERP (dBW)      :%5s%6.2f *\n",
            "", prop->sub_district, "", prop->pw_dbw);
    fprintf(mfp, "DIST1(KM) = DIST. %% RECEIVER    ");
    fprintf(mfp, "* ANTENNA HT (M)  :%8s%3d  TERRAIN HT (M) :%6s%5.1f *",
            "", prop->ant_height, "", prop->height_asl);
    fprintf(mfp, "  PIN_1 = INTERFERENCE POWER OF\n");
    fprintf(mfp, "            AND PROP. STATION   ");
    fprintf(mfp, "* ANTENNA TYPE    :%9s%2s  AZ OF MAX RAD  :        %3d *",
            "", prop->antenna, prop->az_max_rad);
    fprintf(mfp, "          PROP TX FREQ TO RX FREQ\n");
    fprintf(mfp, "DIST2(KM) = DIST. %% RECEIVER    ");
    (prop->sfx_filter[0] != '\0') ?
       fprintf(mfp, "* FEED LOSS (DB)  :        %3d  SFX FILTER     : %10s *",
               prop->feed_loss, prop->sfx_filter):
       fprintf(mfp, "* FEED LOSS (DB)  :        %3d%30s*", prop->feed_loss, "");
    fprintf(mfp, "  PIN_2 = INTERFERENCE POWER OF\n");
    fprintf(mfp, "            AND TX2             ");
    fprintf(mfp, "* INTMOD ADJ (dB) :%5s%6.1f  STATION TYPE   : %9s%c *", 
            "", prop->intmod_att_db, "", prop->station_type);
    fprintf(mfp, "          TX_2 TO RX FREQ\n");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, 
            "*************************************************************\n");
    fprintf(mfp, "\n\n\n");

    fprintf(mfp, "%5sTRANSMITTER #2 (TX2)%21sRECEIVER\n", "", "");
    fprintf(mfp, "===============================");
    fprintf(mfp, "%3s===============================\n", "");
    fprintf(mfp, "SYSTEM-ID%6sBASE%3sTX FREQ 2", "", "");
    fprintf(mfp, "%3sSYSTEM-ID%6sBASE%5sRX FREQ", "", "", "");
    fprintf(mfp, "%3sDIST1%2sDIST2", "", "");
    fprintf(mfp, "%3sDELTA_1%2sDELTA_2%5sINTERMOD", "", "", "");
    fprintf(mfp, "%4sPIN_1%3sPIN_2\n", "", "");
    fprintf(mfp, "%16sNO.%5s(TX_2)", "", "");
    fprintf(mfp, "%20sNO.%31s", "", "");
    fprintf(mfp, "(MHz)%4s(MHz)%7sPRODUCT", "", "");
    fprintf(mfp, "%4s(dBW)%3s(dBW)\n\n", "", "");
}


/***************************************************************************/
/* print 2-signal intermod report heading (prop. station as TRANSMITTER_2) */
/***************************************************************************/

emcmod2_tx2_head(mfp)
FILE    *mfp;
{

/*
#ifdef DEBUG
    printf("emcmod2_tx2_head\n");
#endif
*/

    fprintf(mfp, "*%12s(PROPOSED STATION IS TRANSMITTER  #2)%10s*\n", "", "");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, "*%59s*\n", "");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, "*%16sPROPOSED STATION INFORMATION%15s*\n", "", "");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, "*%59s*\n", "");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, "* PROP. SYSTEM    : %c%s%s-%s%29s*\n", prop->sys_category,
           prop->sys_type, prop->sys_no, prop->sys_suffix, "");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, "* RX FREQ (MHz)   :%11.5lf  TX FREQ (MHz)  :%11.5lf *",
            prop_rx_freq, prop_tx_freq);
    fprintf(mfp, "  DELTA_1 = ABS(TX_1 - RX FREQ)\n");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, "* GRID EAST       :      %5d  GRID NORTH     :      %5d *",
            prop->east_grid, prop->north_grid);
    fprintf(mfp, "  DELTA_2 = ABS(PROP TX FREQ - RX FREQ)\n");
    fprintf(mfp, "'#' : DELTA_1/DELTA_2 < 30KHz   ");
    fprintf(mfp, "* SUB-DISTRICT    :%8s%3s  ERP (dBW)      :%5s%6.2f *\n",
            "", prop->sub_district, "", prop->pw_dbw);
    fprintf(mfp, "DIST1(KM) = DIST. %% RECEIVER    ");
    fprintf(mfp, "* ANTENNA HT (M)  :%8s%3d  TERRAIN HT (M) :%6s%5.1f *",
            "", prop->ant_height, "", prop->height_asl);
    fprintf(mfp, "  PIN_1 = INTERFERENCE POWER OF\n");
    fprintf(mfp, "            AND TX1             ");
    fprintf(mfp, "* ANTENNA TYPE    :%9s%2s  AZ OF MAX RAD  :        %3d *",
            "", prop->antenna, prop->az_max_rad);
    fprintf(mfp, "          TX_1 TO RX FREQ\n");
    fprintf(mfp, "DIST2(KM) = DIST. %% RECEIVER    ");
    (prop->sfx_filter[0] != '\0') ?
       fprintf(mfp, "* FEED LOSS (DB)  :        %3d  SFX FILTER     : %10s *",
               prop->feed_loss, prop->sfx_filter):
       fprintf(mfp, "* FEED LOSS (DB)  :        %3d%30s*", prop->feed_loss, "");
    fprintf(mfp, "  PIN_2 = INTERFERENCE POWER OF\n");
    fprintf(mfp, "            AND PROP. STATION   ");
    fprintf(mfp, "* INTMOD ADJ (dB) :%5s%6.1f  STATION TYPE   : %9s%c *", 
            "", prop->intmod_att_db, "", prop->station_type);
    fprintf(mfp, "          PROP TX FREQ TO RX FREQ\n");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, 
            "*************************************************************\n");
    fprintf(mfp, "\n\n\n");
    fprintf(mfp, "%5sTRANSMITTER #1 (TX1)%21sRECEIVER\n", "", "");
    fprintf(mfp, "===============================");
    fprintf(mfp, "%3s===============================\n", "");
    fprintf(mfp, "SYSTEM-ID%6sBASE%3sTX FREQ 1", "", "");
    fprintf(mfp, "%3sSYSTEM-ID%6sBASE%5sRX FREQ", "", "", "");
    fprintf(mfp, "%3sDIST1%2sDIST2", "", "");
    fprintf(mfp, "%3sDELTA_1%2sDELTA_2%5sINTERMOD", "", "", "");
    fprintf(mfp, "%4sPIN_1%3sPIN_2\n", "", "");
    fprintf(mfp, "%16sNO.%5s(TX_1)", "", "");
    fprintf(mfp, "%20sNO.%31s", "", "");
    fprintf(mfp, "(MHz)%4s(MHz)%7sPRODUCT", "", "");
    fprintf(mfp, "%4s(dBW)%3s(dBW)\n\n", "", "");
}

#endif


#ifdef COSITE


/**********************************************************************/
/* print 3-signal intermod report heading (prop. station as RECEIVER) */
/**********************************************************************/

cosmod2_vict_head(mfp)
FILE    *mfp;
{

/*
#ifdef DEBUG
    printf("cosmod2_vict_head\n");
#endif
*/

    fprintf(mfp, "*%15s(PROPOSED STATION IS RECEIVER)%14s*\n", "", "");
    fprintf(mfp, "%32s*%59s*\n", "", "");
    fprintf(mfp, "%32s*%21sSUB-DISTRICT : %-3s%20s*\n", 
            "", "", sub_district, "");
    fprintf(mfp, "%32s*%59s*\n", "", "");
    fprintf(mfp, "%32s*  RX FREQ (MHz) :%11.5lf   TX FREQ (MHz) :%11.5lf  *",
            "", prop_rx_freq, prop_tx_freq);
    fprintf(mfp, "  DELTA_1 = ABS(TX_1 - PROP RX FREQ)\n");
    fprintf(mfp, "'#' : DELTA_1/DELTA_2 < 30KHz   ");
    fprintf(mfp, "*%59s*", "", "");
    fprintf(mfp, "  DELTA_2 = ABS(TX_2 - PROP RX FREQ)\n");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, 
            "*************************************************************\n");
    fprintf(mfp, "\n\n\n");
/*
    fprintf(mfp, "%18sTRANSMITTER #1 (TX1)%17sTRANSMITTER #2 (TX2)\n", "", "");
    fprintf(mfp, "%13s==============================", "");
    fprintf(mfp, "%7s==============================\n", "");
    fprintf(mfp, "%13sLICENCE%6sSTN%5sTX FREQ 1", "", "", "");
    fprintf(mfp, "%7sLICENCE%6sSTN%5sTX FREQ 2", "", "", "");
    fprintf(mfp, "%5sDELTA_1%4sDELTA_2%7sINTERMOD\n", "", "", "");
    fprintf(mfp, "%13sNUMBER%7sTYPE%5s(TX_1)", "", "", "");
    fprintf(mfp, "%9sNUMBER%7sTYPE%5s(TX_2)", "", "", "");
    fprintf(mfp, "%7s(MHz)%6s(MHz)%9sPRODUCT\n\n", "", "", "");
*/
    fprintf(mfp, "%10sTRANSMITTER #1 (TX1)%23sTRANSMITTER #2 (TX2)\n", "", "");
    fprintf(mfp, "===================================");
    fprintf(mfp, "%5s===================================\n", "");
    fprintf(mfp, "SYSTEM-ID%8sBASE%5sTX FREQ 1", "", "");
    fprintf(mfp, "%5sSYSTEM-ID%8sBASE%5sTX FREQ 2", "", "", "");
    fprintf(mfp, "%5sDELTA_1%5sDELTA_2%8sINTERMOD\n", "", "", "");
    fprintf(mfp, "%18sNO.%7s(TX_1)", "", "");
    fprintf(mfp, "%24sNO.%7s(TX_2)", "", "");
    fprintf(mfp, "%7s(MHz)%7s(MHz)%10sPRODUCT\n\n", "", "", "");
}


/***************************************************************************/
/* print 2-signal intermod report heading (prop. station as TRANSMITTER_1) */
/***************************************************************************/

cosmod2_tx1_head(mfp)
FILE    *mfp;
{

/*
#ifdef DEBUG
    printf("cosmod2_tx1_head\n");
#endif
*/

    fprintf(mfp, "*%12s(PROPOSED STATION IS TRANSMITTER  #1)%10s*\n", "", "");
    fprintf(mfp, "%32s*%59s*\n", "", "");
    fprintf(mfp, "%32s*%21sSUB-DISTRICT : %-3s%20s*\n", 
            "", "", sub_district, "");
    fprintf(mfp, "%32s*%59s*\n", "", "");
    fprintf(mfp, "%32s*  RX FREQ (MHz) :%11.5lf   TX FREQ (MHz) :%11.5lf  *",
            "", prop_rx_freq, prop_tx_freq);
    fprintf(mfp, "  DELTA_1 = ABS(PROP TX FREQ - RX FREQ)\n");
    fprintf(mfp, "'#' : DELTA_1/DELTA_2 < 30KHz   ");
    fprintf(mfp, "*%59s*", "", "");
    fprintf(mfp, "  DELTA_2 = ABS(TX_2 - RX FREQ)\n");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, 
            "*************************************************************\n");
    fprintf(mfp, "\n\n\n");
/*
    fprintf(mfp, "%18sTRANSMITTER #2 (TX2)%23sRECEIVER\n", "", "");
    fprintf(mfp, "%13s==============================", "");
    fprintf(mfp, "%7s=============================\n", "");
    fprintf(mfp, "%13sLICENCE%6sSTN%5sTX FREQ 2", "", "", "");
    fprintf(mfp, "%7sLICENCE%6sSTN%14s", "", "", "");
    fprintf(mfp, "%5sDELTA_1%4sDELTA_2%7sINTERMOD\n", "", "", "");
    fprintf(mfp, "%13sNUMBER%7sTYPE%5s(TX_2)", "", "", "");
    fprintf(mfp, "%9sNUMBER%7sTYPE%6sRX FREQ", "", "", "");
    fprintf(mfp, "%5s(MHz)%6s(MHz)%9sPRODUCT\n\n", "", "", "");
*/
    fprintf(mfp, "%10sTRANSMITTER #2 (TX1)%29sRECEIVER\n", "", "");
    fprintf(mfp, "===================================");
    fprintf(mfp, "%5s===================================\n", "");
    fprintf(mfp, "SYSTEM-ID%8sBASE%5sTX FREQ 2", "", "");
    fprintf(mfp, "%5sSYSTEM-ID%8sBASE%7sRX FREQ", "", "", "");
    fprintf(mfp, "%5sDELTA_1%5sDELTA_2%8sINTERMOD\n", "", "", "");
    fprintf(mfp, "%18sNO.%7s(TX_2)", "", "");
    fprintf(mfp, "%24sNO.%20s", "", "");
    fprintf(mfp, "(MHz)%7s(MHz)%10sPRODUCT\n\n", "", "", "");
}


/***************************************************************************/
/* print 2-signal intermod report heading (prop. station as TRANSMITTER_3) */
/***************************************************************************/

cosmod2_tx2_head(mfp)
FILE    *mfp;
{

/*
#ifdef DEBUG
    printf("comod2_tx2_head\n");
#endif
*/

    fprintf(mfp, "*%12s(PROPOSED STATION IS TRANSMITTER  #2)%10s*\n", "", "");

    fprintf(mfp, "%32s*%59s*\n", "", "");
    fprintf(mfp, "%32s*%21sSUB-DISTRICT : %-3s%20s*\n", 
            "", "", sub_district, "");
    fprintf(mfp, "%32s*%59s*\n", "", "");
    fprintf(mfp, "%32s*  RX FREQ (MHz) :%11.5lf   TX FREQ (MHz) :%11.5lf  *",
            "", prop_rx_freq, prop_tx_freq);
    fprintf(mfp, "  DELTA_1 = ABS(TX_1 - RX FREQ)\n");
    fprintf(mfp, "'#' : DELTA_1/DELTA_2 < 30KHz   ");
    fprintf(mfp, "*%59s*", "", "");
    fprintf(mfp, "  DELTA_2 = ABS(PROP TX FREQ - RX FREQ)\n");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, 
            "*************************************************************\n");
    fprintf(mfp, "\n\n\n");
/*
    fprintf(mfp, "%18sTRANSMITTER #1 (TX1)%23sRECEIVER\n", "", "");
    fprintf(mfp, "%13s==============================", "");
    fprintf(mfp, "%7s==============================\n", "");
    fprintf(mfp, "%13sLICENCE%6sSTN%5sTX FREQ 1", "", "", "");
    fprintf(mfp, "%7sLICENCE%6sSTN%14s", "", "", "");
    fprintf(mfp, "%5sDELTA_1%4sDELTA_2%7sINTERMOD\n", "", "", "");
    fprintf(mfp, "%13sNUMBER%7sTYPE%5s(TX_1)", "", "", "");
    fprintf(mfp, "%9sNUMBER%7sTYPE%6sRX FREQ", "", "", "");
    fprintf(mfp, "%5s(MHz)%6s(MHz)%9sPRODUCT\n\n", "", "", "");
*/
    fprintf(mfp, "%10sTRANSMITTER #1 (TX1)%29sRECEIVER\n", "", "");
    fprintf(mfp, "===================================");
    fprintf(mfp, "%5s===================================\n", "");
    fprintf(mfp, "SYSTEM-ID%8sBASE%5sTX FREQ 1", "", "");
    fprintf(mfp, "%5sSYSTEM-ID%8sBASE%7sRX FREQ", "", "", "");
    fprintf(mfp, "%5sDELTA_1%5sDELTA_2%8sINTERMOD\n", "", "", "");
    fprintf(mfp, "%18sNO.%7s(TX_1)", "", "");
    fprintf(mfp, "%24sNO.%20s", "", "");
    fprintf(mfp, "(MHz)%7s(MHz)%10sPRODUCT\n\n", "", "", "");
}

#endif


/**********************************************************************/
/*                                                                    */
/*    Module Name   :  intermod_3 (esemim0r.c)                        */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. H. Mak                                      */
/*                                                                    */
/*    Callers       :  main of batch EMC module (esemba0x.c)          */  
/*                     main of co-site analysis module (esemcs0f.c)   */
/*                                                                    */
/*    Parameters    :                                                 */
/*                                                                    */
/*    Called Modules:  base_to_base (esemin0x.pc)                     */
/*                                                                    */
/*    Purpose       :  Perform 3-signal intermodulation analysis      */
/*                     together with base-to-base interference        */
/*                     analysis to proposed and existing station.     */
/*                     Report intermod details if interference power  */
/*                     is above pre-defined level.                    */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

intermod_3()
{
    char       im3_fname[120];
    char       cmdline[140];
    FILE       *mfp;
    int        p_tx_channel, p_rx_channel;
    int        cull_channel;
    int        channel_diff;
    int        bw3db_channel;
    int        harmonic_hi, harmonic_lo;
    int        h, harmonic;
    int        intrfr_flag1, intrfr_flag2, intrfr_flag3;
    int        i, j, k;
    int        line_cnt;
    int        page_cnt;


#ifdef BATCHED_EMC

    if (interactive == FALSE)
    {
        printf("\n\n\n***  INTERMOD_3\n\n");
        printf("Formula used:\n");
/*
printf("if (Pin - min sig > INTERMOD_DB), then set flag to TRUE\n");
printf("where INTERMOD_DB = -14dB currently\n");
printf("Pin = erp - feed_loss + ant - off - sfx - prop\n");;
*/
        printf("if (Pin > intmod_att_db + curve_adj), ");
        printf("then set flag to TRUE\n");
        printf("intmod_att_db = %-6.2f\n", prop->intmod_att_db);
        printf("Pin = erp - feed_loss + ant - sfx - prop\n");;
        printf("prop = 28.1 + a + b + diff loss + CLUTTER_LOSS\n");
        printf("where a = 20.0*log10(intr.tx_freq), b = 20.0*log_delta_dist\n\n");
        printf(" intr.       vict.\n");
        printf("tx freq    rx freq    min sig  Pin    erp   feed  ant  curve_adj  sfx    prop    a      b    diff loss flag\n");
        printf("========== ========== ======= ====== ====== ==== ===== ========= ====== ====== ====== ====== ========= ====\n");
    }

#endif


    max_delta_freq = -999; min_delta_freq = 999;
    max_power_intr = -999; min_power_intr = 999;

/*
#ifdef DEBUG
    printf("intermod_3\n");
#endif
*/

    cull_channel = (int)(intermod3_cull_freq/MIN_CHANNEL_SEP + .5);

    p_tx_channel = (int)(prop_tx_freq/MIN_CHANNEL_SEP + .5);
    p_rx_channel = (int)(prop_rx_freq/MIN_CHANNEL_SEP + .5);


#ifdef BATCHED_EMC
    bw3db_channel = (int)(bw3db[prop->band]/MIN_CHANNEL_SEP + .005);
#endif

#ifdef COSITE
    bw3db_channel = (int)(bw3db[freq_band]/MIN_CHANNEL_SEP + .005);
#endif


    /*************************************************************/
    /*     Intermodulation with proposed station as victim       */
    /*************************************************************/


#ifdef BATCHED_EMC

    fprintf(afp, "\n** 3-signal Intermodulation report (victim)\n");
    if (prop->mode == TX_ONLY)
    {
        fprintf(afp, "Proposed station is TX_ONLY station, ");
        fprintf(afp, "no intermod for proposed station as victim\n");
        goto prop_tx1;
    }
    sprintf(im3_fname, "%s/intermod3/%.5lf.%s.v", emc_dir, prop_tx_freq,
            hhmmss);
    if ((mfp = fopen(im3_fname, "w")) == (FILE *) NULL)
    {
        fprintf(afp, "Fatal error: fail to open 3-signal intermod ");
        fprintf(afp, "report : %s\n", im3_fname);
        exit(1);
    }

#endif


#ifdef COSITE

    if (tx_mode == TX_ONLY)
        goto prop_tx1;
    
    sprintf(im3_fname, "%s/cosite/intermod3/%s.%.5lf.%s.v", emc_dir, emc_uid,
            prop_tx_freq, sub_district);
    if ((mfp = fopen(im3_fname, "w")) == (FILE *) NULL)
        return(FILE_OPEN_ERROR);

#endif


    harmonic_hi = p_rx_channel + bw3db_channel;
    harmonic_lo = p_rx_channel - bw3db_channel;

    intermod_cnt = 0; line_cnt = 0; page_cnt = 1;
    for (i = 0; i < fq_cnt; i++)
    {
        if (fq_list[i]->stn_node == prop->stn_node)
            continue;

/*
printf("V 2nd loop il ih: %lf %d %d %d\n", fq_list[i]->tx_freq, fq_list[i]->tx_channel, harmonic_lo, harmonic_hi);
*/
        channel_diff = abs(p_rx_channel - fq_list[i]->tx_channel);
/*
        if (channel_diff > cull_channel)
            continue;
*/
        /***********************************************************/
        /* if channel difference between tx station and rx station */
        /* is greater than culling frequency or the 2 stations are */
        /* co-channel stations, skip this tx station               */
        /***********************************************************/
        if ((channel_diff > cull_channel) || (channel_diff == 0))
            continue;

        eptr1 = exist[fq_list[i]->stn_node];

        for (j = i + 1; j < fq_cnt; j++)
        {
            if (fq_list[j]->stn_node == prop->stn_node)
                continue;

/*
printf("V 3rd loop: %lf %d\n", fq_list[j]->tx_freq, fq_list[j]->tx_channel);
*/
            channel_diff = abs(p_rx_channel - fq_list[j]->tx_channel);
/*
            if (channel_diff > cull_channel)
                continue;
*/
            if ((channel_diff > cull_channel) || (channel_diff == 0))
                continue;


            eptr2 = exist[fq_list[j]->stn_node];
            h = fq_list[i]->tx_channel + fq_list[j]->tx_channel;

            for (k = 0; k < fq_cnt; k++)
            {
                if (fq_list[k]->stn_node == prop->stn_node)
                    continue;

                if ((k == j) || (k == i))
                    continue;

                channel_diff = abs(p_rx_channel - fq_list[k]->tx_channel);
/*
                if (channel_diff > cull_channel)
                    continue;
*/
                if ((channel_diff > cull_channel) || (channel_diff == 0))
                    continue;


                harmonic = h - fq_list[k]->tx_channel;
/*
printf("V 4th loop freq3 ch3 harmonic: %lf %d %d\n", fq_list[k]->tx_freq, fq_list[k]->tx_channel, harmonic);
*/
                if ((harmonic >= harmonic_lo)
                &&  (harmonic <= harmonic_hi))
                {
/*
printf("within harmonic\n");
*/
                    eptr3 = exist[fq_list[k]->stn_node];

#ifdef BATCHED_EMC
                    intrfr_flag1 = intrfr_flag2 = intrfr_flag3 = FALSE;
                    e_idx = fq_list[i]->stn_node;
                    exist_tx_freq = fq_list[i]->tx_freq;
                    set_victim_propose();
                    base_to_base(&intrfr_flag1, INTERMOD);
                    power_intr1 = power_intr;

                    e_idx = fq_list[j]->stn_node;
                    exist_tx_freq = fq_list[j]->tx_freq;
                    set_victim_propose();
                    base_to_base(&intrfr_flag2, INTERMOD);
                    power_intr2 = power_intr;

                    e_idx = fq_list[k]->stn_node;
                    exist_tx_freq = fq_list[k]->tx_freq;
                    set_victim_propose();
                    base_to_base(&intrfr_flag3, INTERMOD);
                    power_intr3 = power_intr;
/*
printf("p1 p2 p3: %f %f %f\n", power_intr1, power_intr2, power_intr3);
*/

                    if (intrfr_flag1 && intrfr_flag2 && intrfr_flag3)
                    {
                        print_intermod3_line(mfp,
                                             &line_cnt,
                                             &page_cnt,
                                             fq_list[i]->tx_freq,
                                             fq_list[j]->tx_freq,
                                             fq_list[k]->tx_freq,
                                             prop_rx_freq,
                                             harmonic,
                                             RECEIVER);
                        intmod3_vict_tot++;
                    }
#endif

#ifdef COSITE
                    print_intermod3_line(mfp,
                                         &line_cnt,
                                         &page_cnt,
                                         fq_list[i]->tx_freq,
                                         fq_list[j]->tx_freq,
                                         fq_list[k]->tx_freq,
                                         prop_rx_freq,
                                         harmonic,
                                         RECEIVER);
                    intmod3_vict_tot++;
#endif

                }
            }
        }
    }


#ifdef BATCHED_EMC
    if (intermod_cnt == 0)
    {
        fprintf(afp, "No intermod found\n");
        fclose(mfp);
        unlink(im3_fname);
    }
    else
    {
        print_intermod_total(line_cnt, EMC_INTERMOD3_LINES, mfp);
        fclose(mfp);
/*
        sprintf(cmdline, "lp -dprinter1 %s > /dev/null", im3_fname);
*/
        sprintf(cmdline, "cat %s >> %s", im3_fname, print_file);
        system(cmdline);
    }
#endif

#ifdef COSITE
    if (intermod_cnt == 0)
    {
        fclose(mfp);
        unlink(im3_fname);
    }
    else
    {
        print_intermod_total(line_cnt, COSITE_INTERMOD3_LINES, mfp);
        fclose(mfp);
        sprintf(cmdline, "lp -dprinter1 %s > /dev/null", im3_fname);
        system(cmdline);
    }
#endif


prop_tx1:

    /*************************************************************/
    /*  Intermodulation with proposed station as transmitter #1  */
    /*************************************************************/


#ifdef BATCHED_EMC

    fprintf(afp, "\n** 3-signal Intermodulation report (Tx1)\n");
    if (prop->mode == RX_ONLY)
    {
        fprintf(afp, "Proposed station is RX_ONLY station, no ");
        fprintf(afp, "intermod for proposed station as transmitter\n");
        goto intermod3_end;
    }
    sprintf(im3_fname, "%s/intermod3/%.5lf.%s.t1", emc_dir,
            prop_tx_freq, hhmmss);
    if ((mfp = fopen(im3_fname, "w")) == (FILE *) NULL)
    {
        fprintf(afp, "Fatal error: fail to open 3-signal intermod ");
        fprintf(afp, "report : %s\n", im3_fname);
        exit(1);
    }

#endif


#ifdef COSITE
    
    if (tx_mode == RX_ONLY)
        goto intermod3_end;
    
    sprintf(im3_fname, "%s/cosite/intermod3/%s.%.5lf.%s.t1", emc_dir, emc_uid,
            prop_tx_freq, sub_district);
    if ((mfp = fopen(im3_fname, "w")) == (FILE *) NULL)
        return(FILE_OPEN_ERROR);

#endif


    max_delta_freq = -999; min_delta_freq = 999;
    max_power_intr = -999; min_power_intr = 999;

    intermod_cnt = 0; line_cnt = 0; page_cnt = 1;
    for (i = 0; i < fq_cnt; i++)
    {
        if (fq_list[i]->stn_node == prop->stn_node)
            continue;

/*
printf("TX1 2nd loop: %lf %d\n", fq_list[i]->tx_freq, fq_list[i]->tx_channel);
*/
        channel_diff = abs(p_tx_channel - fq_list[i]->tx_channel);

        /***********************************************************/
        /* if channel difference between tx station and rx station */
        /* is greater than culling frequency, skip this station    */
        /***********************************************************/
        if (channel_diff > cull_channel)
            continue;

        eptr1 = exist[fq_list[i]->stn_node];
        h = p_tx_channel + fq_list[i]->tx_channel;

        for (j = 0; j < fq_cnt; j++)
        {
            if (fq_list[j]->stn_node == prop->stn_node)
                continue;

            if (j == i)
                continue;

/*
printf("TX1 3rd loop: %lf %d\n", fq_list[j]->tx_freq, fq_list[j]->tx_channel);
*/
            channel_diff = abs(p_tx_channel - fq_list[j]->tx_channel);
            if (channel_diff > cull_channel)
                continue;

            eptr2 = exist[fq_list[j]->stn_node];
            harmonic = h - fq_list[j]->tx_channel;

            for (k = 0; k < fq_cnt; k++)
            {
                if (fq_list[k]->stn_node == prop->stn_node)
                    continue;

                /**************************************/
                /*  skip co-channel stations, if any  */
                /**************************************/
                if ((fq_list[k]->rx_channel == p_tx_channel) 
                ||  (fq_list[k]->rx_channel == fq_list[i]->tx_channel) 
                ||  (fq_list[k]->rx_channel == fq_list[j]->tx_channel))
                    continue;

                if ((k == j) || (k == i))
                    continue;

/*
printf("TX1 4th loop: %lf %d\n", fq_list[k]->rx_freq, fq_list[k]->rx_channel);
*/
                if ((abs(p_tx_channel - fq_list[k]->rx_channel) 
                                            >= cull_channel)
                ||  (abs(fq_list[i]->tx_channel - fq_list[k]->rx_channel) 
                                            >= cull_channel)
                ||  (abs(fq_list[j]->tx_channel - fq_list[k]->rx_channel) 
                                            >= cull_channel))
                    continue;

                if ((harmonic >= (fq_list[k]->rx_channel - bw3db_channel))
                &&  (harmonic <= (fq_list[k]->rx_channel + bw3db_channel)))
                {
/*
printf("within harmonic\n");
*/
                    eptr3 = exist[fq_list[k]->stn_node];

#ifdef BATCHED_EMC
                    intrfr_flag1 = intrfr_flag2 = intrfr_flag3 = FALSE;
                    e_idx = fq_list[k]->stn_node;
                    exist_rx_freq = fq_list[k]->rx_freq;
                    set_victim_exist();
                    base_to_base(&intrfr_flag1, INTERMOD);
                    power_intr1 = power_intr;
                    set_intr_exist(i);
                    base_to_base(&intrfr_flag2, INTERMOD);
                    power_intr2 = power_intr;
                    set_intr_exist(j);
                    base_to_base(&intrfr_flag3, INTERMOD);
                    power_intr3 = power_intr;
                    if (intrfr_flag1 && intrfr_flag2 && intrfr_flag3)
                    {
/*
printf("prop_tx_freq tx1 tx2 rx: %lf %lf %lf %lf\n", 
prop_tx_freq, fq_list[i]->tx_freq, fq_list[j]->tx_freq, fq_list[k]->rx_freq);
*/
                        print_intermod3_line(mfp,
                                             &line_cnt,
                                             &page_cnt,
                                             prop_tx_freq,
                                             fq_list[i]->tx_freq,
                                             fq_list[j]->tx_freq,
                                             fq_list[k]->rx_freq,
                                             harmonic,
                                             TRANSMITTER_1);
                        intmod3_tx1_tot++;
                    }
#endif

#ifdef COSITE
                    print_intermod3_line(mfp,
                                         &line_cnt,
                                         &page_cnt,
                                         prop_tx_freq,
                                         fq_list[i]->tx_freq,
                                         fq_list[j]->tx_freq,
                                         fq_list[k]->rx_freq,
                                         harmonic,
                                         TRANSMITTER_1);
                    intmod3_tx1_tot++;
#endif

                }
            }
        }
    }


#ifdef BATCHED_EMC
    if (intermod_cnt == 0)
    {
        fprintf(afp, "No intermod found\n");
        fclose(mfp);
        unlink(im3_fname);
    }
    else
    {
        print_intermod_total(line_cnt, EMC_INTERMOD3_LINES, mfp);
        fclose(mfp);
/*
        sprintf(cmdline, "lp -dprinter1 %s > /dev/null", im3_fname);
*/
        sprintf(cmdline, "cat %s >> %s", im3_fname, print_file);
        system(cmdline);
    }
#endif

#ifdef COSITE
    if (intermod_cnt == 0)
    {
        fclose(mfp);
        unlink(im3_fname);
    }
    else
    {
        print_intermod_total(line_cnt, COSITE_INTERMOD3_LINES, mfp);
        fclose(mfp);
        sprintf(cmdline, "lp -dprinter1 %s > /dev/null", im3_fname);
        system(cmdline);
    }
#endif


    /*************************************************************/
    /*  Intermodulation with proposed station as transmitter #3  */
    /*************************************************************/
        

#ifdef BATCHED_EMC

    fprintf(afp, "\n** 3-signal Intermodulation report (Tx3)\n");
    sprintf(im3_fname, "%s/intermod3/%.5lf.%s.t3", emc_dir, 
            prop_tx_freq, hhmmss);
    if ((mfp = fopen(im3_fname, "w")) == (FILE *) NULL)
    {
        fprintf(afp, "Fatal error: fail to open 3-signal intermod ");
        fprintf(afp, "report : %s\n", im3_fname);
        exit(1);
    }

#endif

#ifdef COSITE

    sprintf(im3_fname, "%s/cosite/intermod3/%s.%.5lf.%s.t3", emc_dir, emc_uid,
            prop_tx_freq, sub_district);
    if ((mfp = fopen(im3_fname, "w")) == (FILE *) NULL)
        return(FILE_OPEN_ERROR);

#endif

    max_delta_freq = -999; min_delta_freq = 999;
    max_power_intr = -999; min_power_intr = 999;

    intermod_cnt = 0; line_cnt = 0; page_cnt = 1;
    for (i = 0; i < fq_cnt; i++)
    {
        if (fq_list[i]->stn_node == prop->stn_node)
            continue;

/*
printf("TX3 2nd loop: %lf %d\n", fq_list[i]->tx_freq, fq_list[i]->tx_channel);
*/
        channel_diff = abs(p_tx_channel - fq_list[i]->tx_channel);

        /***********************************************************/
        /* if channel difference between tx station and rx station */
        /* is greater than culling frequency, skip this station    */
        /***********************************************************/
        if (channel_diff > cull_channel)
            continue;

        eptr1 = exist[fq_list[i]->stn_node];
        h = fq_list[i]->tx_channel - p_tx_channel;

        for (j = i + 1; j < fq_cnt; j++)
        {
            if (fq_list[j]->stn_node == prop->stn_node)
                continue;

/*
printf("TX3 3rd loop: %lf %d\n", fq_list[j]->tx_freq, fq_list[j]->tx_channel);
*/
            channel_diff = abs(p_tx_channel - fq_list[j]->tx_channel);

            if (channel_diff > cull_channel)
                continue;

            eptr2 = exist[fq_list[j]->stn_node];
            harmonic = h + fq_list[j]->tx_channel;

            for (k = 0; k < fq_cnt; k++)
            {
                if (fq_list[k]->stn_node == prop->stn_node)
                    continue;

                /**************************************/
                /*  skip co-channel stations, if any  */
                /**************************************/
                if ((fq_list[k]->rx_channel == p_tx_channel) 
                ||  (fq_list[k]->rx_channel == fq_list[i]->tx_channel) 
                ||  (fq_list[k]->rx_channel == fq_list[j]->tx_channel))
                    continue;

                if ((k == j) || (k == i))
                    continue;

/*
printf("TX3 4th loop: %lf %d\n", fq_list[k]->tx_freq, fq_list[k]->tx_channel);
*/
                if ((abs(p_tx_channel - fq_list[k]->rx_channel)
                                            >= cull_channel)
                ||  (abs(fq_list[i]->tx_channel - fq_list[k]->rx_channel) 
                                            >= cull_channel)
                ||  (abs(fq_list[j]->tx_channel - fq_list[k]->rx_channel)
                                            >= cull_channel))
                    continue;

                if ((harmonic >= (fq_list[k]->rx_channel - bw3db_channel))
                &&  (harmonic <= (fq_list[k]->rx_channel + bw3db_channel)))
                {
/*
printf("within harmonic\n");
*/
                    eptr3 = exist[fq_list[k]->stn_node];

#ifdef BATCHED_EMC
                    intrfr_flag1 = intrfr_flag2 = intrfr_flag3 = FALSE;
                    e_idx = fq_list[k]->stn_node;
                    exist_rx_freq = fq_list[k]->rx_freq;
                    set_victim_exist();
                    base_to_base(&intrfr_flag1, INTERMOD);
                    power_intr3 = power_intr;
                    set_intr_exist(i);
                    base_to_base(&intrfr_flag2, INTERMOD);
                    power_intr1 = power_intr;
                    set_intr_exist(j);
                    base_to_base(&intrfr_flag3, INTERMOD);
                    power_intr2 = power_intr;
                    if (intrfr_flag1 && intrfr_flag2 && intrfr_flag3)
                    {
/*
printf("tx1 tx2 prop_tx_freq rx: %lf %lf %lf %lf\n", 
fq_list[i]->tx_freq, fq_list[j]->tx_freq, prop_tx_freq, fq_list[k]->rx_freq);
*/
                        print_intermod3_line(mfp,
                                             &line_cnt,
                                             &page_cnt,
                                             fq_list[i]->tx_freq,
                                             fq_list[j]->tx_freq,
                                             prop_tx_freq,
                                             fq_list[k]->rx_freq,
                                             harmonic,
                                             TRANSMITTER_3);
                        intmod3_tx3_tot++;
                    }
#endif

#ifdef COSITE
                    print_intermod3_line(mfp,
                                         &line_cnt,
                                         &page_cnt,
                                         fq_list[i]->tx_freq,
                                         fq_list[j]->tx_freq,
                                         prop_tx_freq,
                                         fq_list[k]->rx_freq,
                                         harmonic,
                                         TRANSMITTER_3);
                    intmod3_tx3_tot++;
#endif

                }
            }
        }
    }


#ifdef BATCHED_EMC
    if (intermod_cnt == 0)
    {
        fprintf(afp, "No intermod found\n");
        fclose(mfp);
        unlink(im3_fname);
    }
    else
    {
        print_intermod_total(line_cnt, EMC_INTERMOD3_LINES, mfp);
        fclose(mfp);
/*
        sprintf(cmdline, "lp -dprinter1 %s > /dev/null", im3_fname);
*/
        sprintf(cmdline, "cat %s >> %s", im3_fname, print_file);
        system(cmdline);
    }
#endif

#ifdef COSITE
    if (intermod_cnt == 0)
    {
        fclose(mfp);
        unlink(im3_fname);
    }
    else
    {
        print_intermod_total(line_cnt, COSITE_INTERMOD3_LINES, mfp);
        fclose(mfp);
        sprintf(cmdline, "lp -dprinter1 %s > /dev/null", im3_fname);
        system(cmdline);
    }
#endif


intermod3_end:
    ;

}


/******************************************************************/
/* print 3-signal intermod report line                            */
/******************************************************************/

print_intermod3_line(mfp, line_cnt, page_cnt, tx_freq1, tx_freq2, tx_freq3,
                     rx_freq, harmonic, mode)
FILE    *mfp;
int     *line_cnt;
int     *page_cnt;
double  tx_freq1;
double  tx_freq2;
double  tx_freq3;
double  rx_freq;
int     harmonic;
int     mode;
{
    float   tx_grid[2], rx_grid[2];
    char    e1_system_id[15], e2_system_id[15], e3_system_id[15];


/*
#ifdef DEBUG
    printf("print_intermod3_line : %lf %lf %lf %lf\n",
    tx_freq1, tx_freq2, tx_freq3, rx_freq);
#endif
*/

    delta_freq1 = abs(tx_freq1 - rx_freq);
    delta_freq2 = abs(tx_freq2 - rx_freq);
    delta_freq3 = abs(tx_freq3 - rx_freq);

    sprintf(e1_system_id, "%c%s%s-%s", eptr1->sys_category, eptr1->sys_type,
            eptr1->sys_no, eptr1->sys_suffix);
    sprintf(e2_system_id, "%c%s%s-%s", eptr2->sys_category, eptr2->sys_type,
            eptr2->sys_no, eptr2->sys_suffix);
    sprintf(e3_system_id, "%c%s%s-%s", eptr3->sys_category, eptr3->sys_type,
            eptr3->sys_no, eptr3->sys_suffix);


#ifdef BATCHED_EMC

    switch (mode)
    {
        case RECEIVER:
        {
	    tx_grid[0]  = (float)eptr1->east_grid / 10;
	    tx_grid[1]  = (float)eptr1->north_grid / 10;
            rx_grid[0]  = (float)prop->east_grid / 10;
	    rx_grid[1]  = (float)prop->north_grid / 10;
            delta_dist1 = cal_dist(rx_grid, tx_grid);

	    tx_grid[0]  = (float)eptr2->east_grid / 10;
	    tx_grid[1]  = (float)eptr2->north_grid / 10;
            rx_grid[0]  = (float)prop->east_grid / 10;
            rx_grid[1]  = (float)prop->north_grid / 10;
            delta_dist2 = cal_dist(rx_grid, tx_grid);

	    tx_grid[0]  = (float)eptr3->east_grid / 10;
	    tx_grid[1]  = (float)eptr3->north_grid / 10;
            rx_grid[0]  = (float)prop->east_grid / 10;
            rx_grid[1]  = (float)prop->north_grid / 10;
            delta_dist3 = cal_dist(rx_grid, tx_grid);

            break;
        }
        case TRANSMITTER_1:
        {
            tx_grid[0]  = (float)prop->east_grid / 10;
            tx_grid[1]  = (float)prop->north_grid / 10;
	    rx_grid[0]  = (float)eptr3->east_grid / 10;
	    rx_grid[1]  = (float)eptr3->north_grid / 10;
            delta_dist1 = cal_dist(rx_grid, tx_grid);

	    tx_grid[0]  = (float)eptr1->east_grid / 10;
	    tx_grid[1]  = (float)eptr1->north_grid / 10;
	    rx_grid[0]  = (float)eptr3->east_grid / 10;
	    rx_grid[1]  = (float)eptr3->north_grid / 10;
            delta_dist2 = cal_dist(rx_grid, tx_grid);

	    tx_grid[0]  = (float)eptr2->east_grid / 10;
	    tx_grid[1]  = (float)eptr2->north_grid / 10;
	    rx_grid[0]  = (float)eptr3->east_grid / 10;
	    rx_grid[1]  = (float)eptr3->north_grid / 10;
            delta_dist3 = cal_dist(rx_grid, tx_grid);

            break;
        }
        case TRANSMITTER_3:
        {
	    tx_grid[0]  = (float)eptr1->east_grid / 10;
	    tx_grid[1]  = (float)eptr1->north_grid / 10;
	    rx_grid[0]  = (float)eptr3->east_grid / 10;
	    rx_grid[1]  = (float)eptr3->north_grid / 10;
            delta_dist1 = cal_dist(rx_grid, tx_grid);

	    tx_grid[0]  = (float)eptr2->east_grid / 10;
	    tx_grid[1]  = (float)eptr2->north_grid / 10;
	    rx_grid[0]  = (float)eptr3->east_grid / 10;
	    rx_grid[1]  = (float)eptr3->north_grid / 10;
            delta_dist2 = cal_dist(rx_grid, tx_grid);

            tx_grid[0]  = (float)prop->east_grid / 10;
            tx_grid[1]  = (float)prop->north_grid / 10;
	    rx_grid[0]  = (float)eptr3->east_grid / 10;
	    rx_grid[1]  = (float)eptr3->north_grid / 10;
            delta_dist3 = cal_dist(rx_grid, tx_grid);

            break;
        }
    }

    if (*line_cnt == 0)
    {
        print_intermod3_head(mfp, page_cnt, mode);
        (*page_cnt)++;
    }

    /**********************************************************/
    /* for prop. station as TRANSMITTER_1, tx_freq1 belongs   */
    /* to prop station, tx_freq2 belongs to station1 (eptr1), */
    /* tx_freq3 belongs to station2 (eptr2)                   */
    /**********************************************************/
    if (mode == TRANSMITTER_1)
    {
        fprintf(mfp, "%-14s %4d %11.5lf%2s",
                e1_system_id, eptr1->base_no, tx_freq2, "");
        fprintf(mfp, "%-14s %4d %11.5lf%2s",
                e2_system_id, eptr2->base_no, tx_freq3, "");
    }
    else
    {
        fprintf(mfp, "%-14s %4d %11.5lf%2s",
                e1_system_id, eptr1->base_no, tx_freq1, "");
        fprintf(mfp, "%-14s %4d %11.5lf%2s",
                e2_system_id, eptr2->base_no, tx_freq2, "");
    }

    fprintf(mfp, "%-14s %4d ", e3_system_id, eptr3->base_no);

    if (mode == RECEIVER)
        fprintf(mfp, "%11.5lf", tx_freq3);
    else
        fprintf(mfp, "%11.5lf", rx_freq);

    fprintf(mfp, "%2s%11.5lf %6.1f %6.1f %6.1f", "", 
            (harmonic*MIN_CHANNEL_SEP), power_intr1, power_intr2, power_intr3);

    /**************************************/
    /* highlight adjacent channel, if any */
    /**************************************/
    if ((delta_freq1 <= MIN_CURVE_LEVEL) || (delta_freq2 <= MIN_CURVE_LEVEL)
    ||  (delta_freq3 <= MIN_CURVE_LEVEL))
        fprintf(mfp, "#\n");
    else
        fprintf(mfp, "\n");

    if (mode == TRANSMITTER_1)
    {
        fprintf(mfp, "(DELTA_2 = %7.5f,DIST2 = %4.2f) ",
                delta_freq2, delta_dist2);
        fprintf(mfp, "(DELTA_3 = %7.5f,DIST3 = %4.2f) ",
                delta_freq3, delta_dist3);
        fprintf(mfp, "(DELTA_1 = %7.5f,DIST1 = %4.2f)\n\n",
                delta_freq1, delta_dist1);
    }
    else
    {
        fprintf(mfp, "(DELTA_1 = %7.5f,DIST1 = %4.2f) ",
                delta_freq1, delta_dist1);
        fprintf(mfp, "(DELTA_2 = %7.5f,DIST2 = %4.2f) ",
                delta_freq2, delta_dist2);
        fprintf(mfp, "(DELTA_3 = %7.5f,DIST3 = %4.2f)\n\n",
                delta_freq3, delta_dist3);
    }

    *line_cnt = (*line_cnt + 1) % EMC_INTERMOD3_LINES;

    max_delta_freq = max(delta_freq1, max_delta_freq);
    max_delta_freq = max(delta_freq2, max_delta_freq);
    max_delta_freq = max(delta_freq3, max_delta_freq);
    min_delta_freq = min(delta_freq1, min_delta_freq);
    min_delta_freq = min(delta_freq2, min_delta_freq);
    min_delta_freq = min(delta_freq3, min_delta_freq);

    max_power_intr = max(power_intr1, max_power_intr);
    max_power_intr = max(power_intr2, max_power_intr);
    max_power_intr = max(power_intr3, max_power_intr);
    min_power_intr = min(power_intr1, min_power_intr);
    min_power_intr = min(power_intr2, min_power_intr);
    min_power_intr = min(power_intr3, min_power_intr);

#endif


#ifdef COSITE

    if (*line_cnt == 0)
    {
        print_intermod3_head(mfp, page_cnt, mode);
        (*page_cnt)++;
    }

    /**********************************************************/
    /* for prop. station as TRANSMITTER_1, tx_freq1 belongs   */
    /* to prop station, tx_freq2 belongs to station1 (eptr1), */
    /* tx_freq3 belongs to station2 (eptr2)                   */
    /**********************************************************/
    if (mode == TRANSMITTER_1)
    {
        fprintf(mfp, "%-14s %4d %11.5lf ",
                e1_system_id, eptr1->base_no, tx_freq2);
        fprintf(mfp, "%-14s %4d %11.5lf ",
                e2_system_id, eptr2->base_no, tx_freq3);
    }
    else
    {
        fprintf(mfp, "%-14s %4d %11.5lf ",
                e1_system_id, eptr1->base_no, tx_freq1);
        fprintf(mfp, "%-14s %4d %11.5lf ",
                e2_system_id, eptr2->base_no, tx_freq2);
    }

    fprintf(mfp, "%-14s %4d ", e3_system_id, eptr3->base_no);

    if (mode == RECEIVER)
        fprintf(mfp, "%11.5lf ", tx_freq3);

    if ((mode == TRANSMITTER_1) || (mode == TRANSMITTER_3))
        fprintf(mfp, "%11.5lf ", rx_freq);

    fprintf(mfp, "%7.5f %7.5f %7.5f %11.5lf", 
            delta_freq1, delta_freq2, delta_freq3, (harmonic*MIN_CHANNEL_SEP));

    /**************************************/
    /* highlight adjacent channel, if any */
    /**************************************/
    if ((delta_freq1 <= MIN_CURVE_LEVEL + FREQ_EPSILON) 
    ||  (delta_freq2 <= MIN_CURVE_LEVEL + FREQ_EPSILON) 
    ||  (delta_freq3 <= MIN_CURVE_LEVEL + FREQ_EPSILON))
        fprintf(mfp, "#\n");
    else
        fprintf(mfp, "\n");

    *line_cnt = (*line_cnt + 1) % COSITE_INTERMOD3_LINES;

    max_delta_freq = max(delta_freq1, max_delta_freq);
    max_delta_freq = max(delta_freq2, max_delta_freq);
    max_delta_freq = max(delta_freq3, max_delta_freq);
    min_delta_freq = min(delta_freq1, min_delta_freq);
    min_delta_freq = min(delta_freq2, min_delta_freq);
    min_delta_freq = min(delta_freq3, min_delta_freq);

#endif


    intermod_cnt++;
}


/*****************************************************************/
/* print 1st part of 3-signal intermod report heading            */
/*****************************************************************/

print_intermod3_head(mfp, page_cnt, mode)
FILE    *mfp;
int     *page_cnt;
int     mode;
{

/*
#ifdef DEBUG
    printf("print_intermod3_head\n");
#endif
*/

   if (*page_cnt > 1)
       fprintf(mfp, "\f");

   fprintf(mfp, "RUN DATE: %s%14s", sys_date, "");
   fprintf(mfp,
          "*************************************************************");
   fprintf(mfp, "%22sPAGE   : %-d\n", "", *page_cnt);
   fprintf(mfp, "RUN TIME: %s%14s", sys_time, "");
   fprintf(mfp,
          "*                                                           *");
   fprintf(mfp, "%22sPROGRAM: esemim0r\n", "");
   fprintf(mfp, "USER ID : %-19s%3s", emc_uid, "");


#ifdef BATCHED_EMC

   fprintf(mfp,
     "*               EMC ANALYSIS - INTERMODULATION              *\n");
   fprintf(mfp, "%32s", "");
   fprintf(mfp,
     "*            3-SIGNAL FREQUENCY COMBINATION REPORT          *\n");
   fprintf(mfp, "%32s", "");
   switch (mode)
   {
       case RECEIVER:
           emcmod3_vict_head(mfp);
           break;
       case TRANSMITTER_1:
           emcmod3_tx1_head(mfp);
           break;
       case TRANSMITTER_3:
           emcmod3_tx3_head(mfp);
           break;
   }

#endif


#ifdef COSITE

   fprintf(mfp,
     "*             CO-SITE ANALYSIS - INTERMODULATION            *\n");
   fprintf(mfp, "%32s", "");
   fprintf(mfp,
     "*           3-SIGNAL FREQUENCY COMBINATION REPORT           *\n");
   switch (mode)
   {
       case RECEIVER:
           cosmod3_vict_head(mfp);
           break;
       case TRANSMITTER_1:
           cosmod3_tx1_head(mfp);
           break;
       case TRANSMITTER_3:
           cosmod3_tx3_head(mfp);
           break;
   }

#endif


}


#ifdef BATCHED_EMC


/**********************************************************************/
/* print 3-signal intermod report heading (prop. station as RECEIVER) */
/**********************************************************************/

emcmod3_vict_head(mfp)
FILE    *mfp;
{

/*
#ifdef DEBUG
    printf("emmod3_vict_head\n");
#endif
*/

    fprintf(mfp, "*%15s(PROPOSED STATION IS RECEIVER)%14s*\n", "", "");
    fprintf(mfp, "%32s*%59s*", "", "");
    fprintf(mfp, "  DELTA_1 = ABS(TX_1 - PROP RX FREQ)\n");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, "*%16sPROPOSED STATION INFORMATION%15s*", "", "");
    fprintf(mfp, "  DELTA_2 = ABS(TX_2 - PROP RX FREQ)\n");
    fprintf(mfp, "'#' : DELTA_1/DELTA_2/DELTA_3   *%59s*", "");
    fprintf(mfp, "  DELTA_3 = ABS(TX_3 - PROP RX FREQ)\n");
    fprintf(mfp, "      < 30KHz                   ");
    fprintf(mfp, "* PROP. SYSTEM    : %c%s%s-%s%29s*\n", prop->sys_category,
           prop->sys_type, prop->sys_no, prop->sys_suffix, "");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, "* RX FREQ (MHz)   :%11.5lf  TX FREQ (MHz)  :%11.5lf *\n",
            prop_rx_freq, prop_tx_freq);
    fprintf(mfp, "DIST1(KM) = DIST. %% TX1 AND     ");
    fprintf(mfp, "* GRID EAST       :      %5d  GRID NORTH     :      %5d *",
            prop->east_grid, prop->north_grid);
    fprintf(mfp, "  PIN_1 = INTERFERENCE POWER OF\n");
    fprintf(mfp, "            AND PROP. STATION   ");
    fprintf(mfp, "* SUB-DISTRICT    :%8s%3s  ERP (dBW)      :%5s%6.2f *",
            "", prop->sub_district, "", prop->pw_dbw);
    fprintf(mfp, "          TX_1 TO PROP RX FREQ\n");
    fprintf(mfp, "DIST2(KM) = DIST. %% TX2 AND     ");
    fprintf(mfp, "* ANTENNA HT (M)  :%8s%3d  TERRAIN HT (M) :%6s%5.1f *",
            "", prop->ant_height, "", prop->height_asl);
    fprintf(mfp, "  PIN_2 = INTERFERENCE POWER OF\n");
    fprintf(mfp, "            AND PROP. STATION   ");
    fprintf(mfp, "* ANTENNA TYPE    :%9s%2s  AZ OF MAX RAD  :        %3d *",
            "", prop->antenna, prop->az_max_rad);
    fprintf(mfp, "          TX_2 TO PROP RX FREQ\n");
    fprintf(mfp, "DIST3(KM) = DIST. %% TX3 AND     ");
    (prop->sfx_filter[0] != '\0') ?
       fprintf(mfp, "* FEED LOSS (DB)  :        %3d  SFX FILTER     : %10s *",
               prop->feed_loss, prop->sfx_filter):
       fprintf(mfp, "* FEED LOSS (DB)  :        %3d%30s*", prop->feed_loss, "");
    fprintf(mfp, "  PIN_3 = INTERFERENCE POWER OF\n");
    fprintf(mfp, "            AND PROP. STATION   ");
    fprintf(mfp, "* INTMOD ADJ (dB) :%5s%6.1f  STATION TYPE   : %9s%c *", 
            "", prop->intmod_att_db, "", prop->station_type);
    fprintf(mfp, "          TX_3 TO PROP RX FREQ\n");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, 
            "*************************************************************\n");
    fprintf(mfp, "\n\n");
    fprintf(mfp, "%5sTRANSMITTER #1 (TX1)%13sTRANSMITTER #2 (TX2)", "", "");
    fprintf(mfp, "%13sTRANSMITTER #3(TX3)\n", "");
    fprintf(mfp, "===============================");
    fprintf(mfp, "  ===============================");
    fprintf(mfp, "  ===============================\n");
    fprintf(mfp, "SYSTEM-ID%6sBASE%3sTX FREQ 1", "", "");
    fprintf(mfp, "  SYSTEM-ID%6sBASE%3sTX FREQ 2", "", "");
    fprintf(mfp, "  SYSTEM-ID%6sBASE%3sTX FREQ 3", "", "");
    fprintf(mfp, "%5sINTERMOD%2sPIN_1%2sPIN_2%2sPIN_3\n", "", "", "", "");
    fprintf(mfp, "%16sNO.%5s(TX_1)", "", "");
    fprintf(mfp, "%19sNO.%5s(TX_2)", "", "");
    fprintf(mfp, "%19sNO.%5s(TX_3)", "", "");
    fprintf(mfp, "%7sPRODUCT%2s(dBW)%2s(dBW)%2s(dBW)\n\n", "", "", "", "");
}


/***************************************************************************/
/* print 3-signal intermod report heading (prop. station as TRANSMITTER_1) */
/***************************************************************************/

emcmod3_tx1_head(mfp)
FILE    *mfp;
{

/*
#ifdef DEBUG
    printf("emcmod3_tx1_head\n");
#endif
*/

    fprintf(mfp, "*%12s(PROPOSED STATION IS TRANSMITTER  #1)%10s*\n", "", "");
    fprintf(mfp, "%32s*%59s*", "", "");
    fprintf(mfp, "  DELTA_1 = ABS(PROP TX FREQ - RX FREQ)\n");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, "*%16sPROPOSED STATION INFORMATION%15s*", "", "");
    fprintf(mfp, "  DELTA_2 = ABS(TX_2 - RX FREQ)\n");
    fprintf(mfp, "'#' : DELTA_1/DELTA_2/DELTA_3   *%59s*", "");
    fprintf(mfp, "  DELTA_3 = ABS(TX_3 - RX FREQ)\n");
    fprintf(mfp, "      < 30KHz                   ");
    fprintf(mfp, "* PROP. SYSTEM    : %c%s%s-%s%29s*\n", prop->sys_category,
           prop->sys_type, prop->sys_no, prop->sys_suffix, "");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, "* RX FREQ (MHz)   :%11.5lf  TX FREQ (MHz)  :%11.5lf *\n",
            prop_rx_freq, prop_tx_freq);
    fprintf(mfp, "DIST1(KM) = DIST. %% RECEIVER    ");
    fprintf(mfp, "* GRID EAST       :      %5d  GRID NORTH     :      %5d *",
            prop->east_grid, prop->north_grid);
    fprintf(mfp, "  PIN_1 = INTERFERENCE POWER OF\n");
    fprintf(mfp, "            AND PROP. STATION   ");
    fprintf(mfp, "* SUB-DISTRICT    :%8s%3s  ERP (dBW)      :%5s%6.2f *",
            "", prop->sub_district, "", prop->pw_dbw);
    fprintf(mfp, "          PROP TX FREQ TO RX FREQ\n");
    fprintf(mfp, "DIST2(KM) = DIST. %% RECEIVER    ");
    fprintf(mfp, "* ANTENNA HT (M)  :%8s%3d  TERRAIN HT (M) :%6s%5.1f *",
            "", prop->ant_height, "", prop->height_asl);
    fprintf(mfp, "  PIN_2 = INTERFERENCE POWER OF\n");
    fprintf(mfp, "            AND TX2             ");
    fprintf(mfp, "* ANTENNA TYPE    :%9s%2s  AZ OF MAX RAD  :        %3d *",
            "", prop->antenna, prop->az_max_rad);
    fprintf(mfp, "          TX_2 TO RX FREQ\n");
    fprintf(mfp, "DIST3(KM) = DIST. %% RECEIVER    ");
    (prop->sfx_filter[0] != '\0') ?
       fprintf(mfp, "* FEED LOSS (DB)  :        %3d  SFX FILTER     : %10s *",
               prop->feed_loss, prop->sfx_filter):
       fprintf(mfp, "* FEED LOSS (DB)  :        %3d%30s*", prop->feed_loss, "");
    fprintf(mfp, "  PIN_3 = INTERFERENCE POWER OF\n");
    fprintf(mfp, "            AND TX3             ");
    fprintf(mfp, "* INTMOD ADJ (dB) :%5s%6.1f  STATION TYPE   : %9s%c *", 
            "", prop->intmod_att_db, "", prop->station_type);
    fprintf(mfp, "          TX_3 TO RX FREQ\n");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, 
            "*************************************************************\n");
    fprintf(mfp, "\n\n");
    fprintf(mfp, "%5sTRANSMITTER #2 (TX2)%13sTRANSMITTER #3 (TX3)", "", "");
    fprintf(mfp, "%19sRECEIVER\n", "");
    fprintf(mfp, "===============================");
    fprintf(mfp, "  ===============================");
    fprintf(mfp, "  ===============================\n");
    fprintf(mfp, "SYSTEM-ID%6sBASE%3sTX FREQ 2", "", "");
    fprintf(mfp, "  SYSTEM-ID%6sBASE%3sTX FREQ 3", "", "");
    fprintf(mfp, "  SYSTEM-ID%6sBASE%5sRX FREQ", "", "");
    fprintf(mfp, "%5sINTERMOD%2sPIN_1%2sPIN_2%2sPIN_3\n", "", "", "", "");
    fprintf(mfp, "%16sNO.%5s(TX_2)", "", "");
    fprintf(mfp, "%19sNO.%5s(TX_3)", "", "");
    fprintf(mfp, "%19sNO.%18s", "", "");
    fprintf(mfp, "PRODUCT%2s(dBW)%2s(dBW)%2s(dBW)\n\n", "", "", "", "");
}


/***************************************************************************/
/* print 3-signal intermod report heading (prop. station as TRANSMITTER_3) */
/***************************************************************************/

emcmod3_tx3_head(mfp)
FILE    *mfp;
{

/*
#ifdef DEBUG
    printf("emcmod3_tx3_head\n");
#endif
*/

    fprintf(mfp, "*%12s(PROPOSED STATION IS TRANSMITTER  #3)%10s*\n", "", "");
    fprintf(mfp, "%32s*%59s*", "", "");
    fprintf(mfp, "  DELTA_1 = ABS(TX_1 - RX FREQ)\n");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, "*%16sPROPOSED STATION INFORMATION%15s*", "", "");
    fprintf(mfp, "  DELTA_2 = ABS(TX_2 - RX FREQ)\n");
    fprintf(mfp, "'#' : DELTA_1/DELTA_2/DELTA_3   *%59s*", "");
    fprintf(mfp, "  DELTA_3 = ABS(PROP TX FREQ - RX FREQ)\n");
    fprintf(mfp, "      < 30KHz                   ");
    fprintf(mfp, "* PROP. SYSTEM    : %c%s%s-%s%29s*\n", prop->sys_category,
           prop->sys_type, prop->sys_no, prop->sys_suffix, "");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, "* RX FREQ (MHz)   :%11.5lf  TX FREQ (MHz)  :%11.5lf *\n",
            prop_rx_freq, prop_tx_freq);
    fprintf(mfp, "DIST1(KM) = DIST. %% RECEIVER    ");
    fprintf(mfp, "* GRID EAST       :      %5d  GRID NORTH     :      %5d *",
            prop->east_grid, prop->north_grid);
    fprintf(mfp, "  PIN_1 = INTERFERENCE POWER OF\n");
    fprintf(mfp, "            AND TX1             ");
    fprintf(mfp, "* SUB-DISTRICT    :%8s%3s  ERP (dBW)      :%5s%6.2f *",
            "", prop->sub_district, "", prop->pw_dbw);
    fprintf(mfp, "          TX_1 TO RX FREQ\n");
    fprintf(mfp, "DIST2(KM) = DIST. %% RECEIVER    ");
    fprintf(mfp, "* ANTENNA HT (M)  :%8s%3d  TERRAIN HT (M) :%6s%5.1f *",
            "", prop->ant_height, "", prop->height_asl);
    fprintf(mfp, "  PIN_2 = INTERFERENCE POWER OF\n");
    fprintf(mfp, "            AND TX2             ");
    fprintf(mfp, "* ANTENNA TYPE    :%9s%2s  AZ OF MAX RAD  :        %3d *",
            "", prop->antenna, prop->az_max_rad);
    fprintf(mfp, "          TX_2 TO RX FREQ\n");
    fprintf(mfp, "DIST3(KM) = DIST. %% RECEIVER    ");
    (prop->sfx_filter[0] != '\0') ?
       fprintf(mfp, "* FEED LOSS (DB)  :        %3d  SFX FILTER     : %10s *",
               prop->feed_loss, prop->sfx_filter):
       fprintf(mfp, "* FEED LOSS (DB)  :        %3d%30s*", prop->feed_loss, "");
    fprintf(mfp, "  PIN_3 = INTERFERENCE POWER OF\n");
    fprintf(mfp, "            AND PROP. STATION   ");
    fprintf(mfp, "* INTMOD ADJ (dB) :%5s%6.1f  STATION TYPE   : %9s%c *", 
            "", prop->intmod_att_db, "", prop->station_type);
    fprintf(mfp, "          PROP TX FREQ TO RX FREQ\n");
    fprintf(mfp, "%32s", "");
    fprintf(mfp, 
            "*************************************************************\n");
    fprintf(mfp, "\n\n");

    fprintf(mfp, "%5sTRANSMITTER #1 (TX1)%13sTRANSMITTER #2 (TX2)", "", "");
    fprintf(mfp, "%19sRECEIVER\n", "");
    fprintf(mfp, "===============================");
    fprintf(mfp, "  ===============================");
    fprintf(mfp, "  ===============================\n");
    fprintf(mfp, "SYSTEM-ID%6sBASE%3sTX FREQ 1", "", "");
    fprintf(mfp, "  SYSTEM-ID%6sBASE%3sTX FREQ 2", "", "");
    fprintf(mfp, "  SYSTEM-ID%6sBASE%5sRX FREQ", "", "");
    fprintf(mfp, "%5sINTERMOD%2sPIN_1%2sPIN_2%2sPIN_3\n", "", "", "", "");
    fprintf(mfp, "%16sNO.%5s(TX_1)", "", "");
    fprintf(mfp, "%19sNO.%5s(TX_2)", "", "");
    fprintf(mfp, "%19sNO.%18s", "", "");
    fprintf(mfp, "PRODUCT%2s(dBW)%2s(dBW)%2s(dBW)\n\n", "", "", "", "");
}

#endif


#ifdef COSITE


/**********************************************************************/
/* print 3-signal intermod report heading (prop. station as RECEIVER) */
/**********************************************************************/

cosmod3_vict_head(mfp)
FILE    *mfp;
{

/*
#ifdef DEBUG
    printf("cosmod3_vict_head\n");
#endif
*/

    fprintf(mfp, "%32s*%15s(PROPOSED STATION IS RECEIVER)%14s*\n", "", "", "");
    fprintf(mfp, "%32s*%59s*\n", "", "");
    fprintf(mfp, "%32s*%21sSUB-DISTRICT : %-3s%20s*\n", 
            "", "", sub_district, "");
    fprintf(mfp, "%32s*%59s*\n", "", "");
    fprintf(mfp, "%32s*  RX FREQ (MHz) :%11.5lf   TX FREQ (MHz) :%11.5lf  *",
            "", prop_rx_freq, prop_tx_freq);
    fprintf(mfp, "  DELTA_1 = ABS(TX_1 - PROP RX FREQ)\n");
    fprintf(mfp, "'#' : DELTA_1/DELTA_2/DELTA_3   *%59s*", "");
    fprintf(mfp, "  DELTA_2 = ABS(TX_2 - PROP RX FREQ)\n");
    fprintf(mfp, "      < 30KHz                   ");
    fprintf(mfp, 
            "*************************************************************");
    fprintf(mfp, "  DELTA_3 = ABS(TX_3 - PROP RX FREQ)\n");
    fprintf(mfp, "\n\n\n");
    fprintf(mfp, "%5sTRANSMITTER #1 (TX1)%13sTRANSMITTER #2 (TX2)", "", "");
    fprintf(mfp, "%12sTRANSMITTER #3 (TX3)\n", "");
    fprintf(mfp, "===============================");
    fprintf(mfp, " ===============================");
    fprintf(mfp, " ===============================\n");
    fprintf(mfp, "SYSTEM-ID%6sBASE%3sTX FREQ 1", "", "");
    fprintf(mfp, " SYSTEM-ID%6sBASE%3sTX FREQ 2", "", "");
    fprintf(mfp, " SYSTEM-ID%6sBASE%3sTX FREQ 3", "", "");
    fprintf(mfp, " DELTA_1 DELTA_2 DELTA_3    INTERMOD\n");
    fprintf(mfp, "%16sNO.%5s(TX_1)", "", "");
    fprintf(mfp, "%18sNO.%5s(TX_2)", "", "");
    fprintf(mfp, "%18sNO.%5s(TX_3)", "", "");
    fprintf(mfp, "%3s(MHz)%3s(MHz)%3s(MHz)%6sPRODUCT\n\n", "", "", "", "");
}


/***************************************************************************/
/* print 3-signal intermod report heading (prop. station as TRANSMITTER_1) */
/***************************************************************************/

cosmod3_tx1_head(mfp)
FILE    *mfp;
{

/*
#ifdef DEBUG
    printf("cosmod3_tx1_head\n");
#endif
*/

    fprintf(mfp, "%32s*%12s(PROPOSED STATION IS TRANSMITTER  #1)%10s*\n", 
            "", "", "");
    fprintf(mfp, "%32s*%59s*\n", "", "");
    fprintf(mfp, "%32s*%21sSUB-DISTRICT : %-3s%20s*\n", 
            "", "", sub_district, "");
    fprintf(mfp, "%32s*%59s*\n", "", "");
    fprintf(mfp, "%32s*  RX FREQ (MHz) :%11.5lf   TX FREQ (MHz) :%11.5lf  *",
            "", prop_rx_freq, prop_tx_freq);
    fprintf(mfp, "  DELTA_1 = ABS(PROP TX FREQ - RX FREQ)\n");
    fprintf(mfp, "'#' : DELTA_1/DELTA_2/DELTA_3   *%59s*", "");
    fprintf(mfp, "  DELTA_2 = ABS(TX_2 - RX FREQ)\n");
    fprintf(mfp, "      < 30KHz                   ");
    fprintf(mfp, 
            "*************************************************************");
    fprintf(mfp, "  DELTA_3 = ABS(TX_3 - RX FREQ)\n");
    fprintf(mfp, "\n\n\n");
    fprintf(mfp, "%5sTRANSMITTER #2 (TX2)%13sTRANSMITTER #3 (TX3)", "", "");
    fprintf(mfp, "%16sRECEIVER\n", "");
    fprintf(mfp, "===============================");
    fprintf(mfp, " ===============================");
    fprintf(mfp, " ===============================\n");
    fprintf(mfp, "SYSTEM-ID%6sBASE%3sTX FREQ 2", "", "");
    fprintf(mfp, " SYSTEM-ID%6sBASE%3sTX FREQ 3", "", "");
    fprintf(mfp, " SYSTEM-ID%6sBASE%5sRX FREQ", "", "");
    fprintf(mfp, " DELTA_1 DELTA_2 DELTA_3    INTERMOD\n");
    fprintf(mfp, "%16sNO.%5s(TX_2)", "", "");
    fprintf(mfp, "%18sNO.%5s(TX_3)", "", "");
    fprintf(mfp, "%18sNO.%14s", "", "");
    fprintf(mfp, "(MHz)%3s(MHz)%3s(MHz)%6sPRODUCT\n\n", "", "", "");
}


/***************************************************************************/
/* print 3-signal intermod report heading (prop. station as TRANSMITTER_3) */
/***************************************************************************/

cosmod3_tx3_head(mfp)
FILE    *mfp;
{

/*
#ifdef DEBUG
    printf("comod3_tx3_head\n");
#endif
*/

    fprintf(mfp, "%32s*%12s(PROPOSED STATION IS TRANSMITTER  #3)%10s*\n", 
            "", "", "");
    fprintf(mfp, "%32s*%59s*\n", "", "");
    fprintf(mfp, "%32s*%21sSUB-DISTRICT : %-3s%20s*\n", 
            "", "", sub_district, "");
    fprintf(mfp, "%32s*%59s*\n", "", "");
    fprintf(mfp, "%32s*  RX FREQ (MHz) :%11.5lf   TX FREQ (MHz) :%11.5lf  *",
            "", prop_rx_freq, prop_tx_freq);
    fprintf(mfp, "  DELTA_1 = ABS(TX_1 - RX FREQ)\n");
    fprintf(mfp, "'#' : DELTA_1/DELTA_2/DELTA_3   *%59s*", "");
    fprintf(mfp, "  DELTA_2 = ABS(TX_2 - RX FREQ)\n");
    fprintf(mfp, "      < 30KHz                   ");
    fprintf(mfp, 
            "*************************************************************");
    fprintf(mfp, "  DELTA_3 = ABS(PROP TX FREQ - RX FREQ)\n");
    fprintf(mfp, "\n\n\n");
    fprintf(mfp, "%4sTRANSMITTER #1 (TX1)%10sTRANSMITTER #2 (TX2)", "", "");
    fprintf(mfp, "%16sRECEIVER\n", "");
    fprintf(mfp, "===============================");
    fprintf(mfp, " ===============================");
    fprintf(mfp, " ===============================\n");
    fprintf(mfp, "SYSTEM-ID%6sBASE%3sTX FREQ 1", "", "");
    fprintf(mfp, " SYSTEM-ID%6sBASE%3sTX FREQ 2", "", "");
    fprintf(mfp, " SYSTEM-ID%6sBASE%5sRX FREQ", "", "");
    fprintf(mfp, " DELTA_1 DELTA_2 DELTA_3    INTERMOD\n");
    fprintf(mfp, "%16sNO.%5s(TX_1)", "", "");
    fprintf(mfp, "%18sNO.%5s(TX_2)", "", "");
    fprintf(mfp, "%18sNO.%14s", "", "");
    fprintf(mfp, "(MHz)%3s(MHz)%3s(MHz)%6sPRODUCT\n\n", "", "", "");
}

#endif


/********************************************/
/* print intermod totals                    */
/********************************************/

print_intermod_total(line_cnt, max_line, mfp)
int    line_cnt;
int    max_line;
FILE   *mfp;
{
    int    skip_lines;
    int    i;


/*
#ifdef DEBUG
    printf("print_intermod_total\n");
#endif
*/

    if (line_cnt > max_line - 2)
        fprintf(mfp, "\f\n\n\n\n");
    else
    {
        skip_lines = 2 + ((max_line - 2) - line_cnt);
        for (i = 0; i < skip_lines; i++)
            fprintf(mfp, "\n");
    }

    fprintf(mfp, "%47sNUMBER OF COMBINATIONS        :   %4d\n", "",
            intermod_cnt);
    fprintf(mfp, "%47sMAX. DELTA FREQUENCY (MHz)    : %6.4f\n", "",
            max_delta_freq);
    fprintf(mfp, "%47sMIN. DELTA FREQUENCY (MHz)    : %6.4f", "",
            min_delta_freq);
    

/*
#ifdef COSITE
    fprintf(mfp, "\f");
#endif
*/

#ifdef BATCHED_EMC

    fprintf(mfp, "\n%47sMAX. INTERFERENCE POWER (dBW) : %6.1f\n", "",
            max_power_intr);
    fprintf(mfp, "%47sMIN. INTERFERENCE POWER (dBW) : %6.1f\f", "",
            min_power_intr);

#endif


}
