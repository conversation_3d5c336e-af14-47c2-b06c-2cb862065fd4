/**********************************************************************/
/*                                                                    */
/*    Module Name   :  base_to_base (esemin0x.pc)                     */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  <PERSON><PERSON> <PERSON><PERSON>                                      */
/*                                                                    */
/*    Callers       :  desensit_interference (esemdc0r.c)             */  
/*                     intermod_2 (esemim0r.c)                        */  
/*                     intermod_3 (esemim0r.c)                        */  
/*                                                                    */
/*    Parameters    :  intrfr_flag (interference flag)                */
/*                     caller (caller id.)                            */
/*                                                                    */
/*    Called Modules:  cal_dist (utility.c)                           */
/*                     get_diffract_loss (esemdl0x.c)                 */
/*                     get_vict_antenna_gain (esemin0x.pc)            */
/*                                                                    */
/*    Purpose       :  Calculate base-to-base interference and then   */
/*                     determine the interference is significant      */
/*                     (according to nature of caller). If yes, then  */
/*                     set intrfr_flag to TRUE.                       */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <math.h>
/* #include "../include/math.h" */
#include "../include/macros.h"
#include "../include/define.h"
#include "../include/emcext.h"
#include "../include/refext.h"
#include "../include/victext.h"
#include "../include/intrext.h"
#include "../include/presuext.h"
#include "../include/propext.h"

#ifndef M_PI
#define M_PI            3.14159265358979323846
#endif

#ifndef M_PI_2
#define M_PI_2          1.57079632679489661923
#endif


float    cal_dist();
float    get_diffract_loss();
int	 get_vict_antenna_gain();





extern char msg[];

EXEC SQL BEGIN DECLARE SECTION;

    VARCHAR o_sfx_filter[11];
    double  o_chan_sep;
    float   o_filter_att_db;
    VARCHAR o_antenna[3];
    int     o_degree;
    float   o_gain_in_db;
    int     o_cnt;
    float   epsilon;

EXEC SQL END DECLARE SECTION;

EXEC SQL INCLUDE SQLCA;

int base_to_base(intrfr_flag, caller)
int    *intrfr_flag;
int    caller;
{
    float   log_delta_dist;
    float   diffract_loss;
    float   off_chan_rej;
    float   prop_loss;
    float   min_usable_power;
    double  curve_adj;
    float   tmp_vict_grid[2], tmp_intr_grid[2];
    int     i;


	
printf( "esemin0x 107  in base_to_base \n\r" ); /*Cyrus Debug*/		
	
    EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn;
	
printf( "esemin0x 108  in base_to_base \n\r" ); /*Cyrus Debug*/		
	
    EXEC SQL WHENEVER NOT FOUND CONTINUE;

printf( "esemin0x 109  in base_to_base \n\r" ); /*Cyrus Debug*/		

/*
#ifdef DEBUG
    printf("base_to_base\n");
#endif
*/

    epsilon = FREQ_EPSILON;
    tmp_vict_grid[0] = vict.grid[0] / 10;
    tmp_vict_grid[1] = vict.grid[1] / 10;
    tmp_intr_grid[0] = intr.grid[0] / 10;
    tmp_intr_grid[1] = intr.grid[1] / 10;
    delta_dist       = cal_dist(tmp_vict_grid, tmp_intr_grid) * 1000;
	
printf( "esemin0x 110  in base_to_base \n\r" ); /*Cyrus Debug*/		
	
	
/*
printf("vict0 vict1 intr0 intr1 delta_dist: %f %f %f %f %f\n",
tmp_vict_grid[0], tmp_vict_grid[1], tmp_intr_grid[0], tmp_intr_grid[1],
delta_dist);
*/
/*
system("echo \"esemin0x data\" >> /tmp/debug");
sprintf(msg, "echo \"%f %f %f %f %f\" >> /tmp/debug",
tmp_vict_grid[0], tmp_vict_grid[1], tmp_intr_grid[0], tmp_intr_grid[1],
delta_dist);
system(msg);
*/

    log_delta_dist = (float)log10((double)delta_dist/1000);
	
printf( "esemin0x 111  in base_to_base \n\r" ); /*Cyrus Debug*/		
	

    diffract_loss = get_diffract_loss(intr.grid, intr.ant_height,
                                      vict.grid, vict.ant_height);

printf( "esemin0x 112  in base_to_base \n\r" ); /*Cyrus Debug*/		
									  
    prop_loss =   28.1 + 20.0*log10(intr.tx_freq) + 20.0*log_delta_dist
                + diffract_loss + CLUTTER_LOSS;


    /*********************************************************************/
    /* If antenna gain (horizontal) of victim station is to be neglected */
    /* or antenna is non-directional (currently with respect to vertical */
    /* plane), then vict.az_max_rad is set to 999 in EMC Batch Entry     */
    /*********************************************************************/
    vict.ant_gain = 0.0;
	
printf( "esemin0x 113  in base_to_base \n\r" ); /*Cyrus Debug*/		
	
	
    if (vict.az_max_rad != 999)
		
printf( "esemin0x 114  before get_vict_antenna_gain \n\r" ); /*Cyrus Debug*/		
	
        get_vict_antenna_gain();

printf( "esemin0x 115 end get_vict_antenna_gain \n\r" ); /*Cyrus Debug*/				
		
    if ((vict.sfx_filter[0] != '\0') && (vict.sfx_filter[0] != ' '))
    {
		
printf( "esemin0x 116 in  (vict.sfx_filter[0] != '\0') && (vict.sfx_filter[0] != ' ')     \n\r" ); /*Cyrus Debug*/				
				

/*        strcpy(o_sfx_filter.arr, vict.sfx_filter); */
	    strcpy((char *)o_sfx_filter.arr, vict.sfx_filter);
        o_sfx_filter.len = strlen((char *)o_sfx_filter.arr);
        o_chan_sep = delta_freq;
/*
system("echo \"esemin0x 1\" >> /tmp/debug");
sprintf(msg, "echo \"%s %f %f\" >> /tmp/debug",
o_sfx_filter.arr, o_chan_sep, epsilon );
system(msg);
*/

printf( "esemin0x 117 b4 exec  SQL  \n\r" ); /*Cyrus Debug*/

        EXEC SQL
             SELECT ATTENUATION_IN_DB
             INTO   :o_filter_att_db
             FROM   SFX_FILTER_ATT
             WHERE  FILTER_TYPE = :o_sfx_filter
             AND    ABS(CHANNEL_SEPARATION - :o_chan_sep) <= :epsilon;

        if (sqlca.sqlcode == NOT_FOUND)
        {
printf( "esemin0x 118 b4 exec  SQL  \n\r" ); /*Cyrus Debug*/
			
            fprintf(afp, "Warning: No attenuation for filter %s ",
                    o_sfx_filter.arr);
					
printf( "esemin0x 119 b4 exec  SQL  \n\r" ); /*Cyrus Debug*/
					
            fprintf(afp, "at channel separation %lf\n", o_chan_sep);
            o_filter_att_db = 0.0;
			
printf( "esemin0x 120 b4 exec  SQL  \n\r" ); /*Cyrus Debug*/
			
        }
/*
printf("sfx_filter att_db: %s %f\n", o_sfx_filter.arr, o_filter_att_db);
*/
    }
    else
printf( "esemin0x 121 b4 exec  SQL  \n\r" ); /*Cyrus Debug*/
		
        o_filter_att_db = 0.0;

min_usable_power = min_signal[subdist[prop->dist_index].noise_code][prop->band];

printf( "esemin0x 122 b4 exec  SQL  \n\r" ); /*Cyrus Debug*/

/*
printf("idx min noise band i delta intr: %d %f %d %d %d %lf %f\n", 
prop->dist_index, min_usable_power, subdist[prop->dist_index].noise_code,prop->band, i, delta_freq, power_intr);
printf("prop freq diffr vfeed gain off att_db al: %f %lf %f %d %f %f %f %f\n",
prop_loss,intr.tx_freq,diffract_loss,vict.feed_loss,vict.ant_gain,off_chan_rej,o_filter_att_db, prop->att_db);
*/

printf( "esemin0x 123 b4 run switch  \n\r" ); /*Cyrus Debug*/


switch (caller)
{
    case DESENSIT:
    {
        if (delta_freq < FREQ_EPSILON)
            i = 0;
        else
			
printf( "esemin0x 124 b4 for off_chan_cnt=%d  \n\r",off_chan_cnt ); /*Cyrus Debug*/

		
	    for (i = 1; i < off_chan_cnt; i++)
            {
                if (delta_freq < off_channel[i].ch_sep)
                    break;
                if (abs(delta_freq - off_channel[i].ch_sep) < FREQ_EPSILON)
                    break;    /* delta_freq = off_channel[i].ch_sep */
            }
/*
                if (delta_freq < off_channel[i].ch_sep)
                    if (abs(delta_freq - off_channel[i].ch_sep) > FREQ_EPSILON)
                        break;
*/

printf( "esemin0x 125   \n\r" ); /*Cyrus Debug*/

        off_chan_rej = (i == off_chan_cnt)?
                   off_channel[i-1].rej_db[prop->band]:
                   off_channel[i].rej_db[prop->band];

        power_intr =   intr.pw_dbw - prop_loss -  vict.feed_loss + vict.ant_gain
                     - off_chan_rej - o_filter_att_db;
        attenuation = power_intr - min_usable_power + 6.0;
        if (attenuation >= prop->desen_att_db)
            *intrfr_flag = TRUE;
/*
printf("att flag: %f %d\n",attenuation,*intrfr_flag);
*/
        break;
    }
    case INTERMOD:
    {
        double  x = log10(delta_freq);
        double  x2 = x * x;
        double  x3 = x2 * x;
        double  x4 = x2 * x2;
        double  c0 = 56.1666142692494105;
        double  c1 = 22.143965108789265;
        double  c2 = 17.365681294850533;
        double  c3 = 11.876775570883986;
        double  c4 = 3.8234601723933208;

        curve_adj  = (-1) * (c0 - c1*x - c2*x2 - c3*x3 - c4*x4);
        power_intr =   intr.pw_dbw - prop_loss -  vict.feed_loss + vict.ant_gain
                     - o_filter_att_db;
        
        if (power_intr > curve_adj + prop->intmod_att_db)
            *intrfr_flag = TRUE;
/*
printf("intr min intr_flag: %f %f %d\n", power_intr,min_usable_power,*intrfr_flag);
        break;
*/
    }
	
	
printf( "esemin0x 126   \n\r" ); /*Cyrus Debug*/	
	
}


#ifdef DEBUG
    if (interactive == FALSE)
    {
printf( "esemin0x 127   \n\r" ); /*Cyrus Debug*/		
        float a=20.0*log10(intr.tx_freq);
        float b=20.0*log_delta_dist;
        if (caller == DESENSIT)
        {
           printf("%10.4lf  %10.4lf  %6.2f   %6.1f  %6.1f  %6.2f  %3d   %5.2f",
                  intr.tx_freq, vict.rx_freq, attenuation, min_usable_power,
                  power_intr, intr.pw_dbw, vict.feed_loss, vict.ant_gain);
           printf("  %6.2f  %6.2f  %6.2f  %6.2f  %6.2f    %6.2f    %d\n", 
                  off_chan_rej, o_filter_att_db, prop_loss, a, b, diffract_loss,
                  *intrfr_flag);
        }
        if (caller == INTERMOD)
           printf("%10.4lf %10.4lf %6.1f  %6.1f %6.2f %3d  %5.2f  %7.2f  %6.2f %6.2f %6.2f %6.2f    %6.2f  %d\n", 
           intr.tx_freq, vict.rx_freq, min_usable_power, power_intr,
           intr.pw_dbw, vict.feed_loss, vict.ant_gain, curve_adj, 
           o_filter_att_db, prop_loss, a, b, diffract_loss, *intrfr_flag);
    }

printf( "esemin0x 128   \n\r" ); /*Cyrus Debug*/	
	
#endif

/* commented out by Chen Yung
return;
*/
/* changed by Chen Yung */
/* original: */
/*    return; */
    return(0);


sqlerr_rtn:
/* changed by Chen Yung */
/* original: */
/*    printf("%s\c", sqlca.sqlerrm.sqlerrmc); */

printf( "esemin0x *** error *** 126 %s  \n\r", sqlca.sqlerrm.sqlerrmc ); /*Cyrus Debug*/
    printf("%s\n", sqlca.sqlerrm.sqlerrmc);
    fflush(afp);
    fclose(afp);
    exit(1);

}


/**********************************************************************/
/*                                                                    */
/*    Module Name   :  get_vict_antenna_gain (esemin0x.pc)            */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. H. Mak                                      */
/*                                                                    */
/*    Callers       :  base_to_base(esemin0x.pc)                      */  
/*                                                                    */
/*    Parameters    :                                                 */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  Calculate antenna gain of victim station       */
/*                     with respect to interfering station.           */
/*                     Currently, only horizontal antenna gain is     */
/*                     considered.                                    */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

int get_vict_antenna_gain()
{

/*
#ifdef DEBUG
    printf("get_vict_antenna_gain\n");
#endif
*/


/*    strcpy(o_antenna.arr, vict.antenna); */
    strcpy((char *)o_antenna.arr, vict.antenna);
    o_antenna.len = strlen((char *)o_antenna.arr);
    
    EXEC SQL
         SELECT COUNT(ROWID)
         INTO   :o_cnt
         FROM   HZ_ANTENNA_GAIN
         WHERE  ANTENNA = :o_antenna;

    if (o_cnt == 0)
        fprintf(afp, "Warning: no horizontal antenna gain record for '%s' (%s, base no. %d)\n", 
                vict.antenna, vict.system_id, vict.base_no);
    else
    {
        double   ar2, ad;

/*
        double   ar1, ar2, ad;
        double   val_in_tan;

        ar1 = 5 * M_PI / 2 - vict.az_max_rad_r;
        if (ar1 - 2 * M_PI > (double)0)
            ar1 -= (2 * M_PI);
        else
            if (abs(ar1 - 2 * M_PI) <  EPSILON)
                ar1 = (double)0;

        if (((intr.grid[0] > vict.grid[0]) && (intr.grid[1] > vict.grid[1]))
        ||  ((intr.grid[0] > vict.grid[0]) && (intr.grid[1] < vict.grid[1])))
        {
            val_in_tan = (double)((intr.grid[1]-vict.grid[1])/((intr.grid[0]-vict.grid[0])));
            ar2 = atan(val_in_tan);
            ar2 = atan2((double)((intr.grid[1]-vict.grid[1])/10),
                        (double)((intr.grid[0]-vict.grid[0])/10));
        }
        
        if (((intr.grid[0] < vict.grid[0]) && (intr.grid[1] > vict.grid[1]))
        ||  ((intr.grid[0] < vict.grid[0]) && (intr.grid[1] < vict.grid[1])))
        {
            val_in_tan = (double)((intr.grid[1]-vict.grid[1])/((intr.grid[0]-vict.grid[0])));
            ar2 = atan(val_in_tan) + M_PI_2;
            ar2 =   atan2((double)((intr.grid[1]-vict.grid[1])/10),
                          (double)((intr.grid[0]-vict.grid[0])/10))
                  + M_PI_2;
        }
*/

        ar2 = atan2((double)(intr.grid[0]-vict.grid[0]),
                    (double)(intr.grid[1]-vict.grid[1]));

        if (intr.grid[0] == vict.grid[0])
		{   /* 20170703 Cyrusma -Add "{}" into if statement */
            if (intr.grid[1] == vict.grid[1])
                ar2 = 0; /* co-site antennae */
            else
                ar2 = (intr.grid[1] > vict.grid[1]) ? 0 : M_PI;
		}	

        if (intr.grid[1] == vict.grid[1])
		{   /* 20170703 Cyrusma -Add "{}" into if statement */
	
            if (intr.grid[0] == vict.grid[0])
                ar2 = 0; /* co-site antennae */
            else
                ar2 = (intr.grid[0] > vict.grid[0]) ? M_PI_2 : (3 * M_PI_2);
		}
/*
        ad = (ar1 - ar2) * (180 / M_PI);
*/

        if ((ar2 - 0) > EPSILON)
            ad = abs((vict.az_max_rad_r - ar2) * (180 / M_PI));
        else
            if (abs(ar2 - 0) < EPSILON)    /* ie., ar2 = 0 */
                ad = vict.az_max_rad_r * (180 / M_PI);
            else
                ad = abs((vict.az_max_rad_r - ar2) * (180 / M_PI) - 360.0);

        if (ad >= 0.0)
            if (ad > 360.0)
                o_degree = (int)(ad - 360.0 + 0.5);
            else
                o_degree = (int)(ad + 0.5);
        else
            o_degree = (int)(360.0 + ad + 0.5);

        if (o_degree == 360)
            o_degree = 0;

/*
printf("intr vict: %f %f %f %f\n",
            intr.grid[0],intr.grid[1],vict.grid[0],vict.grid[1]);
printf("az azr ar2 ad degree: %d %f %f %f %d\n", vict.az_max_rad, vict.az_max_rad_r, ar2, ad, o_degree);
printf("az azr ar1 ar2 ad degree: %d %f %f %f %f %d\n", vict.az_max_rad, vict.az_max_rad_r, ar1, ar2, ad, o_degree);
*/
/*
system("echo \"esemin0x 2\" >> /tmp/debug");
sprintf(msg, "echo \"%s %d\" >> /tmp/debug", o_antenna.arr , o_degree );
system(msg);
*/

        EXEC SQL
             SELECT GAIN_IN_DB
             INTO   :o_gain_in_db
             FROM   HZ_ANTENNA_GAIN
             WHERE  ANTENNA = :o_antenna
             AND    DEGREE = :o_degree;

        if (sqlca.sqlcode == NOT_FOUND)
        {
            fprintf(afp, "Warning: No horizontal gain for antenna %s ",
                    vict.antenna);
            fprintf(afp, "at degree %d (%s, base no. %d)\n", 
                    o_degree, vict.system_id, vict.base_no);
        }
        else
            vict.ant_gain = o_gain_in_db;
    }

/* commented out by Chen Yung
    return;
*/
/* changed by Chen Yung */
/* original: */
/*    return; */
    return(0);

sqlerr_rtn:
/* changed by Chen Yung */
/* original: */
/*    printf("%s\c", sqlca.sqlerrm.sqlerrmc); */
    printf("%s\n", sqlca.sqlerrm.sqlerrmc);
    fflush(afp);
    fclose(afp);
    exit(1);

}
