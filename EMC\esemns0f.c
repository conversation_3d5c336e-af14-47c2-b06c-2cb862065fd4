
/* Result Sets Interface */
#ifndef SQL_CRSR
#  define SQL_CRSR
  struct sql_cursor
  {
    unsigned int curocn;
    void *ptr1;
    void *ptr2;
    unsigned int magic;
  };
  typedef struct sql_cursor sql_cursor;
  typedef struct sql_cursor SQL_CURSOR;
#endif /* SQL_CRSR */

/* Thread Safety */
typedef void * sql_context;
typedef void * SQL_CONTEXT;

/* Object support */
struct sqltvn
{
  unsigned char *tvnvsn; 
  unsigned short tvnvsnl; 
  unsigned char *tvnnm;
  unsigned short tvnnml; 
  unsigned char *tvnsnm;
  unsigned short tvnsnml;
};
typedef struct sqltvn sqltvn;

struct sqladts
{
  unsigned int adtvsn; 
  unsigned short adtmode; 
  unsigned short adtnum;  
  sqltvn adttvn[1];       
};
typedef struct sqladts sqladts;

static struct sqladts sqladt = {
  1,0,0,
};

/* Binding to PL/SQL Records */
struct sqltdss
{
  unsigned int tdsvsn; 
  unsigned short tdsnum; 
  unsigned char *tdsval[1]; 
};
typedef struct sqltdss sqltdss;
static struct sqltdss sqltds =
{
  1,
  0,
};

/* File name & Package Name */
struct sqlcxp
{
  unsigned short fillen;
           char  filnam[12];
};
static const struct sqlcxp sqlfpn =
{
    11,
    "esemns0f.pc"
};


static unsigned int sqlctx = 150059;


static struct sqlexd {
   unsigned long  sqlvsn;
   unsigned int   arrsiz;
   unsigned int   iters;
   unsigned int   offset;
   unsigned short selerr;
   unsigned short sqlety;
   unsigned int   occurs;
      const short *cud;
   unsigned char  *sqlest;
      const char  *stmt;
   sqladts *sqladtp;
   sqltdss *sqltdsp;
   unsigned char  **sqphsv;
   unsigned long  *sqphsl;
            int   *sqphss;
            short **sqpind;
            int   *sqpins;
   unsigned long  *sqparm;
   unsigned long  **sqparc;
   unsigned short  *sqpadto;
   unsigned short  *sqptdso;
   unsigned int   sqlcmax;
   unsigned int   sqlcmin;
   unsigned int   sqlcincr;
   unsigned int   sqlctimeout;
   unsigned int   sqlcnowait;
            int   sqfoff;
   unsigned int   sqcmod;
   unsigned int   sqfmod;
   unsigned int   sqlpfmem;
   unsigned char  *sqhstv[17];
   unsigned long  sqhstl[17];
            int   sqhsts[17];
            short *sqindv[17];
            int   sqinds[17];
   unsigned long  sqharm[17];
   unsigned long  *sqharc[17];
   unsigned short  sqadto[17];
   unsigned short  sqtdso[17];
} sqlstm = {13,17};

/* SQLLIB Prototypes */
extern void sqlcxt (void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlcx2t(void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlbuft(void **, char *);
extern void sqlgs2t(void **, char *);
extern void sqlorat(void **, unsigned int *, void *);

/* Forms Interface */
static const int IAPSUCC = 0;
static const int IAPFAIL = 1403;
static const int IAPFTL  = 535;
extern void sqliem(unsigned char *, signed int *);

 static const char *sq0003 = 
"select SYS_CATEGORY ,SYS_TYPE ,SYS_NO ,SYS_SUFFIX ,BASE_NO ,EAST ,NORTH ,STA\
TION_TYPE ,SUBDISTRICT ,NVL(E_CALLSIGN,' ') ,DECODE(ROOM,'','',('RM-'||ROOM)) \
,DECODE(BLOCK,'','',('BK-'||BLOCK)) ,DECODE(FLOOR,'','',(FLOOR||'/F')) ,NVL(BL\
DG,' ') ,STREET_NO ,STREET_NAME ,NVL(TO_CHAR(CANCEL_DATE),'-')  from STATION w\
here (NORTH between :b0 and :b1 and EAST between :b2 and :b3) order by SYS_CAT\
EGORY,SYS_TYPE,SYS_NO,SYS_SUFFIX,BASE_NO,STATION_FLAG,RECORD_TYPE            ";

typedef struct { unsigned short len; unsigned char arr[1]; } VARCHAR;
typedef struct { unsigned short len; unsigned char arr[1]; } varchar;

/* cud (compilation unit data) array */
static const short sqlcud0[] =
{13,4242,1,0,0,
5,0,0,1,122,0,4,1140,0,0,5,4,0,1,0,2,9,0,0,1,1,0,0,1,9,0,0,1,9,0,0,1,9,0,0,
40,0,0,2,153,0,4,1186,0,0,7,6,0,1,0,2,1,0,0,1,1,0,0,1,9,0,0,1,9,0,0,1,9,0,0,1,
3,0,0,1,3,0,0,
83,0,0,3,465,0,9,1267,0,0,4,4,0,1,0,1,3,0,0,1,3,0,0,1,3,0,0,1,3,0,0,
114,0,0,3,0,0,13,1269,0,0,17,0,0,1,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,3,0,0,2,
3,0,0,2,3,0,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,9,0,0,
2,9,0,0,2,9,0,0,
197,0,0,3,0,0,15,1279,0,0,0,0,0,1,0,
212,0,0,4,174,0,4,1327,0,0,8,7,0,1,0,2,3,0,0,1,1,0,0,1,9,0,0,1,9,0,0,1,9,0,0,1,
3,0,0,1,3,0,0,1,3,0,0,
259,0,0,5,122,0,4,1352,0,0,5,4,0,1,0,2,9,0,0,1,1,0,0,1,9,0,0,1,9,0,0,1,9,0,0,
294,0,0,3,0,0,13,1503,0,0,17,0,0,1,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,3,0,0,2,
3,0,0,2,3,0,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,9,0,0,
2,9,0,0,2,9,0,0,
377,0,0,3,0,0,15,1518,0,0,0,0,0,1,0,
};


/**********************************************************************/
/*                                                                    */
/*    Module Name   :  main (esemns0f.pc)                             */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. H. Mak                                      */
/*                                                                    */
/*    Callers       :  esemcn0f.c through 'system' statement          */  
/*                                                                    */
/*    Parameters    :  user id.                                       */
/*                     user password                                  */
/*                     EMC user id.                                   */
/*                     printer id.                                    */
/*                                                                    */
/*    Called Modules:  screen functions from 'screen.c'               */
/*                     (centre_msg, disp_err, disp_space)             */
/*                     screen function from 'cursesX'                 */
/*                     user_login and user_logout (login.pc)          */
/*                                                                    */
/*    Purpose       :  Accept user input of system id, grid           */
/*                     location, culling distance, culling            */
/*                     frequency range and perform near-site          */
/*                     analysis. Near-site report(s) are              */
/*                     generated if any near-site station(s)          */      
/*                     near-site station(s) are found.                */ 
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak          Apr 93      Initial version                   */
/*   Paul H. B. SEN     Feb 96      1st revision                      */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <time.h>
#include <curses.h>
#include "../include/emc.h"
#include "../include/propose.h"
#include "../include/ref.h"
#include "../include/define.h"
#include "../include/exist.h"

#define  MAX_FLD_LEN    8      /* max. field length of input field */

#define  LOGIN_OK       0

/* ORACLE status */
#define  FOUND          0
#define  NOT_FOUND      1403

#define  NEXT_START_PT  7     /* field index at which user starts his */

                              /* input for next near-site analysis    */

#define  SMALLEST_DIST  10
#define  EPSILON        0.00005

#define  NORTH_LOW      4650
#define  NORTH_HIGH     48690

#define  EAST_LOW       1100
#define  EAST_HIGH      60380

#define  LINES_PER_PAGE    58

#include "../include/screen.h"

/* field validation function declarations */
int    chk_east_grid();
int    chk_north_grid();
int    chk_distance();         /* modified */
int    chk_freq_range();       

/* For field definitions, see ../include/screen.h */
/* item[0] up to item[8] for valid fields (modified) */ 
FIELD item[] =
{
   6, 39,STRING,     TRUE, 1, 0, NEW, " ",          "",0,FALSE,(int(*)())NULL,
   6, 41,STRING,     TRUE, 2, 0, NEW, "  ",         "",0,FALSE,(int(*)())NULL,
   6, 44,STRING,     TRUE, 7, 0, NEW, "       ",    "",0,FALSE,(int(*)())NULL,
   6, 52,STRING,     TRUE, 3, 0, NEW, "   ",        "",0,FALSE,(int(*)())NULL,
   8, 39,INTEGER,   FALSE, 4, 0, NEW, "250 ",       "",0,FALSE,chk_distance,  
   10,39,INTEGER,   FALSE, 4, 0, NEW, "800 ",       "",0,FALSE,(int(*)())NULL, 
   10,46,INTEGER,   FALSE, 4, 0, NEW, "999 ",       "",0,FALSE,chk_freq_range, 
   12,42,INTEGER,    TRUE, 5, 0, NEW, "00000",      "",0,FALSE,chk_east_grid,
   12,53,INTEGER,    TRUE, 5, 0, NEW, "00000",      "",0,FALSE,chk_north_grid, 
   -1,-1,DUMMY,      TRUE, 0, 0, NEW, "",           "",0,FALSE,(int(*)())NULL
};

/*
char   *item_err_msg[] = 
{
   "Distance covered too small", 
   "", "", "", "",
   "Invalid east grid",
   "Invalid north grid",
   ""
};
*/
     
char   *prog_id = "emns0f_01";
char   *screen_head = "NEAR SITE ANALYSIS"; /* modified */

float  cal_dist();

char            *getenv();
extern double   atof();

char   passwd[20];
char   emc_uid[20];
char   curr_sys_id[15];
char   prop_sys_desc[30];
int    distance;       /* modified to accept default value */
int    cull_grid;

extern char msg[];

/* EXEC SQL BEGIN DECLARE SECTION; */ 

/* ORACLE variables for report generation */

    int      o_freq_lo;      /* value to be assigned by global counterpart */ 
    int      o_freq_hi;      /*  ditto  */
    int      o_grid_east;
    int      o_grid_north;
    int      o_p_east;
    int      o_p_north;
    int      o_e_east;
    int      o_e_north;
    int      o_east;
    int      o_west;
    int      o_north;
    int      o_south;
    int      o_base_no;
    int      o_cnt;
    char     o_p_sys_category;
    /* VARCHAR  o_p_sys_type[3]; */ 
struct { unsigned short len; unsigned char arr[3]; } o_p_sys_type;

    /* VARCHAR  o_p_sys_no[8]; */ 
struct { unsigned short len; unsigned char arr[8]; } o_p_sys_no;

    /* VARCHAR  o_p_sys_suffix[4]; */ 
struct { unsigned short len; unsigned char arr[4]; } o_p_sys_suffix;

    /* VARCHAR  o_p_sys_desc[21]; */ 
struct { unsigned short len; unsigned char arr[21]; } o_p_sys_desc;

    char     o_p_stn_type;
    char     o_sys_category;
    /* VARCHAR  o_sys_type[3]; */ 
struct { unsigned short len; unsigned char arr[3]; } o_sys_type;

    /* VARCHAR  o_sys_no[8]; */ 
struct { unsigned short len; unsigned char arr[8]; } o_sys_no;

    /* VARCHAR  o_sys_suffix[4]; */ 
struct { unsigned short len; unsigned char arr[4]; } o_sys_suffix;

    /* VARCHAR  o_sys_desc[21]; */ 
struct { unsigned short len; unsigned char arr[21]; } o_sys_desc;

    char     o_stn_type;
    /* VARCHAR  o_sub_district[4]; */ 
struct { unsigned short len; unsigned char arr[4]; } o_sub_district;

    /* VARCHAR  o_cellsite[26]; */ 
struct { unsigned short len; unsigned char arr[26]; } o_cellsite;

    /* VARCHAR  o_room[9]; */ 
struct { unsigned short len; unsigned char arr[9]; } o_room;

    /* VARCHAR  o_block[8]; */ 
struct { unsigned short len; unsigned char arr[8]; } o_block;

    /* VARCHAR  o_floor[6]; */ 
struct { unsigned short len; unsigned char arr[6]; } o_floor;

    /* VARCHAR  o_bldg[21]; */ 
struct { unsigned short len; unsigned char arr[21]; } o_bldg;

    /* VARCHAR  o_street_no[11]; */ 
struct { unsigned short len; unsigned char arr[11]; } o_street_no;

    /* VARCHAR  o_street_name[31]; */ 
struct { unsigned short len; unsigned char arr[31]; } o_street_name;

    /* VARCHAR  o_cancel_date[10]; */ 
struct { unsigned short len; unsigned char arr[10]; } o_cancel_date;


/* EXEC SQL END DECLARE SECTION; */ 


/* EXEC SQL INCLUDE SQLCA;
 */ 
/*
 * $Header: sqlca.h 24-apr-2003.12:50:58 mkandarp Exp $ sqlca.h 
 */

/* Copyright (c) 1985, 2003, Oracle Corporation.  All rights reserved.  */
 
/*
NAME
  SQLCA : SQL Communications Area.
FUNCTION
  Contains no code. Oracle fills in the SQLCA with status info
  during the execution of a SQL stmt.
NOTES
  **************************************************************
  ***                                                        ***
  *** This file is SOSD.  Porters must change the data types ***
  *** appropriately on their platform.  See notes/pcport.doc ***
  *** for more information.                                  ***
  ***                                                        ***
  **************************************************************

  If the symbol SQLCA_STORAGE_CLASS is defined, then the SQLCA
  will be defined to have this storage class. For example:
 
    #define SQLCA_STORAGE_CLASS extern
 
  will define the SQLCA as an extern.
 
  If the symbol SQLCA_INIT is defined, then the SQLCA will be
  statically initialized. Although this is not necessary in order
  to use the SQLCA, it is a good pgming practice not to have
  unitialized variables. However, some C compilers/OS's don't
  allow automatic variables to be init'd in this manner. Therefore,
  if you are INCLUDE'ing the SQLCA in a place where it would be
  an automatic AND your C compiler/OS doesn't allow this style
  of initialization, then SQLCA_INIT should be left undefined --
  all others can define SQLCA_INIT if they wish.

  If the symbol SQLCA_NONE is defined, then the SQLCA variable will
  not be defined at all.  The symbol SQLCA_NONE should not be defined
  in source modules that have embedded SQL.  However, source modules
  that have no embedded SQL, but need to manipulate a sqlca struct
  passed in as a parameter, can set the SQLCA_NONE symbol to avoid
  creation of an extraneous sqlca variable.
 
MODIFIED
    lvbcheng   07/31/98 -  long to int
    jbasu      12/12/94 -  Bug 217878: note this is an SOSD file
    losborne   08/11/92 -  No sqlca var if SQLCA_NONE macro set 
  Clare      12/06/84 - Ch SQLCA to not be an extern.
  Clare      10/21/85 - Add initialization.
  Bradbury   01/05/86 - Only initialize when SQLCA_INIT set
  Clare      06/12/86 - Add SQLCA_STORAGE_CLASS option.
*/
 
#ifndef SQLCA
#define SQLCA 1
 
struct   sqlca
         {
         /* ub1 */ char    sqlcaid[8];
         /* b4  */ int     sqlabc;
         /* b4  */ int     sqlcode;
         struct
           {
           /* ub2 */ unsigned short sqlerrml;
           /* ub1 */ char           sqlerrmc[70];
           } sqlerrm;
         /* ub1 */ char    sqlerrp[8];
         /* b4  */ int     sqlerrd[6];
         /* ub1 */ char    sqlwarn[8];
         /* ub1 */ char    sqlext[8];
         };

#ifndef SQLCA_NONE 
#ifdef   SQLCA_STORAGE_CLASS
SQLCA_STORAGE_CLASS struct sqlca sqlca
#else
         struct sqlca sqlca
#endif
 
#ifdef  SQLCA_INIT
         = {
         {'S', 'Q', 'L', 'C', 'A', ' ', ' ', ' '},
         sizeof(struct sqlca),
         0,
         { 0, {0}},
         {'N', 'O', 'T', ' ', 'S', 'E', 'T', ' '},
         {0, 0, 0, 0, 0, 0},
         {0, 0, 0, 0, 0, 0, 0, 0},
         {0, 0, 0, 0, 0, 0, 0, 0}
         }
#endif
         ;
#endif
 
#endif
 
/* end SQLCA */



main(argc, argv)
int    argc;
char   **argv;
{
    char    ns_fname[150];
    char    *confirm_msg = "CONFIRM (Y/N)? ";
    char    err_msg[80];
    char    answer;
    char    cmdline[150];
    char    category_str[2];
    char    prev_sys_id[15];            /* previous system id., used for    */
                                        /* checking whether current system  */
                                        /* is the same as previous system   */
    float   p_grid[2];                  /* user-input near-site grid        */
    FILE    *nfp;
    int     term_code;
    int     item_cnt;                   /* total no. of items               */
    int     last_item;                  /* index of last filled-up item     */

    int     x_pos, y_pos;               /* current cursor position          */
    int     curr_pos;                   /* current position of input string */
    int     loop;                       /* TRUE if entry continue on        */
                                        /* current item                     */
    int     modified = FALSE;           /* TRUE when current item has been  */
                                        /* modified                         */
    int     token;                      /* user-input character             */
    int     status = !(QUIT);
    int     direction;                  /* field shuttle direction          */
    int     confirm = FALSE;
    int     err_flag = FALSE;
    int     prev_p_east = -999;
    int     prev_p_north = -999;
    int     report_open = FALSE;        /* TRUE when a near-site report has */
                                        /* been opened                      */
    int     nearsite_cnt = 0;           /* count no. of nearsite records    */
                                        /* printed for a site of a proposed */
                                        /* system                           */
    int     line_cnt = 0;
    int     page_cnt = 1;
    int     i;

    register int     j;
    struct tm  *tt;
    char s[80];


    /* EXEC SQL WHENEVER NOT FOUND CONTINUE; */ 


    initscr();
    raw();
    noecho();
    clear();
    keypad(stdscr, TRUE); 

    if (argc != 6)
    {
       sprintf(err_msg, "Usage: esemns0f user_id passwd emc_uid -P printer_id");
       goto force_exit;
    }

    if (strcmp(argv[4], "-P"))
    {
       sprintf(err_msg, "Usage: esemns0f user_id passwd emc_uid -P printer_id");
       goto force_exit;
    }
    
    strcpy(user_id, argv[1]);
    strcpy(passwd, argv[2]);

    if (user_login(user_id, passwd, err_msg) != LOGIN_OK)
    {
        strip_blank(err_msg, TRAIL);
        strcat(err_msg, "  ");
        strcat(err_msg, user_id);
        strcat(err_msg, passwd);
        goto force_exit;
    }

    /* EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn; */ 


    strcpy(emc_uid, argv[3]);
    strcpy(printer_id, argv[5]);
    get_sys_date_time(sys_date, sys_time, yymmdd, hhmmss);

    for (i = 0; item[i].xpos != -1; i++)
        ;

    item_cnt = i;

    emc_dir = getenv("EMC");
    disp_heading(prog_id, screen_head, sys_date);
    disp_entry_scn(item_cnt, item_cnt, NEW_SCREEN);
 
    prev_sys_id[0] = '\0';
    category_str[1] = '\0';

/*
    o_freq_lo = 66;
    o_freq_hi = 74;
*/

    for (i = 0; status != QUIT; )
    {
        x_pos = item[i].xpos; 
        y_pos = item[i].ypos;

        /**********************************************************/
        /* if data type of current field is sign field, then skip */
        /* the 1st position of current string and increment       */
        /* screen x-coordinate by 1, else keep 1st position       */
        /**********************************************************/
        if (is_sign(&item[i]))
        {
            curr_pos = 1;
            x_pos++;
        }
        else
            curr_pos = 0;
        move(y_pos, x_pos);
        loop = TRUE;

        for (; loop == TRUE;)
        {
            refresh();
            token = getch();

            if (err_flag)
            {
                err_flag = FALSE;
                attroff(A_REVERSE);
                getyx(stdscr, y_pos, x_pos);
                clear_err();
                move(y_pos, x_pos);
                attron(A_REVERSE);
            }

            /**************************************/
            /* Function key definitions:          */
            /*    KEY_F(1)  - quit session        */
            /*    KEY_F(2)  - confirm entry       */
            /*    KEY_F(3)  - refresh screen      */
            /*    KEY_F(4)  - clear field         */
            /*    TAB,                            */
            /*    NEWLINE,                        */
            /*    KEY_DOWN  - next field          */
            /*    CTRL_B,                         */
            /*    KEY_UP    - next field          */
            /*    KEY_LEFT  - next char position  */
            /*    KEY_RIGHT - next char position  */
            /*    DELETE,                         */
            /*    BACKSPACE - delete current char */
            /*                and back 1 position */
            /**************************************/

            switch (token)
            {
                case KEY_F(1):
                    status = QUIT;
                    loop = FALSE;
                    break;

                case KEY_F(2):
                    confirm = TRUE;
                    loop = FALSE;
                    break;

                case KEY_F(3):
                    attroff(A_REVERSE);
                    refresh_screen(item_cnt, i);
                    move(y_pos, x_pos);
                    break; 

                case KEY_F(4):
                    init_field(&item[i], &curr_pos);
                    loop = FALSE; 
                    direction = RESTART; 
                    break; 

                case KEY_UP:
                case CTRL_B:
                    if (i == 0)
                        beep();
                    else
                    {
                        loop = FALSE;
                        direction = BACKWARD;
                    }
                    break;

                case KEY_DOWN:
                case NEWLINE:
                case TAB:
/*
sprintf(s," i len req: %d %d %d", i, item[i].curr_len, item[i].required);
mvaddstr(23,0,s);
                    if ((item[i].state == NEW) && (item[i].required))
                    if ((is_sign(&item[i]) && (item[i].curr_len == 1))
                     || (!is_sign(&item[i]) && empty(&item[i]))
                    && (item[i].required))
*/
                    /*********************************************************/
                    /* beep user if he wants to skip an input-required field */
                    /*********************************************************/
                    if (is_sign(&item[i]))
                    {
                        if ((item[i].curr_len == 1) && (item[i].required))
                        {
                            err_flag = TRUE;
                            disp_err(INPUT_REQ_MSG);
                            attron(A_REVERSE);
                            break;
                        }
                    }
                    else
                        if (empty(&item[i]) && (item[i].required))
                        {
                            err_flag = TRUE;
                            disp_err(INPUT_REQ_MSG);
                            attron(A_REVERSE);
                            break;
                        }

                    if (empty(&item[i]) 
                    ||  (is_sign(&item[i]) && (item[i].curr_len == 1)))
                        strcpy(item[i].curr_str, item[i].init_str);

                    loop = FALSE;
                    direction = FORWARD;
                    break;

                case KEY_LEFT:

                    /************************************/
                    /* beep user if he has reached the  */
                    /* left-most position of a field    */
                    /************************************/
                    if (is_sign(&item[i]))
                        if (x_pos == item[i].xpos + 1)
                        {
                            beep();
                            break;
                        }

                    if (x_pos == item[i].xpos)
                        beep();
                    else
                    {
                        x_pos--; curr_pos--;
                    }
                    break;

                case KEY_RIGHT:

                    /************************************/
                    /* beep user if he has reached the  */
                    /* right boundary of current field  */
                    /************************************/
                    if (x_pos - item[i].xpos == item[i].curr_len)
                        beep();
                    else
                    {
                        x_pos++; curr_pos++;
                    }
                    break;

                case BACKSPACE:
                case DELETE:

                    /************************************/
                    /* beep user if he has reached the  */
                    /* left-most position of a field    */
                    /************************************/
                    if (is_sign(&item[i]))
                        if (x_pos == item[i].xpos + 1)
                        {
                            beep();
                            break;
                        }

                    if (x_pos == item[i].xpos)
                        beep();
                    else
                    {
                        x_pos--; curr_pos--;
                        if (is_float(&item[i]) 
                        && (item[i].curr_str[curr_pos] == DOT))
                            item[i].has_dot = FALSE;
                        move(y_pos, x_pos);
                        addch(BLANK);
                        modified = TRUE;
/*
                        if (x_pos == item[i].xpos)
                            item[i].state = NEW;
                        else
                            item[i].state = MODIFIED;
*/
                    }
                    break;

                case DOT:

                    /******************************************************/
                    /* beep user if data type of current field is integer */
                    /******************************************************/
                    if (is_int(&item[i]))
                    {
                        beep();
                        break;
                    }


                    /********************************************************/
                    /* beep user if data type of current field is float and */
                    /* user has already input one dot                       */
                    /********************************************************/
                    if (is_float(&item[i]))
                        if (item[i].has_dot)
                        {
                            beep();
                            break;
                        }
                        else
                            item[i].has_dot = TRUE;

                    if (item[i].state == NEW)
                        if (is_sign(&item[i]))
                        {
                            if (curr_pos == 1)
                            {
                                disp_space(y_pos, x_pos, item[i].fld_len - 1);
                                move(y_pos, x_pos);
                            }
                        }
                        else
                            if (curr_pos == 0)
                            {
                                disp_space(y_pos, x_pos, item[i].fld_len);
                                move(y_pos, x_pos);
                            }

                    addch(token);
                    item[i].curr_str[curr_pos] = token;
                    x_pos++; curr_pos++;
                    modified = TRUE;
                    item[i].state = MODIFIED;

                    if (x_pos - item[i].xpos == item[i].fld_len)
                    {
                        item[i].curr_str[item[i].fld_len] = '\0';
                        item[i].curr_len = item[i].fld_len;
                        loop = FALSE;
                        direction = FORWARD;
                    }

                    break;

                default:
                    if ((!isalnum(token)) && (token != MINUS) 
                    &&  (token != PLUS))
                    {
                        beep();
                        break;
                    }

                    if (!isdigit(token))
                    {
/*
mvaddstr(23, 0, "not digit");
*/
                        if (is_sign(&item[i]))
                        {
                            if ((token != MINUS) && (token != PLUS))
                            {
                                beep();
                                break;
                            }
                            else
                                if (curr_pos > 1)
                                {
                                    beep();
                                    break;
                                }
                        }
                        else
/*
sprintf(s, "should not be integer: %d %d %s", i, item[i].type, item[i].init_str);
mvaddstr(22, 0, s);
*/
                            if ((item[i].type != CHAR) 
                            &&  (item[i].type != STRING))
                            {
                                beep();
                                break;
                            }
                            else
                                if (isalpha(token))
                                    token = toupper(token); 
                    }
                    else
                        if (item[i].state == NEW)
                            if (is_sign(&item[i]))
                            {
                                if (curr_pos == 1)
                                {
                                    disp_space(y_pos, x_pos, item[i].fld_len-1);
                                    move(y_pos, x_pos);
                                }
                            }
                            else
                                if (curr_pos == 0)
                                {
                                    disp_space(y_pos, x_pos, item[i].fld_len);
                                    move(y_pos, x_pos);
                                }

                    if (((token == MINUS) || (token == PLUS)) 
                    &&  is_sign(&item[i]))
                    {
                        x_pos--; curr_pos--;  /* because we don't want to  */
                                              /* move cursor to 1 position */
                                              /* this statement is used    */
                                              /* to complement the         */
                                              /* 'x_pos++; curr_pos++'     */
                                              /* a few lines below         */
                        if (token == MINUS)
                        {
                            item[i].curr_str[0] = MINUS;
                            move(y_pos, x_pos);
                            addch(MINUS);
                        }
                            
                    }
                    else
                    {
                        item[i].curr_str[curr_pos] = token;
                        addch(token);
                    }

/*
sprintf(s, "modified");
mvaddstr(22, 0, s);
*/
                    if ((token != MINUS) && (token != PLUS))
                        item[i].state = MODIFIED;

                    modified = TRUE;
                    x_pos++; curr_pos++;

                    if (x_pos - item[i].xpos == item[i].fld_len)
                    {
                        item[i].curr_str[item[i].fld_len] = '\0';
                        item[i].curr_len = item[i].fld_len;
                        loop = FALSE;
                        direction = FORWARD;
                    }
                
                    break;
            }

            refresh();
            if (loop == FALSE)
            {
                int    (*check_item)();

                check_item = item[i].validate;
                if (check_item != (int(*)())NULL)
                {
                    if ((direction != BACKWARD) && (!empty(&item[i])))
                        if ((*check_item)(&i, err_msg) == ERROR)
                        {
                            err_flag = TRUE;
                            disp_err(err_msg);
                            attron(A_REVERSE);
                            loop = FALSE;
                            direction = RESTART;
                            if (confirm)
                                confirm = FALSE;
                        }
                }

                if (confirm)
                {
                    for (j = 0; j < item_cnt; j++)
                        if (is_sign(&item[j]))
                        {
                            if ((item[j].curr_len == 1) 
                            &&  (item[j].required == TRUE))
                                {
                                    confirm = FALSE;
                                    direction = RESTART;
                                    err_flag = TRUE;
                                    disp_err(INPUT_REQ_MSG);
                                    attron(A_REVERSE);
                                    i = j;
                                    break;
                                }
                        }
                        else
                            if (empty(&item[j]) && (item[j].required == TRUE))
                            {
                                confirm = FALSE;
                                direction = RESTART;
                                err_flag = TRUE;
                                disp_err(INPUT_REQ_MSG);
                                attron(A_REVERSE);
                                i = j;
                                break;
                            }
                }

                switch (direction)
                {
                    case FORWARD:
                        i++;
                        break;
                    case BACKWARD:
                        i--;
                        break;
                    case RESTART:
                        break;
                }

                continue;
            }
            
            if (modified && (!err_flag))
            {
                int    len = x_pos - item[i].xpos;
   
                if ((item[i].curr_len < len) || is_delete(token))
                {
                    item[i].curr_len = len;
                    item[i].curr_str[item[i].curr_len] = '\0';
                }
/*
disp_space(22,0,80);
sprintf(s,"i mode: %d %d", i, tx_mode);
mvaddstr(22,0,s);
*/

                modified = FALSE;
            }
  
            move(y_pos, x_pos);
        }

        if (((i == item_cnt) || confirm) && (!err_flag))
        {
            confirm = FALSE;
            centre_msg(19, confirm_msg, A_NORMAL, A_REVERSE);
            getyx(stdscr, y_pos, x_pos);
            x_pos += 2;
            disp_space(y_pos, x_pos, 1);
            attroff(A_REVERSE);
            move(y_pos, x_pos);
            refresh();
            read_str(&answer, "", 1, 0, 10, &term_code, A_REVERSE);
            answer = toupper(answer);
            disp_space(19, 0, 80);
            attron(A_REVERSE);
            if (answer == 'Y')
            {
                i = NEXT_START_PT;
                hide_cursor();
                centre_msg(23, "Processing ...", A_BLINK, A_REVERSE);
                cull_grid = distance = atoi(item[4].curr_str);
                o_freq_lo = atoi(item[5].curr_str);
                o_freq_hi = atoi(item[6].curr_str); 
                o_p_sys_category = item[0].curr_str[0];
                category_str[0] = o_p_sys_category;
/*                strcpy(o_p_sys_type.arr, item[1].curr_str);
                strcpy(o_p_sys_no.arr, item[2].curr_str);
                (empty(&item[3])) ?
                    strcpy(o_p_sys_suffix.arr, item[3].init_str):
                    strcpy(o_p_sys_suffix.arr, item[3].curr_str);
                o_p_sys_type.len = strlen(o_p_sys_type.arr);
                o_p_sys_no.len = strlen(o_p_sys_no.arr);
                o_p_sys_suffix.len = strlen(o_p_sys_suffix.arr); */

                strcpy((char *)o_p_sys_type.arr, item[1].curr_str);
                strcpy((char *)o_p_sys_no.arr, item[2].curr_str);
                (empty(&item[3])) ?
                    strcpy((char *)o_p_sys_suffix.arr, item[3].init_str):
                    strcpy((char *)o_p_sys_suffix.arr, item[3].curr_str);
                o_p_sys_type.len = strlen((char *)o_p_sys_type.arr);
                o_p_sys_no.len = strlen((char *)o_p_sys_no.arr);
                o_p_sys_suffix.len = strlen((char *)o_p_sys_suffix.arr);
                
                sprintf(curr_sys_id, "%s%s%s-%s", category_str, 
                        o_p_sys_type.arr, o_p_sys_no.arr, o_p_sys_suffix.arr);
                p_grid[0] = (float)atof(item[7].curr_str);
                p_grid[1] = (float)atof(item[8].curr_str);
                o_p_east = (int)(p_grid[0] + EPSILON);
                o_p_north = (int)(p_grid[1] + EPSILON);
/*
                o_p_east = (int)(p_grid[0] + EPSILON) * 10;
                o_p_north = (int)(p_grid[1] + EPSILON) * 10;
*/
                get_prop_sys_desc(curr_sys_id);

                /***********************************************************/ 
                /* if current system id is not equal to previous system id */
                /* then a new near-site report is created for this system  */ 
                /***********************************************************/ 
                if (strcmp(prev_sys_id, curr_sys_id))
                {
                    if (prev_sys_id[0] != '\0')
                    {
                        fprintf(nfp, "\n%51s", "");
                        fprintf(nfp, "No. of near sites printed: %d",
                                nearsite_cnt);
                        fclose(nfp);
                        sprintf(cmdline, "lp -dprinter1 %s > /dev/null", ns_fname);
                        system(cmdline);
                        nearsite_cnt = line_cnt = 0;
                        page_cnt = 1;
                    }

                    report_open = FALSE;
                    strcpy(prev_sys_id, curr_sys_id);
                    prev_p_east = o_p_east;
                    prev_p_north = o_p_north;
                    sprintf(ns_fname, "%s/nearsite/%s.%c%s%s%s.%d", emc_dir, 
                            emc_uid, o_p_sys_category, o_p_sys_type.arr, 
                            o_p_sys_no.arr, o_p_sys_suffix.arr, distance);
                    if((nfp = fopen(ns_fname, "w")) == (FILE *) NULL)
                    {
                        sprintf(err_msg, "Fail to open %s", ns_fname);
                        goto force_exit;
                    }
                }

                if ((o_p_east != prev_p_east) || (o_p_north != prev_p_north))
                {
                    prev_p_east = o_p_east;
                    prev_p_north = o_p_north;
                    fprintf(nfp, "\n%51s", "");
                    fprintf(nfp, "No. of near sites printed: %d",
                            nearsite_cnt);

                    line_cnt += 4;
                    if (LINES_PER_PAGE - line_cnt <= 5)
                    {
                        print_nearsite_head(nfp, &page_cnt);
                        page_cnt++;
                        line_cnt = 11;
                    }
                    else
                        fprintf(nfp, "\n\n\n");

                    print_nearsite_subhead(nfp);
                    line_cnt += 5;
                    nearsite_cnt = 0;
                }

                report_open = TRUE;
    
/*
                if (print_nearsite_rpt(nfp, p_grid, &nearsite_cnt, &page_cnt,
                                       &line_cnt, err_msg) == NOT_FOUND)
                {
                    sprintf(err_msg, "%s, press any key to continue", err_msg);
                    disp_err(err_msg);
                    refresh();
                    getch();
                }
*/
                print_nearsite_rpt(nfp, p_grid, &nearsite_cnt, &page_cnt, 
                                   &line_cnt, err_msg);

                show_cursor();
                attrset(A_NORMAL);
                disp_space(23, 0, 80);
                refresh();
                attrset(A_REVERSE);
                
                for (j = NEXT_START_PT; j < item_cnt; j++)
                    init_field(&item[j], &curr_pos);
                beep();
            }
            else
                i = 0;
        }
    }

    attroff(A_BOLD);
    clear();
    endwin();

    if (report_open)
    {
        fprintf(nfp, "\n%51sNo. of near sites printed: %d", "",
                nearsite_cnt);
        fclose(nfp);
        sprintf(cmdline, "lp -dprinter1 %s > /dev/null", ns_fname);
        system(cmdline);
        }

    user_logout();
    exit(0);


sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);

force_exit:
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    getch(); clear(); refresh();
    endwin();
    exit(1);
}


/**************************************************************/
/* Display new entry screen or refresh current screen to user */
/**************************************************************/

disp_entry_scn(item_cnt, curr_cnt, mode)
int    item_cnt;
int    curr_cnt;
int    mode;
{
    char    tmp_str[80];
    register int    i;

    attrset(A_NORMAL);

/****************/
/* modified SEN */
/****************/

   mvaddstr(6,27,                "SYSTEM ID :  -  -       -");
   mvaddstr(8,20,        "CULLING DISTANCE :      (m)");
   mvaddstr(10,13, "CULLING FREQUENCY RANGE :      -      (MHz)");
   mvaddstr(12,23,            "GRID LOCATION : (E)      , (N)");
   mvaddstr(14,17,     "LIST OF SYSTEM TYPE : RESERVED");

    sprintf(tmp_str, "%s",
"F1-Quit   F2-Confirm   F3-Refresh   F4-Clear field   UP-Prev   DOWN/<Tab>-next");
    centre_msg(21, tmp_str, A_BOLD, A_REVERSE);

    if (mode == REFRESH)
    {
        for (i = 0; i < curr_cnt; i++)
        {
            disp_space(item[i].ypos, item[i].xpos, item[i].fld_len);
            mvaddstr(item[i].ypos, item[i].xpos, item[i].curr_str);
        }
        for (i = curr_cnt; i < item_cnt; i++)
            if (item[i].state == NEW)
                mvaddstr(item[i].ypos, item[i].xpos, item[i].init_str);
            else
            {
                disp_space(item[i].ypos, item[i].xpos, item[i].fld_len);
                mvaddstr(item[i].ypos, item[i].xpos, item[i].curr_str);
            }
    }
    else
        for (i = 0; i < item_cnt; i++)
            mvaddstr(item[i].ypos, item[i].xpos, item[i].init_str);

    refresh();

}

/**************************************************************/
/* call 'disp_entry_scn' to refresh current screen to user    */
/**************************************************************/

refresh_screen(item_cnt, curr_cnt)
int item_cnt;
int curr_cnt;
{
    clear();
    disp_heading(prog_id, screen_head, sys_date);
    disp_entry_scn(item_cnt, curr_cnt, REFRESH);
}


/*************************************************/
/* initialise current field to its initial value */
/*************************************************/

init_field(p_item, curr_pos)
FIELD *p_item;
int   *curr_pos;
{
    mvaddstr(p_item->ypos, p_item->xpos, p_item->init_str);
    if (is_sign(p_item))
    {
        p_item->curr_len = *curr_pos = 1;
        p_item->curr_str[1] = '\0';
    }
    else
    {
        p_item->curr_len = *curr_pos = 0;
        p_item->curr_str[0] = '\0';
    }

    if (!disp_only(p_item))
    {
        p_item->state = NEW;
        if (is_float(p_item))
            p_item->has_dot = FALSE;
    }
}


/****************************************************
* check that input grid can be found in TERRAIN or *
* (ieast/inorth arrays)                            *
****************************************************

int    chk_grid(curr_cnt)
int    *curr_cnt;
{
    char   err_msg[80];
    int    i;


    o_grid_east = atoi(item[*curr_cnt - 1].curr_str); 
    o_grid_north = atoi(item[*curr_cnt].curr_str); 

    EXEC SQL
         SELECT COUNT(ROWID)
         INTO   :o_cnt
         FROM   TERRAIN
         WHERE  EAST = :o_grid_east * 10
         AND    NORTH = :o_grid_north * 10;

    if (o_cnt == 0)
    {
        for (i = 0; ieast[i] != DUMMY; i++)
            if (ieast[i] >= o_grid_east)
                break;
        if (ieast[i] == DUMMY)
        {
            (*curr_cnt)--;
            return ERROR;
        }

        for (i = 0; inorth[i] != DUMMY; i++)
            if (inorth[i] >= o_grid_north) 
                break;
        if (inorth[i] == DUMMY)
        {
            (*curr_cnt)--;
            return ERROR;
        }

    }

    return OK;


sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    refresh(); beep();
    sleep(1); clear(); refresh();
    endwin();
    exit(1);
}
*/


/****************************************************/
/* check that input east grid is within HONG KONG   */
/* boundary.                                        */
/****************************************************/

int    chk_east_grid(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{
    int    grid_east = atoi(item[*curr_cnt].curr_str); 

    if ((grid_east < EAST_LOW) || (grid_east > EAST_HIGH))
    {
        sprintf(err_msg, "Invalid east grid");
        return ERROR;
    }

    return OK;
}


/****************************************************/
/* check that input north grid is within HONG KONG  */
/* boundary.                                        */
/****************************************************/

int    chk_north_grid(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{
    int    grid_north = atoi(item[*curr_cnt].curr_str); 

    if ((grid_north < NORTH_LOW) || (grid_north > NORTH_HIGH))
    {
        sprintf(err_msg, "Invalid north grid");
        return ERROR;
    }

    return OK;
}


/************************************************************************/
/* check that input distance covered must be greater than SMALLEST_DIST */
/************************************************************************/

int    chk_distance(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{
/*
    int    i;
*/
    if (item[*curr_cnt].curr_str[0] == '\0')
        strcpy(item[*curr_cnt].curr_str, item[*curr_cnt].init_str);

    if (atoi(item[*curr_cnt].curr_str) < SMALLEST_DIST)
    {
        sprintf(err_msg, "Distance covered too small");
        return ERROR;
    }

    return OK;
}

/*************************************************************/
/* check that upper limit of input frequency must be greater */
/* than the lower limit                                      */ 
/*************************************************************/
int  chk_freq_range(curr_cnt, err_msg)
int  *curr_cnt;
char *err_msg;
{
     int cnt = (*curr_cnt)-1;

     if (item[cnt].curr_str[0] == '\0')
        strcpy(item[cnt].curr_str, item[cnt].init_str);
     
     if (item[cnt+1].curr_str[0] == '\0')
        strcpy(item[cnt+1].curr_str, item[cnt+1].init_str);
      
     if (atoi(item[cnt+1].curr_str) <= atoi(item[cnt].curr_str))
     {
        sprintf(err_msg, "Upper limit of not greater than lower limit");
        return ERROR;
     }
   
     return OK;
}

/**********************************************************/
/* get system description (SYS_DESC) from SYSTEM_DESC for */
/* current system using current system id as search key   */
/**********************************************************/

get_prop_sys_desc(sys_id)
char   *sys_id;
{
    char   err_msg[80];
    

/*
system("echo \"esemns0f 1\" >> /tmp/debug");
sprintf(msg, "echo \"%c %s %s %s\" >> /tmp/debug", 
o_p_sys_category, o_p_sys_type.arr, o_p_sys_no.arr, o_p_sys_suffix.arr );
system(msg);
*/

    /* EXEC SQL 
         SELECT SYS_DESC
         INTO   :o_p_sys_desc
         FROM   SYSTEM_DESC
         WHERE  SYS_CATEGORY = :o_p_sys_category
         AND    SYS_TYPE = :o_p_sys_type
         AND    SYS_NO = :o_p_sys_no
         AND    SYS_SUFFIX = :o_p_sys_suffix; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 5;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "select SYS_DESC into :b0  from SYSTEM_DESC where (((SYS_C\
ATEGORY=:b1 and SYS_TYPE=:b2) and SYS_NO=:b3) and SYS_SUFFIX=:b4)";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )5;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_p_sys_desc;
    sqlstm.sqhstl[0] = (unsigned long )23;
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_p_sys_category;
    sqlstm.sqhstl[1] = (unsigned long )1;
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqhstv[2] = (unsigned char  *)&o_p_sys_type;
    sqlstm.sqhstl[2] = (unsigned long )5;
    sqlstm.sqhsts[2] = (         int  )0;
    sqlstm.sqindv[2] = (         short *)0;
    sqlstm.sqinds[2] = (         int  )0;
    sqlstm.sqharm[2] = (unsigned long )0;
    sqlstm.sqadto[2] = (unsigned short )0;
    sqlstm.sqtdso[2] = (unsigned short )0;
    sqlstm.sqhstv[3] = (unsigned char  *)&o_p_sys_no;
    sqlstm.sqhstl[3] = (unsigned long )10;
    sqlstm.sqhsts[3] = (         int  )0;
    sqlstm.sqindv[3] = (         short *)0;
    sqlstm.sqinds[3] = (         int  )0;
    sqlstm.sqharm[3] = (unsigned long )0;
    sqlstm.sqadto[3] = (unsigned short )0;
    sqlstm.sqtdso[3] = (unsigned short )0;
    sqlstm.sqhstv[4] = (unsigned char  *)&o_p_sys_suffix;
    sqlstm.sqhstl[4] = (unsigned long )6;
    sqlstm.sqhsts[4] = (         int  )0;
    sqlstm.sqindv[4] = (         short *)0;
    sqlstm.sqinds[4] = (         int  )0;
    sqlstm.sqharm[4] = (unsigned long )0;
    sqlstm.sqadto[4] = (unsigned short )0;
    sqlstm.sqtdso[4] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    if (sqlca.sqlcode == NOT_FOUND)
        strcpy(prop_sys_desc, sys_id);
    else
    {
        o_p_sys_desc.arr[o_p_sys_desc.len] = '\0';
/*        strcpy(prop_sys_desc, o_p_sys_desc.arr); */
        strcpy(prop_sys_desc, (char *)o_p_sys_desc.arr);
    }

    pad_space(prop_sys_desc, 20);


    /*********************************************************/
    /* if the input system is not a mobile telephone system, */
    /* determine the station type if this system exists in   */
    /* the database                                          */
    /*********************************************************/

/*    if ((o_p_sys_category != 'P') 
    || (strcmp(o_p_sys_type.arr, "06") && strcmp(o_p_sys_type.arr, "40"))) */
    if ((o_p_sys_category != 'P') 
    || (strcmp((char *)o_p_sys_type.arr, "06") && strcmp((char *)o_p_sys_type.arr, "40")))

/*
    if ((o_p_sys_category != 'T') || (strcmp(o_p_sys_type.arr, "00")))
*/
    {
        char   s[5];

/*
system("echo \"esemns0f 2\" >> /tmp/debug");
sprintf(msg, "echo \"%c %s %s %s %d %d\" >> /tmp/debug", 
o_p_sys_category, o_p_sys_type.arr, o_p_sys_no.arr, o_p_sys_suffix.arr,
o_p_east, o_p_north );
system(msg);
*/

        /* EXEC SQL 
             SELECT STATION_TYPE
             INTO   :o_p_stn_type
             FROM   STATION
             WHERE  SYS_CATEGORY = :o_p_sys_category
             AND    SYS_TYPE = :o_p_sys_type
             AND    SYS_NO = :o_p_sys_no
             AND    SYS_SUFFIX = :o_p_sys_suffix
             AND    EAST = :o_p_east
             AND    NORTH = :o_p_north; */ 

{
        struct sqlexd sqlstm;
        sqlstm.sqlvsn = 13;
        sqlstm.arrsiz = 7;
        sqlstm.sqladtp = &sqladt;
        sqlstm.sqltdsp = &sqltds;
        sqlstm.stmt = "select STATION_TYPE into :b0  from STATION where ((((\
(SYS_CATEGORY=:b1 and SYS_TYPE=:b2) and SYS_NO=:b3) and SYS_SUFFIX=:b4) and EA\
ST=:b5) and NORTH=:b6)";
        sqlstm.iters = (unsigned int  )1;
        sqlstm.offset = (unsigned int  )40;
        sqlstm.selerr = (unsigned short)1;
        sqlstm.sqlpfmem = (unsigned int  )0;
        sqlstm.cud = sqlcud0;
        sqlstm.sqlest = (unsigned char  *)&sqlca;
        sqlstm.sqlety = (unsigned short)4352;
        sqlstm.occurs = (unsigned int  )0;
        sqlstm.sqhstv[0] = (unsigned char  *)&o_p_stn_type;
        sqlstm.sqhstl[0] = (unsigned long )1;
        sqlstm.sqhsts[0] = (         int  )0;
        sqlstm.sqindv[0] = (         short *)0;
        sqlstm.sqinds[0] = (         int  )0;
        sqlstm.sqharm[0] = (unsigned long )0;
        sqlstm.sqadto[0] = (unsigned short )0;
        sqlstm.sqtdso[0] = (unsigned short )0;
        sqlstm.sqhstv[1] = (unsigned char  *)&o_p_sys_category;
        sqlstm.sqhstl[1] = (unsigned long )1;
        sqlstm.sqhsts[1] = (         int  )0;
        sqlstm.sqindv[1] = (         short *)0;
        sqlstm.sqinds[1] = (         int  )0;
        sqlstm.sqharm[1] = (unsigned long )0;
        sqlstm.sqadto[1] = (unsigned short )0;
        sqlstm.sqtdso[1] = (unsigned short )0;
        sqlstm.sqhstv[2] = (unsigned char  *)&o_p_sys_type;
        sqlstm.sqhstl[2] = (unsigned long )5;
        sqlstm.sqhsts[2] = (         int  )0;
        sqlstm.sqindv[2] = (         short *)0;
        sqlstm.sqinds[2] = (         int  )0;
        sqlstm.sqharm[2] = (unsigned long )0;
        sqlstm.sqadto[2] = (unsigned short )0;
        sqlstm.sqtdso[2] = (unsigned short )0;
        sqlstm.sqhstv[3] = (unsigned char  *)&o_p_sys_no;
        sqlstm.sqhstl[3] = (unsigned long )10;
        sqlstm.sqhsts[3] = (         int  )0;
        sqlstm.sqindv[3] = (         short *)0;
        sqlstm.sqinds[3] = (         int  )0;
        sqlstm.sqharm[3] = (unsigned long )0;
        sqlstm.sqadto[3] = (unsigned short )0;
        sqlstm.sqtdso[3] = (unsigned short )0;
        sqlstm.sqhstv[4] = (unsigned char  *)&o_p_sys_suffix;
        sqlstm.sqhstl[4] = (unsigned long )6;
        sqlstm.sqhsts[4] = (         int  )0;
        sqlstm.sqindv[4] = (         short *)0;
        sqlstm.sqinds[4] = (         int  )0;
        sqlstm.sqharm[4] = (unsigned long )0;
        sqlstm.sqadto[4] = (unsigned short )0;
        sqlstm.sqtdso[4] = (unsigned short )0;
        sqlstm.sqhstv[5] = (unsigned char  *)&o_p_east;
        sqlstm.sqhstl[5] = (unsigned long )sizeof(int);
        sqlstm.sqhsts[5] = (         int  )0;
        sqlstm.sqindv[5] = (         short *)0;
        sqlstm.sqinds[5] = (         int  )0;
        sqlstm.sqharm[5] = (unsigned long )0;
        sqlstm.sqadto[5] = (unsigned short )0;
        sqlstm.sqtdso[5] = (unsigned short )0;
        sqlstm.sqhstv[6] = (unsigned char  *)&o_p_north;
        sqlstm.sqhstl[6] = (unsigned long )sizeof(int);
        sqlstm.sqhsts[6] = (         int  )0;
        sqlstm.sqindv[6] = (         short *)0;
        sqlstm.sqinds[6] = (         int  )0;
        sqlstm.sqharm[6] = (unsigned long )0;
        sqlstm.sqadto[6] = (unsigned short )0;
        sqlstm.sqtdso[6] = (unsigned short )0;
        sqlstm.sqphsv = sqlstm.sqhstv;
        sqlstm.sqphsl = sqlstm.sqhstl;
        sqlstm.sqphss = sqlstm.sqhsts;
        sqlstm.sqpind = sqlstm.sqindv;
        sqlstm.sqpins = sqlstm.sqinds;
        sqlstm.sqparm = sqlstm.sqharm;
        sqlstm.sqparc = sqlstm.sqharc;
        sqlstm.sqpadto = sqlstm.sqadto;
        sqlstm.sqptdso = sqlstm.sqtdso;
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



        if (sqlca.sqlcode == FOUND)
        {
            sprintf(s, " (%c)", o_p_stn_type);
            strcat(prop_sys_desc, s);
        }

    }

/* commented out by Chen Yung
return;
*/
/* changed by Chen Yung */
/* original: */
/*    return; */
    return OK;


sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    refresh(); beep();
    sleep(1); clear(); refresh();
    endwin();
    exit(1);
}

    
/**********************************************************************/
/*  print near-site report                                            */
/**********************************************************************/

print_nearsite_rpt(nfp, p_grid, nearsite_cnt, page_cnt, line_cnt, err_msg)
FILE   *nfp;
float  p_grid[];
int    *nearsite_cnt;
int    *page_cnt;
int    *line_cnt;
char   *err_msg;
{
    char   prt_sys_desc[30];
    char   location[140], location_1[80], location_2[60];
    char   str[65];
    float  grid[2], e_grid[2], dist_in_km; 
    float  dist_p_e;
    char s[132];

    dist_in_km = (float)distance / 1000;
    o_south = o_p_north - cull_grid;
    o_north = o_p_north + cull_grid;
    o_west  = o_p_east - cull_grid;
    o_east  = o_p_east + cull_grid;
/*
sprintf(s,"%d %d-%d %d-%d %d-%d",cull_grid,o_p_north, o_p_east, o_south, o_north, o_west, o_east);
mvaddstr(19,0,s);refresh();
*/
    /* EXEC SQL DECLARE C00 CURSOR FOR
         SELECT SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO,
                EAST, NORTH, STATION_TYPE, SUBDISTRICT, NVL(E_CALLSIGN, ' '),
                DECODE(ROOM, '', '', 'RM-'||ROOM),
                DECODE(BLOCK, '', '','BK-'||BLOCK),
                DECODE(FLOOR, '', '',FLOOR||'/F'),
                NVL(BLDG, ' '), STREET_NO, STREET_NAME, 
                NVL(TO_CHAR(CANCEL_DATE), '-')
         FROM   STATION
         WHERE  (NORTH BETWEEN :o_south AND :o_north)
         AND    (EAST BETWEEN :o_west AND :o_east)
         ORDER BY SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO,
                  STATION_FLAG, RECORD_TYPE; */ 


    /* EXEC SQL OPEN C00; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 7;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = sq0003;
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )83;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqcmod = (unsigned int )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_south;
    sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_north;
    sqlstm.sqhstl[1] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqhstv[2] = (unsigned char  *)&o_west;
    sqlstm.sqhstl[2] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[2] = (         int  )0;
    sqlstm.sqindv[2] = (         short *)0;
    sqlstm.sqinds[2] = (         int  )0;
    sqlstm.sqharm[2] = (unsigned long )0;
    sqlstm.sqadto[2] = (unsigned short )0;
    sqlstm.sqtdso[2] = (unsigned short )0;
    sqlstm.sqhstv[3] = (unsigned char  *)&o_east;
    sqlstm.sqhstl[3] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[3] = (         int  )0;
    sqlstm.sqindv[3] = (         short *)0;
    sqlstm.sqinds[3] = (         int  )0;
    sqlstm.sqharm[3] = (unsigned long )0;
    sqlstm.sqadto[3] = (unsigned short )0;
    sqlstm.sqtdso[3] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    /* EXEC SQL
         FETCH C00
         INTO  :o_sys_category, :o_sys_type, :o_sys_no, :o_sys_suffix,
               :o_base_no, :o_e_east, :o_e_north, :o_stn_type, :o_sub_district,
               :o_cellsite, :o_room, :o_block, :o_floor, :o_bldg, :o_street_no,
               :o_street_name, :o_cancel_date; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 17;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )114;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqfoff = (         int )0;
    sqlstm.sqfmod = (unsigned int )2;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
    sqlstm.sqhstl[0] = (unsigned long )1;
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
    sqlstm.sqhstl[1] = (unsigned long )5;
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
    sqlstm.sqhstl[2] = (unsigned long )10;
    sqlstm.sqhsts[2] = (         int  )0;
    sqlstm.sqindv[2] = (         short *)0;
    sqlstm.sqinds[2] = (         int  )0;
    sqlstm.sqharm[2] = (unsigned long )0;
    sqlstm.sqadto[2] = (unsigned short )0;
    sqlstm.sqtdso[2] = (unsigned short )0;
    sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
    sqlstm.sqhstl[3] = (unsigned long )6;
    sqlstm.sqhsts[3] = (         int  )0;
    sqlstm.sqindv[3] = (         short *)0;
    sqlstm.sqinds[3] = (         int  )0;
    sqlstm.sqharm[3] = (unsigned long )0;
    sqlstm.sqadto[3] = (unsigned short )0;
    sqlstm.sqtdso[3] = (unsigned short )0;
    sqlstm.sqhstv[4] = (unsigned char  *)&o_base_no;
    sqlstm.sqhstl[4] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[4] = (         int  )0;
    sqlstm.sqindv[4] = (         short *)0;
    sqlstm.sqinds[4] = (         int  )0;
    sqlstm.sqharm[4] = (unsigned long )0;
    sqlstm.sqadto[4] = (unsigned short )0;
    sqlstm.sqtdso[4] = (unsigned short )0;
    sqlstm.sqhstv[5] = (unsigned char  *)&o_e_east;
    sqlstm.sqhstl[5] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[5] = (         int  )0;
    sqlstm.sqindv[5] = (         short *)0;
    sqlstm.sqinds[5] = (         int  )0;
    sqlstm.sqharm[5] = (unsigned long )0;
    sqlstm.sqadto[5] = (unsigned short )0;
    sqlstm.sqtdso[5] = (unsigned short )0;
    sqlstm.sqhstv[6] = (unsigned char  *)&o_e_north;
    sqlstm.sqhstl[6] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[6] = (         int  )0;
    sqlstm.sqindv[6] = (         short *)0;
    sqlstm.sqinds[6] = (         int  )0;
    sqlstm.sqharm[6] = (unsigned long )0;
    sqlstm.sqadto[6] = (unsigned short )0;
    sqlstm.sqtdso[6] = (unsigned short )0;
    sqlstm.sqhstv[7] = (unsigned char  *)&o_stn_type;
    sqlstm.sqhstl[7] = (unsigned long )1;
    sqlstm.sqhsts[7] = (         int  )0;
    sqlstm.sqindv[7] = (         short *)0;
    sqlstm.sqinds[7] = (         int  )0;
    sqlstm.sqharm[7] = (unsigned long )0;
    sqlstm.sqadto[7] = (unsigned short )0;
    sqlstm.sqtdso[7] = (unsigned short )0;
    sqlstm.sqhstv[8] = (unsigned char  *)&o_sub_district;
    sqlstm.sqhstl[8] = (unsigned long )6;
    sqlstm.sqhsts[8] = (         int  )0;
    sqlstm.sqindv[8] = (         short *)0;
    sqlstm.sqinds[8] = (         int  )0;
    sqlstm.sqharm[8] = (unsigned long )0;
    sqlstm.sqadto[8] = (unsigned short )0;
    sqlstm.sqtdso[8] = (unsigned short )0;
    sqlstm.sqhstv[9] = (unsigned char  *)&o_cellsite;
    sqlstm.sqhstl[9] = (unsigned long )28;
    sqlstm.sqhsts[9] = (         int  )0;
    sqlstm.sqindv[9] = (         short *)0;
    sqlstm.sqinds[9] = (         int  )0;
    sqlstm.sqharm[9] = (unsigned long )0;
    sqlstm.sqadto[9] = (unsigned short )0;
    sqlstm.sqtdso[9] = (unsigned short )0;
    sqlstm.sqhstv[10] = (unsigned char  *)&o_room;
    sqlstm.sqhstl[10] = (unsigned long )11;
    sqlstm.sqhsts[10] = (         int  )0;
    sqlstm.sqindv[10] = (         short *)0;
    sqlstm.sqinds[10] = (         int  )0;
    sqlstm.sqharm[10] = (unsigned long )0;
    sqlstm.sqadto[10] = (unsigned short )0;
    sqlstm.sqtdso[10] = (unsigned short )0;
    sqlstm.sqhstv[11] = (unsigned char  *)&o_block;
    sqlstm.sqhstl[11] = (unsigned long )10;
    sqlstm.sqhsts[11] = (         int  )0;
    sqlstm.sqindv[11] = (         short *)0;
    sqlstm.sqinds[11] = (         int  )0;
    sqlstm.sqharm[11] = (unsigned long )0;
    sqlstm.sqadto[11] = (unsigned short )0;
    sqlstm.sqtdso[11] = (unsigned short )0;
    sqlstm.sqhstv[12] = (unsigned char  *)&o_floor;
    sqlstm.sqhstl[12] = (unsigned long )8;
    sqlstm.sqhsts[12] = (         int  )0;
    sqlstm.sqindv[12] = (         short *)0;
    sqlstm.sqinds[12] = (         int  )0;
    sqlstm.sqharm[12] = (unsigned long )0;
    sqlstm.sqadto[12] = (unsigned short )0;
    sqlstm.sqtdso[12] = (unsigned short )0;
    sqlstm.sqhstv[13] = (unsigned char  *)&o_bldg;
    sqlstm.sqhstl[13] = (unsigned long )23;
    sqlstm.sqhsts[13] = (         int  )0;
    sqlstm.sqindv[13] = (         short *)0;
    sqlstm.sqinds[13] = (         int  )0;
    sqlstm.sqharm[13] = (unsigned long )0;
    sqlstm.sqadto[13] = (unsigned short )0;
    sqlstm.sqtdso[13] = (unsigned short )0;
    sqlstm.sqhstv[14] = (unsigned char  *)&o_street_no;
    sqlstm.sqhstl[14] = (unsigned long )13;
    sqlstm.sqhsts[14] = (         int  )0;
    sqlstm.sqindv[14] = (         short *)0;
    sqlstm.sqinds[14] = (         int  )0;
    sqlstm.sqharm[14] = (unsigned long )0;
    sqlstm.sqadto[14] = (unsigned short )0;
    sqlstm.sqtdso[14] = (unsigned short )0;
    sqlstm.sqhstv[15] = (unsigned char  *)&o_street_name;
    sqlstm.sqhstl[15] = (unsigned long )33;
    sqlstm.sqhsts[15] = (         int  )0;
    sqlstm.sqindv[15] = (         short *)0;
    sqlstm.sqinds[15] = (         int  )0;
    sqlstm.sqharm[15] = (unsigned long )0;
    sqlstm.sqadto[15] = (unsigned short )0;
    sqlstm.sqtdso[15] = (unsigned short )0;
    sqlstm.sqhstv[16] = (unsigned char  *)&o_cancel_date;
    sqlstm.sqhstl[16] = (unsigned long )12;
    sqlstm.sqhsts[16] = (         int  )0;
    sqlstm.sqindv[16] = (         short *)0;
    sqlstm.sqinds[16] = (         int  )0;
    sqlstm.sqharm[16] = (unsigned long )0;
    sqlstm.sqadto[16] = (unsigned short )0;
    sqlstm.sqtdso[16] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    if (sqlca.sqlcode == NOT_FOUND)
    {
        sprintf(err_msg, "No stations found");
        /* EXEC SQL CLOSE C00; */ 

{
        struct sqlexd sqlstm;
        sqlstm.sqlvsn = 13;
        sqlstm.arrsiz = 17;
        sqlstm.sqladtp = &sqladt;
        sqlstm.sqltdsp = &sqltds;
        sqlstm.iters = (unsigned int  )1;
        sqlstm.offset = (unsigned int  )197;
        sqlstm.cud = sqlcud0;
        sqlstm.sqlest = (unsigned char  *)&sqlca;
        sqlstm.sqlety = (unsigned short)4352;
        sqlstm.occurs = (unsigned int  )0;
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


        return NOT_FOUND;
    }

    grid[0] = p_grid[0] / 10;
    grid[1] = p_grid[1] / 10;

    if ((*page_cnt) == 1)
    {
        print_nearsite_head(nfp, page_cnt);
        print_nearsite_subhead(nfp);
        (*page_cnt)++;
        (*line_cnt) = 16;
    }

    for (; ;)
    {
        memset(location, '\0', sizeof(location));
        memset(location_1, '\0', sizeof(location_1));
        memset(location_2, '\0', sizeof(location_2));
        memset(str, '\0', sizeof(str));

        /******************************************************/
        /*  Only non-cancelled stations (CANCEL_DATE is NULL) */
        /*  are considered                                    */
        /******************************************************/
/*
        if ((o_e_east > 0) || (o_e_north > 0))
*/
        if (o_cancel_date.arr[0] == '-')
        {
            e_grid[0] = (float)o_e_east / 10; 
            e_grid[1] = (float)o_e_north / 10; 

            dist_p_e = cal_dist(grid, e_grid);
/*
sprintf(s,"%f-%f %f-%f %f km: %f",p_grid[0],p_grid[1],e_grid[0],e_grid[1],dist_p_e,dist_in_km);
mvaddstr(22,0,s); refresh(); getch();
*/
            if (dist_p_e <= dist_in_km)
            {
/*
mvaddstr(23,0,"FIND");
*/
                /********************************************************/
                /* determine whether current system has channel(s) fall */
                /* between o_freq_lo and o_freq_hi                      */
                /********************************************************/
                /* EXEC SQL
                     SELECT COUNT(ROWID)
                     INTO   :o_cnt
                     FROM   BASE_CH
                     WHERE  SYS_CATEGORY = :o_sys_category
                     AND    SYS_TYPE = :o_sys_type
                     AND    SYS_NO = :o_sys_no
                     AND    SYS_SUFFIX = :o_sys_suffix
                     AND    BASE_NO = :o_base_no
                     AND    TX_FREQ BETWEEN :o_freq_lo and :o_freq_hi; */ 

{
                struct sqlexd sqlstm;
                sqlstm.sqlvsn = 13;
                sqlstm.arrsiz = 17;
                sqlstm.sqladtp = &sqladt;
                sqlstm.sqltdsp = &sqltds;
                sqlstm.stmt = "select count(ROWID) into :b0  from BASE_CH wh\
ere (((((SYS_CATEGORY=:b1 and SYS_TYPE=:b2) and SYS_NO=:b3) and SYS_SUFFIX=:b4\
) and BASE_NO=:b5) and TX_FREQ between :b6 and :b7)";
                sqlstm.iters = (unsigned int  )1;
                sqlstm.offset = (unsigned int  )212;
                sqlstm.selerr = (unsigned short)1;
                sqlstm.sqlpfmem = (unsigned int  )0;
                sqlstm.cud = sqlcud0;
                sqlstm.sqlest = (unsigned char  *)&sqlca;
                sqlstm.sqlety = (unsigned short)4352;
                sqlstm.occurs = (unsigned int  )0;
                sqlstm.sqhstv[0] = (unsigned char  *)&o_cnt;
                sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
                sqlstm.sqhsts[0] = (         int  )0;
                sqlstm.sqindv[0] = (         short *)0;
                sqlstm.sqinds[0] = (         int  )0;
                sqlstm.sqharm[0] = (unsigned long )0;
                sqlstm.sqadto[0] = (unsigned short )0;
                sqlstm.sqtdso[0] = (unsigned short )0;
                sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_category;
                sqlstm.sqhstl[1] = (unsigned long )1;
                sqlstm.sqhsts[1] = (         int  )0;
                sqlstm.sqindv[1] = (         short *)0;
                sqlstm.sqinds[1] = (         int  )0;
                sqlstm.sqharm[1] = (unsigned long )0;
                sqlstm.sqadto[1] = (unsigned short )0;
                sqlstm.sqtdso[1] = (unsigned short )0;
                sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_type;
                sqlstm.sqhstl[2] = (unsigned long )5;
                sqlstm.sqhsts[2] = (         int  )0;
                sqlstm.sqindv[2] = (         short *)0;
                sqlstm.sqinds[2] = (         int  )0;
                sqlstm.sqharm[2] = (unsigned long )0;
                sqlstm.sqadto[2] = (unsigned short )0;
                sqlstm.sqtdso[2] = (unsigned short )0;
                sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_no;
                sqlstm.sqhstl[3] = (unsigned long )10;
                sqlstm.sqhsts[3] = (         int  )0;
                sqlstm.sqindv[3] = (         short *)0;
                sqlstm.sqinds[3] = (         int  )0;
                sqlstm.sqharm[3] = (unsigned long )0;
                sqlstm.sqadto[3] = (unsigned short )0;
                sqlstm.sqtdso[3] = (unsigned short )0;
                sqlstm.sqhstv[4] = (unsigned char  *)&o_sys_suffix;
                sqlstm.sqhstl[4] = (unsigned long )6;
                sqlstm.sqhsts[4] = (         int  )0;
                sqlstm.sqindv[4] = (         short *)0;
                sqlstm.sqinds[4] = (         int  )0;
                sqlstm.sqharm[4] = (unsigned long )0;
                sqlstm.sqadto[4] = (unsigned short )0;
                sqlstm.sqtdso[4] = (unsigned short )0;
                sqlstm.sqhstv[5] = (unsigned char  *)&o_base_no;
                sqlstm.sqhstl[5] = (unsigned long )sizeof(int);
                sqlstm.sqhsts[5] = (         int  )0;
                sqlstm.sqindv[5] = (         short *)0;
                sqlstm.sqinds[5] = (         int  )0;
                sqlstm.sqharm[5] = (unsigned long )0;
                sqlstm.sqadto[5] = (unsigned short )0;
                sqlstm.sqtdso[5] = (unsigned short )0;
                sqlstm.sqhstv[6] = (unsigned char  *)&o_freq_lo;
                sqlstm.sqhstl[6] = (unsigned long )sizeof(int);
                sqlstm.sqhsts[6] = (         int  )0;
                sqlstm.sqindv[6] = (         short *)0;
                sqlstm.sqinds[6] = (         int  )0;
                sqlstm.sqharm[6] = (unsigned long )0;
                sqlstm.sqadto[6] = (unsigned short )0;
                sqlstm.sqtdso[6] = (unsigned short )0;
                sqlstm.sqhstv[7] = (unsigned char  *)&o_freq_hi;
                sqlstm.sqhstl[7] = (unsigned long )sizeof(int);
                sqlstm.sqhsts[7] = (         int  )0;
                sqlstm.sqindv[7] = (         short *)0;
                sqlstm.sqinds[7] = (         int  )0;
                sqlstm.sqharm[7] = (unsigned long )0;
                sqlstm.sqadto[7] = (unsigned short )0;
                sqlstm.sqtdso[7] = (unsigned short )0;
                sqlstm.sqphsv = sqlstm.sqhstv;
                sqlstm.sqphsl = sqlstm.sqhstl;
                sqlstm.sqphss = sqlstm.sqhsts;
                sqlstm.sqpind = sqlstm.sqindv;
                sqlstm.sqpins = sqlstm.sqinds;
                sqlstm.sqparm = sqlstm.sqharm;
                sqlstm.sqparc = sqlstm.sqharc;
                sqlstm.sqpadto = sqlstm.sqadto;
                sqlstm.sqptdso = sqlstm.sqtdso;
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



                if (o_cnt > 0)
                {
                    int    len;

                    o_sys_type.arr[o_sys_type.len] = '\0';
                    o_sys_no.arr[o_sys_no.len] = '\0';
                    o_sys_suffix.arr[o_sys_suffix.len] = '\0';
/*
system("echo \"esemns0f 3\" >> /tmp/debug");
sprintf(msg, "echo \"%c %s %s %s\" >> /tmp/debug", 
o_sys_category, o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr );
system(msg);
*/

                    /* EXEC SQL 
                         SELECT SYS_DESC
                         INTO   :o_sys_desc
                         FROM   SYSTEM_DESC
                         WHERE  SYS_CATEGORY = :o_sys_category
                         AND    SYS_TYPE = :o_sys_type
                         AND    SYS_NO = :o_sys_no
                         AND    SYS_SUFFIX = :o_sys_suffix; */ 

{
                    struct sqlexd sqlstm;
                    sqlstm.sqlvsn = 13;
                    sqlstm.arrsiz = 17;
                    sqlstm.sqladtp = &sqladt;
                    sqlstm.sqltdsp = &sqltds;
                    sqlstm.stmt = "select SYS_DESC into :b0  from SYSTEM_DES\
C where (((SYS_CATEGORY=:b1 and SYS_TYPE=:b2) and SYS_NO=:b3) and SYS_SUFFIX=:\
b4)";
                    sqlstm.iters = (unsigned int  )1;
                    sqlstm.offset = (unsigned int  )259;
                    sqlstm.selerr = (unsigned short)1;
                    sqlstm.sqlpfmem = (unsigned int  )0;
                    sqlstm.cud = sqlcud0;
                    sqlstm.sqlest = (unsigned char  *)&sqlca;
                    sqlstm.sqlety = (unsigned short)4352;
                    sqlstm.occurs = (unsigned int  )0;
                    sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_desc;
                    sqlstm.sqhstl[0] = (unsigned long )23;
                    sqlstm.sqhsts[0] = (         int  )0;
                    sqlstm.sqindv[0] = (         short *)0;
                    sqlstm.sqinds[0] = (         int  )0;
                    sqlstm.sqharm[0] = (unsigned long )0;
                    sqlstm.sqadto[0] = (unsigned short )0;
                    sqlstm.sqtdso[0] = (unsigned short )0;
                    sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_category;
                    sqlstm.sqhstl[1] = (unsigned long )1;
                    sqlstm.sqhsts[1] = (         int  )0;
                    sqlstm.sqindv[1] = (         short *)0;
                    sqlstm.sqinds[1] = (         int  )0;
                    sqlstm.sqharm[1] = (unsigned long )0;
                    sqlstm.sqadto[1] = (unsigned short )0;
                    sqlstm.sqtdso[1] = (unsigned short )0;
                    sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_type;
                    sqlstm.sqhstl[2] = (unsigned long )5;
                    sqlstm.sqhsts[2] = (         int  )0;
                    sqlstm.sqindv[2] = (         short *)0;
                    sqlstm.sqinds[2] = (         int  )0;
                    sqlstm.sqharm[2] = (unsigned long )0;
                    sqlstm.sqadto[2] = (unsigned short )0;
                    sqlstm.sqtdso[2] = (unsigned short )0;
                    sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_no;
                    sqlstm.sqhstl[3] = (unsigned long )10;
                    sqlstm.sqhsts[3] = (         int  )0;
                    sqlstm.sqindv[3] = (         short *)0;
                    sqlstm.sqinds[3] = (         int  )0;
                    sqlstm.sqharm[3] = (unsigned long )0;
                    sqlstm.sqadto[3] = (unsigned short )0;
                    sqlstm.sqtdso[3] = (unsigned short )0;
                    sqlstm.sqhstv[4] = (unsigned char  *)&o_sys_suffix;
                    sqlstm.sqhstl[4] = (unsigned long )6;
                    sqlstm.sqhsts[4] = (         int  )0;
                    sqlstm.sqindv[4] = (         short *)0;
                    sqlstm.sqinds[4] = (         int  )0;
                    sqlstm.sqharm[4] = (unsigned long )0;
                    sqlstm.sqadto[4] = (unsigned short )0;
                    sqlstm.sqtdso[4] = (unsigned short )0;
                    sqlstm.sqphsv = sqlstm.sqhstv;
                    sqlstm.sqphsl = sqlstm.sqhstl;
                    sqlstm.sqphss = sqlstm.sqhsts;
                    sqlstm.sqpind = sqlstm.sqindv;
                    sqlstm.sqpins = sqlstm.sqinds;
                    sqlstm.sqparm = sqlstm.sqharm;
                    sqlstm.sqparc = sqlstm.sqharc;
                    sqlstm.sqpadto = sqlstm.sqadto;
                    sqlstm.sqptdso = sqlstm.sqtdso;
                    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



                    if (sqlca.sqlcode == NOT_FOUND)
                    {
                        sprintf(prt_sys_desc, "%c%s%s-%s", o_sys_category, 
                                o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr);
                    }
                    else
                    {
                        o_sys_desc.arr[o_sys_desc.len] = '\0';
/*                        strcpy(prt_sys_desc, o_sys_desc.arr); */
                        strcpy(prt_sys_desc, (char *)o_sys_desc.arr);
                    }

                    if (((*line_cnt) == 0) || ((*line_cnt) >= LINES_PER_PAGE))
                    {
                        print_nearsite_head(nfp, page_cnt);
                        print_nearsite_subhead(nfp);
                        (*page_cnt)++;
                        (*line_cnt) = 16;
                    }

                    pad_space(prt_sys_desc, 20);
                    o_sub_district.arr[o_sub_district.len] = '\0';
                    o_cellsite.arr[o_cellsite.len] = '\0';
                    o_room.arr[o_room.len] = '\0';
                    o_block.arr[o_block.len] = '\0';
                    o_floor.arr[o_floor.len] = '\0';
                    o_bldg.arr[o_bldg.len] = '\0';
                    o_street_no.arr[o_street_no.len] = '\0';
                    o_street_name.arr[o_street_name.len] = '\0';
                    if (o_cellsite.arr[0] == ' ')
                        if (o_bldg.arr[0] == ' ')
                            sprintf(location, "%s %s %s %s %s", 
                                    o_room.arr, o_block.arr, o_floor.arr, 
                                    o_street_no.arr, o_street_name.arr);
                        else
                            sprintf(location, "%s %s %s %s %s %s", 
                                    o_bldg.arr, o_room.arr, o_block.arr, 
                                    o_floor.arr, o_street_no.arr, 
                                    o_street_name.arr);
                    else
                        if (o_bldg.arr[0] == ' ')
                            sprintf(location, "%s %s %s %s %s %s", 
                                    o_cellsite.arr, o_room.arr, 
                                    o_block.arr, o_floor.arr, o_street_no.arr, 
                                    o_street_name.arr);
                        else
                            sprintf(location, "%s %s %s %s %s %s %s", 
                                    o_cellsite.arr, o_bldg.arr, o_room.arr, 
                                    o_block.arr, o_floor.arr, o_street_no.arr, 
                                    o_street_name.arr);

                    strip_blank(location, LEADING);
                    if ((len = strlen(location)) > 78)
                    {
                        int   pos0, pos;
                        
                        strncpy(str, location, 77);
                        pos0 = chr_in_str(str, BLANK, TRAIL);
                        pos = strlen(str) - pos0;
                        strncpy(location_1, location, pos);
                        strcpy(location_2, location+pos+1);
                        strip_blank(location_2, LEADING);
/*
clear_msg(19); sprintf(s,"%s", location); mvaddstr(19,0,s);
sprintf(s,"%d %d %s       ", len, strlen(str), str); mvaddstr(22,0,s);
sprintf(s,"pos0 pos: %d %d    ", pos0,pos);mvaddstr(23,0,s);refresh();getch();
*/
                    }
                    else
                        location_2[0] = '\0';

                    /**************************************************/
                    /* if sys_category+sys_type is "P06", ie., mobile */
                    /* telephone then do not print station type for   */
                    /* this system                                    */
                    /**************************************************/
/*
                    if ((o_sys_category == 'T') && (!strcmp(o_sys_type.arr, "00")))
*/
/*                    if (o_sys_category == 'P') 
                        if ((!strcmp(o_sys_type.arr, "06")) 
                        ||  (!strcmp(o_sys_type.arr, "40"))) */

                    if (o_sys_category == 'P') 
                        if ((!strcmp((char *)o_sys_type.arr, "06")) 
                        ||  (!strcmp((char *)o_sys_type.arr, "40")))
                        {
                            fprintf(nfp, "%s     %5d %5d  %-3s   %4d   ",
                                    prt_sys_desc, o_e_east, o_e_north, 
                                    o_sub_district.arr, (int)(dist_p_e*1000));
                            if (location_2[0] == '\0')
                                fprintf(nfp, "  %s\n", location);
                            else
                            {
                                fprintf(nfp, "  %s\n", location_1);
                                fprintf(nfp, "%51s%s\n", "", location_2);
                                (*line_cnt)++;
                            }   
                        }
                        else
                        {
                            char   s[5];

                            sprintf(s, " (%c)", o_stn_type);
                            strcat(prt_sys_desc, s);
                            fprintf(nfp, "%s %5d %5d  %-3s   %4d   ",
                                    prt_sys_desc, o_e_east, o_e_north, 
                                    o_sub_district.arr, (int)(dist_p_e*1000));
                            if (location_2[0] == '\0')
                                fprintf(nfp, "  %s\n", location);
                            else
                            {
                                fprintf(nfp, "  %s\n", location_1);
                                fprintf(nfp, "%51s%s\n", "", location_2);
                                (*line_cnt)++;
                            }   
                        }
                    else
                    {
                        char   s[5];
 
                        sprintf(s, " (%c)", o_stn_type);
                        strcat(prt_sys_desc, s);
                        fprintf(nfp, "%s %5d %5d  %-3s   %4d   ",
                                prt_sys_desc, o_e_east, o_e_north, 
                                o_sub_district.arr, (int)(dist_p_e*1000));
                        if (location_2[0] == '\0')
                            fprintf(nfp, "  %s\n", location);
                        else
                        {
                            fprintf(nfp, "  %s\n", location_1);
                            fprintf(nfp, "%51s%s\n", "", location_2);
                            (*line_cnt)++;
                        }   
                    }

                    (*line_cnt)++;
                    (*nearsite_cnt)++;
                }
            }
        }

        /* EXEC SQL
             FETCH C00
             INTO  :o_sys_category, :o_sys_type, :o_sys_no, :o_sys_suffix,
                   :o_base_no, :o_e_east, :o_e_north, :o_stn_type, 
                   :o_sub_district, :o_cellsite, :o_room, :o_block, :o_floor,
                   :o_bldg, :o_street_no, :o_street_name, :o_cancel_date; */ 

{
        struct sqlexd sqlstm;
        sqlstm.sqlvsn = 13;
        sqlstm.arrsiz = 17;
        sqlstm.sqladtp = &sqladt;
        sqlstm.sqltdsp = &sqltds;
        sqlstm.iters = (unsigned int  )1;
        sqlstm.offset = (unsigned int  )294;
        sqlstm.selerr = (unsigned short)1;
        sqlstm.sqlpfmem = (unsigned int  )0;
        sqlstm.cud = sqlcud0;
        sqlstm.sqlest = (unsigned char  *)&sqlca;
        sqlstm.sqlety = (unsigned short)4352;
        sqlstm.occurs = (unsigned int  )0;
        sqlstm.sqfoff = (         int )0;
        sqlstm.sqfmod = (unsigned int )2;
        sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
        sqlstm.sqhstl[0] = (unsigned long )1;
        sqlstm.sqhsts[0] = (         int  )0;
        sqlstm.sqindv[0] = (         short *)0;
        sqlstm.sqinds[0] = (         int  )0;
        sqlstm.sqharm[0] = (unsigned long )0;
        sqlstm.sqadto[0] = (unsigned short )0;
        sqlstm.sqtdso[0] = (unsigned short )0;
        sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
        sqlstm.sqhstl[1] = (unsigned long )5;
        sqlstm.sqhsts[1] = (         int  )0;
        sqlstm.sqindv[1] = (         short *)0;
        sqlstm.sqinds[1] = (         int  )0;
        sqlstm.sqharm[1] = (unsigned long )0;
        sqlstm.sqadto[1] = (unsigned short )0;
        sqlstm.sqtdso[1] = (unsigned short )0;
        sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
        sqlstm.sqhstl[2] = (unsigned long )10;
        sqlstm.sqhsts[2] = (         int  )0;
        sqlstm.sqindv[2] = (         short *)0;
        sqlstm.sqinds[2] = (         int  )0;
        sqlstm.sqharm[2] = (unsigned long )0;
        sqlstm.sqadto[2] = (unsigned short )0;
        sqlstm.sqtdso[2] = (unsigned short )0;
        sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
        sqlstm.sqhstl[3] = (unsigned long )6;
        sqlstm.sqhsts[3] = (         int  )0;
        sqlstm.sqindv[3] = (         short *)0;
        sqlstm.sqinds[3] = (         int  )0;
        sqlstm.sqharm[3] = (unsigned long )0;
        sqlstm.sqadto[3] = (unsigned short )0;
        sqlstm.sqtdso[3] = (unsigned short )0;
        sqlstm.sqhstv[4] = (unsigned char  *)&o_base_no;
        sqlstm.sqhstl[4] = (unsigned long )sizeof(int);
        sqlstm.sqhsts[4] = (         int  )0;
        sqlstm.sqindv[4] = (         short *)0;
        sqlstm.sqinds[4] = (         int  )0;
        sqlstm.sqharm[4] = (unsigned long )0;
        sqlstm.sqadto[4] = (unsigned short )0;
        sqlstm.sqtdso[4] = (unsigned short )0;
        sqlstm.sqhstv[5] = (unsigned char  *)&o_e_east;
        sqlstm.sqhstl[5] = (unsigned long )sizeof(int);
        sqlstm.sqhsts[5] = (         int  )0;
        sqlstm.sqindv[5] = (         short *)0;
        sqlstm.sqinds[5] = (         int  )0;
        sqlstm.sqharm[5] = (unsigned long )0;
        sqlstm.sqadto[5] = (unsigned short )0;
        sqlstm.sqtdso[5] = (unsigned short )0;
        sqlstm.sqhstv[6] = (unsigned char  *)&o_e_north;
        sqlstm.sqhstl[6] = (unsigned long )sizeof(int);
        sqlstm.sqhsts[6] = (         int  )0;
        sqlstm.sqindv[6] = (         short *)0;
        sqlstm.sqinds[6] = (         int  )0;
        sqlstm.sqharm[6] = (unsigned long )0;
        sqlstm.sqadto[6] = (unsigned short )0;
        sqlstm.sqtdso[6] = (unsigned short )0;
        sqlstm.sqhstv[7] = (unsigned char  *)&o_stn_type;
        sqlstm.sqhstl[7] = (unsigned long )1;
        sqlstm.sqhsts[7] = (         int  )0;
        sqlstm.sqindv[7] = (         short *)0;
        sqlstm.sqinds[7] = (         int  )0;
        sqlstm.sqharm[7] = (unsigned long )0;
        sqlstm.sqadto[7] = (unsigned short )0;
        sqlstm.sqtdso[7] = (unsigned short )0;
        sqlstm.sqhstv[8] = (unsigned char  *)&o_sub_district;
        sqlstm.sqhstl[8] = (unsigned long )6;
        sqlstm.sqhsts[8] = (         int  )0;
        sqlstm.sqindv[8] = (         short *)0;
        sqlstm.sqinds[8] = (         int  )0;
        sqlstm.sqharm[8] = (unsigned long )0;
        sqlstm.sqadto[8] = (unsigned short )0;
        sqlstm.sqtdso[8] = (unsigned short )0;
        sqlstm.sqhstv[9] = (unsigned char  *)&o_cellsite;
        sqlstm.sqhstl[9] = (unsigned long )28;
        sqlstm.sqhsts[9] = (         int  )0;
        sqlstm.sqindv[9] = (         short *)0;
        sqlstm.sqinds[9] = (         int  )0;
        sqlstm.sqharm[9] = (unsigned long )0;
        sqlstm.sqadto[9] = (unsigned short )0;
        sqlstm.sqtdso[9] = (unsigned short )0;
        sqlstm.sqhstv[10] = (unsigned char  *)&o_room;
        sqlstm.sqhstl[10] = (unsigned long )11;
        sqlstm.sqhsts[10] = (         int  )0;
        sqlstm.sqindv[10] = (         short *)0;
        sqlstm.sqinds[10] = (         int  )0;
        sqlstm.sqharm[10] = (unsigned long )0;
        sqlstm.sqadto[10] = (unsigned short )0;
        sqlstm.sqtdso[10] = (unsigned short )0;
        sqlstm.sqhstv[11] = (unsigned char  *)&o_block;
        sqlstm.sqhstl[11] = (unsigned long )10;
        sqlstm.sqhsts[11] = (         int  )0;
        sqlstm.sqindv[11] = (         short *)0;
        sqlstm.sqinds[11] = (         int  )0;
        sqlstm.sqharm[11] = (unsigned long )0;
        sqlstm.sqadto[11] = (unsigned short )0;
        sqlstm.sqtdso[11] = (unsigned short )0;
        sqlstm.sqhstv[12] = (unsigned char  *)&o_floor;
        sqlstm.sqhstl[12] = (unsigned long )8;
        sqlstm.sqhsts[12] = (         int  )0;
        sqlstm.sqindv[12] = (         short *)0;
        sqlstm.sqinds[12] = (         int  )0;
        sqlstm.sqharm[12] = (unsigned long )0;
        sqlstm.sqadto[12] = (unsigned short )0;
        sqlstm.sqtdso[12] = (unsigned short )0;
        sqlstm.sqhstv[13] = (unsigned char  *)&o_bldg;
        sqlstm.sqhstl[13] = (unsigned long )23;
        sqlstm.sqhsts[13] = (         int  )0;
        sqlstm.sqindv[13] = (         short *)0;
        sqlstm.sqinds[13] = (         int  )0;
        sqlstm.sqharm[13] = (unsigned long )0;
        sqlstm.sqadto[13] = (unsigned short )0;
        sqlstm.sqtdso[13] = (unsigned short )0;
        sqlstm.sqhstv[14] = (unsigned char  *)&o_street_no;
        sqlstm.sqhstl[14] = (unsigned long )13;
        sqlstm.sqhsts[14] = (         int  )0;
        sqlstm.sqindv[14] = (         short *)0;
        sqlstm.sqinds[14] = (         int  )0;
        sqlstm.sqharm[14] = (unsigned long )0;
        sqlstm.sqadto[14] = (unsigned short )0;
        sqlstm.sqtdso[14] = (unsigned short )0;
        sqlstm.sqhstv[15] = (unsigned char  *)&o_street_name;
        sqlstm.sqhstl[15] = (unsigned long )33;
        sqlstm.sqhsts[15] = (         int  )0;
        sqlstm.sqindv[15] = (         short *)0;
        sqlstm.sqinds[15] = (         int  )0;
        sqlstm.sqharm[15] = (unsigned long )0;
        sqlstm.sqadto[15] = (unsigned short )0;
        sqlstm.sqtdso[15] = (unsigned short )0;
        sqlstm.sqhstv[16] = (unsigned char  *)&o_cancel_date;
        sqlstm.sqhstl[16] = (unsigned long )12;
        sqlstm.sqhsts[16] = (         int  )0;
        sqlstm.sqindv[16] = (         short *)0;
        sqlstm.sqinds[16] = (         int  )0;
        sqlstm.sqharm[16] = (unsigned long )0;
        sqlstm.sqadto[16] = (unsigned short )0;
        sqlstm.sqtdso[16] = (unsigned short )0;
        sqlstm.sqphsv = sqlstm.sqhstv;
        sqlstm.sqphsl = sqlstm.sqhstl;
        sqlstm.sqphss = sqlstm.sqhsts;
        sqlstm.sqpind = sqlstm.sqindv;
        sqlstm.sqpins = sqlstm.sqinds;
        sqlstm.sqparm = sqlstm.sqharm;
        sqlstm.sqparc = sqlstm.sqharc;
        sqlstm.sqpadto = sqlstm.sqadto;
        sqlstm.sqptdso = sqlstm.sqtdso;
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



        if (sqlca.sqlcode == NOT_FOUND)
            break;

/*
refresh();
*/
    }

    /* EXEC SQL CLOSE C00; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 17;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )377;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    if (*nearsite_cnt == 0)
    {
        sprintf(err_msg, "No stations found");
        return NOT_FOUND;
    }
    else
        return OK;

sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    refresh();
    beep();
    sleep(1);
    clear();
    refresh();
    endwin();
    exit(1);
}


/**********************************************************************/
/*  print near site report heading                                    */
/**********************************************************************/

print_nearsite_head(nfp, page_cnt)
FILE    *nfp;
int     *page_cnt;
{

/*
#ifdef DEBUG
    printf("print_nearsite_head\n");
#endif
*/

   if (*page_cnt > 1)
       fprintf(nfp, "\f");

   fprintf(nfp, "RUN DATE: %s%23s", sys_date, "");
   fprintf(nfp, "****************************************************");
   fprintf(nfp, "%22sPAGE   : %-d\n", "", *page_cnt);
   fprintf(nfp, "RUN TIME: %s%23s", sys_time, "");
   fprintf(nfp, "*                                                  *");
   fprintf(nfp, "%22sPROGRAM: esemns0f\n", "");
   fprintf(nfp, "USER ID : %-19s%12s", emc_uid, "");
   fprintf(nfp, "*       NEAR-SITE ANALYSIS %4d MHz - %4d MHz     *\n", o_freq_lo, o_freq_hi);
   fprintf(nfp, "%41s", "");
   fprintf(nfp, "*    STATIONS WITHIN %-4dM OF PROPOSED STATION     *\n",
           distance);
   fprintf(nfp, "%41s", "");
   fprintf(nfp, "*                                                  *\n");
   fprintf(nfp, "%41s", "");
   fprintf(nfp, "*    SYSTEM ID :  %-24s         *\n", prop_sys_desc);
   fprintf(nfp, "%41s", "");
   fprintf(nfp, "*                                                  *\n");
   fprintf(nfp, "%41s", "");
   fprintf(nfp, "****************************************************");
   fprintf(nfp, "\n\n\n");
}


print_nearsite_subhead(nfp)
/* added by Chen Yung */
FILE    *nfp;
/* -- */
{
   fprintf(nfp, "PROPOSED STATION GRID : %05d (E)    %05d (N)\n", 
                o_p_east, o_p_north);
   fprintf(nfp, "%37sSUB- DISTANCE\n", " ");
   fprintf(nfp, "SYSTEM ID%16sEAST%2sNORTH DIST   (M)%4s", "", "", "");
   fprintf(nfp, "CELL SITE + LOCATION\n");
   fprintf(nfp, "======================== ===== ===== ");
   fprintf(nfp, "==== ======== =========================");
   fprintf(nfp, "=====================================================\n\n");
}
