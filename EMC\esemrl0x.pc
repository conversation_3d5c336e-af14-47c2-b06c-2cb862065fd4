/**********************************************************************/
/*                                                                    */
/*    Module Name   :  load_reference (esemrl0x.pc)                   */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. <PERSON>. Mak                                      */
/*                                                                    */
/*    Callers       :  main (esemba0x.c)                              */  
/*                                                                    */
/*    Parameters    :                                                 */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  Retrieve the EMC reference data from ORACLE    */
/*                     database and put into following arrays:        */
/*                       subdist (sub-district info.),                */
/*                       off_channel (off-channel rejection info.),   */
/*                       min_signal (min. usable signal),             */
/*                       fine_terrain_east and fine_terrain_north,    */
/*                       culling frequencies for desensitisation and  */
/*                       intermodulation                              */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include "../include/define.h"
#include "../include/ref.h"
#include "../include/train.h"
#include "../include/emcext.h"


int load_reference()
{
/*  char   err_msg[80];*/
    int    i;

    EXEC SQL BEGIN DECLARE SECTION;

        VARCHAR o_sub_district[4];
        int     o_noise_code;
        char    o_dist_type;
        int     o_low_vhf_reg;
        int     o_low_vhf_hd;
        int     o_vhf_reg;
        int     o_vhf_hd;
        int     o_uhf_reg;
        int     o_uhf_hd;
        int     o_uhf_800_reg;
        int     o_uhf_800_hd;
        float   o_ch_sep;
        float   o_low_vhf_rej;
        float   o_vhf_rej;
        float   o_uhf_rej;
        float   o_uhf_800_rej;
        float   o_low_vhf_sig;
        float   o_vhf_sig;
        float   o_uhf_sig;
        float   o_uhf_800_sig;
        float   o_desen_cull;
        float   o_intmod2_cull;
        float   o_intmod3_cull;
        int     o_fine_east;
        int     o_fine_north;

    EXEC SQL END DECLARE SECTION;

    EXEC SQL INCLUDE SQLCA;


    EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn;
    EXEC SQL WHENEVER NOT FOUND CONTINUE;

#ifdef DEBUG
    if (interactive == FALSE)
    {
        printf("load_reference\n");
        fflush(stdin);
    }
#endif

    EXEC SQL DECLARE C10 CURSOR FOR
         SELECT SUBDISTRICT, NOISE_CODE, DISTRICT_TYPE, LOW_VHF_CULL_REG,
                LOW_VHF_CULL_HD, VHF_CULL_REG, VHF_CULL_HD, UHF_CULL_REG,
                UHF_CULL_HD, UHF_800_CULL_REG, UHF_800_CULL_HD
         FROM   SUBDISTRICT_TAB
         ORDER BY SUBDISTRICT;

    EXEC SQL OPEN C10;

    for (i = 0; i < MAX_SUBDIST; i++)
    {
        EXEC SQL
             FETCH C10
             INTO  :o_sub_district, :o_noise_code, :o_dist_type, :o_low_vhf_reg,
                   :o_low_vhf_hd, :o_vhf_reg, :o_vhf_hd, :o_uhf_reg,
                   :o_uhf_hd, :o_uhf_800_reg, :o_uhf_800_hd;

        if (sqlca.sqlcode == NOT_FOUND)
            break;

        o_sub_district.arr[o_sub_district.len] = '\0';
/*        strcpy(subdist[i].sub_district, o_sub_district.arr); */
        strcpy(subdist[i].sub_district, (char *)o_sub_district.arr);
        subdist[i].noise_code = o_noise_code;
        subdist[i].dist_type  = o_dist_type;
        subdist[i].cull_dist[0][0] = o_low_vhf_reg;
        subdist[i].cull_dist[0][1] = o_low_vhf_hd;
        subdist[i].cull_dist[1][0] = o_vhf_reg;
        subdist[i].cull_dist[1][1] = o_vhf_hd;
        subdist[i].cull_dist[2][0] = o_uhf_reg;
        subdist[i].cull_dist[2][1] = o_uhf_hd;
        subdist[i].cull_dist[3][0] = o_uhf_800_reg;
        subdist[i].cull_dist[3][1] = o_uhf_800_hd;
    }

    EXEC SQL CLOSE C10;
    subdist_cnt = i;

/*
    printf("\n");
    for (i=0; i<subdist_cnt; i++)
        printf("%d: %s %d %d %d %d %d %d %d %d %d\n", 
               i, subdist[i].sub_district, subdist[i].noise_code,
               subdist[i].cull_dist[0][0], subdist[i].cull_dist[0][1],
               subdist[i].cull_dist[1][0], subdist[i].cull_dist[1][1],
               subdist[i].cull_dist[2][0], subdist[i].cull_dist[2][1],
               subdist[i].cull_dist[3][0], subdist[i].cull_dist[3][1]);
*/

    EXEC SQL DECLARE C11 CURSOR FOR
         SELECT CHANNEL_SEPARATION, LOW_VHF_REJECT, HIGH_VHF_REJECT,
                UHF_REJECT, UHF_800_REJECT
         FROM   OFF_CHANNEL_REJECT
         ORDER BY CHANNEL_SEPARATION;

    EXEC SQL OPEN C11;

    off_channel[0].ch_sep = 0;
    off_channel[0].rej_db[0] = 0;
    off_channel[0].rej_db[1] = 0;
    off_channel[0].rej_db[2] = 0;
    off_channel[0].rej_db[3] = 0;

    for (i = 1; i < MAX_OFF_CHAN; i++)
    {
        EXEC SQL
             FETCH C11
             INTO  :o_ch_sep, :o_low_vhf_rej, :o_vhf_rej, :o_uhf_rej,
                   :o_uhf_800_rej;

        if (sqlca.sqlcode == NOT_FOUND)
            break;

        off_channel[i].ch_sep = o_ch_sep;
        off_channel[i].rej_db[0] = o_low_vhf_rej;
        off_channel[i].rej_db[1] = o_vhf_rej;
        off_channel[i].rej_db[2] = o_uhf_rej;
        off_channel[i].rej_db[3] = o_uhf_800_rej;
    }

    EXEC SQL CLOSE C11;
    off_chan_cnt = i;

/*
    printf("\n");
    for (i=0; i<off_chan_cnt; i++)
        printf("%f %f %f %f %f\n", off_channel[i].ch_sep,
               off_channel[i].rej_db[0], off_channel[i].rej_db[1],
               off_channel[i].rej_db[2], off_channel[i].rej_db[3]);
*/

    EXEC SQL DECLARE C12 CURSOR FOR
         SELECT LOW_VHF_SIGNAL, HIGH_VHF_SIGNAL,
                UHF_SIGNAL, UHF_800_SIGNAL
         FROM   MIN_USABLE_SIGNAL
         ORDER BY NOISE_CODE;

    EXEC SQL OPEN C12;

    min_signal[0][0] = 0;
    min_signal[0][1] = 0;
    min_signal[0][2] = 0;
    min_signal[0][3] = 0;

    for (i = 1; i < MAX_NOISE; i++)
    {
        EXEC SQL
             FETCH C12
             INTO  :o_low_vhf_sig, :o_vhf_sig, :o_uhf_sig,
                   :o_uhf_800_sig;

        if (sqlca.sqlcode == NOT_FOUND)
            break;

        min_signal[i][0] = o_low_vhf_sig;
        min_signal[i][1] = o_vhf_sig;
        min_signal[i][2] = o_uhf_sig;
        min_signal[i][3] = o_uhf_800_sig;
    }

    EXEC SQL CLOSE C12;
    noise_cnt = i;

/*
    printf("\n");
    for (i=0; i<noise_cnt; i++)
        printf("%d: %f %f %f %f\n", i,
               min_signal[i][0], min_signal[i][1],
               min_signal[i][2], min_signal[i][3]);
*/


    EXEC SQL DECLARE C13 CURSOR FOR
         SELECT GRID_EAST, GRID_NORTH
         FROM   FINE_TERRAIN_POINT;

    EXEC SQL OPEN C13;

    for (i = 0; fine_terrain_east[i] != DUMMY; i++)
    {
        EXEC SQL
             FETCH C13
             INTO  :o_fine_east, o_fine_north;

        if (sqlca.sqlcode == NOT_FOUND)
            break;

        fine_terrain_east[i]  = o_fine_east;
        fine_terrain_north[i] = o_fine_north;
    }

    EXEC SQL CLOSE C13;
    fine_terrain_cnt = i;

/*
    printf("\n");
    for (i=0; i<fine_terrain_cnt; i++)
        printf("%d %d\n", fine_terrain_east[i], fine_terrain_north[i]);
*/

    EXEC SQL
         SELECT DESENSITISATION, SIGNAL_2_INTERMOD, SIGNAL_3_INTERMOD 
         INTO   :o_desen_cull, :o_intmod2_cull, :o_intmod3_cull
         FROM   CULLING_FREQUENCY;

    desensit_cull_freq  = o_desen_cull;
    intermod2_cull_freq = o_intmod2_cull;
    intermod3_cull_freq = o_intmod3_cull;

/* commented out by Chen Yung
    return;
*/
/* changed by Chen Yung */
/* original: */
/*    return; */
    return(0);


sqlerr_rtn:
/* changed by Chen Yung */
/* original: */
/*    printf("%s\c", sqlca.sqlerrm.sqlerrmc); */
    printf("%s\n", sqlca.sqlerrm.sqlerrmc);
    fflush(afp);
    fclose(afp);
    exit(1);

}
