/**********************************************************************/
/*                                                                    */
/*    Module Name   :  set_band_mode (esemsb0x.c)                     */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. <PERSON>. Mak                                      */
/*                                                                    */
/*    Callers       :  main (esemba0x.c)                              */  
/*                     desensit_analysis (esemda0r.c)                 */
/*                                                                    */
/*    Parameters    :  prop_or_exist                                  */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  Set up the transmission mode and band category */
/*                     of proposed station or existing station by     */
/*                     calling set_band_mode2 to do the actual set-up */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include "../include/macros.h"
#include "../include/define.h"
#include "../include/modeband.h"


/*#ifdef BATCHED_EMC */

#include "../include/propext.h"
#include "../include/existext.h"




void set_band_mode2();




void set_band_mode(prop_or_exist)
int    prop_or_exist;
{
    double   eff_freq;


	
	
		
/*
#ifdef DEBUG
    printf("set_band_mode\n");
#endif
*/

    if (prop_or_exist == IS_PROPOSED)
	{	

        set_band_mode2(&(prop->mode), 
                       &(prop->band), 
                       prop_tx_freq, 
                       prop_rx_freq);
	}				   
    else
	{	

        set_band_mode2(&(exist[e_idx]->mode), 
                       &(exist[e_idx]->band), 
                       exist_tx_freq, 
                       exist_rx_freq);
	}				   
					   

						   
/*
    if (prop_or_exist == IS_PROPOSED)
    {
	prop->mode = 
            (abs(prop_tx_freq - prop_rx_freq) <= FREQ_EPSILON)? SFX : DFX;
	if (prop_tx_freq <= FREQ_EPSILON)
	    prop->mode = RX_ONLY;
	if (prop_rx_freq <= FREQ_EPSILON)
            prop->mode = TX_ONLY;

        prop->band = 0;
	eff_freq = max(prop_tx_freq, prop_rx_freq);
        if (eff_freq > HIGH_VHF)
            prop->band = 1;
        if (eff_freq > UHF)
            prop->band = 2;
        if (eff_freq > UHF_800)
            prop->band = 3;
    }
    else
    {
        exist[e_idx]->mode =
	    (abs(exist_tx_freq - exist_rx_freq) <= FREQ_EPSILON)? SFX : DFX;
	if (exist_tx_freq <= FREQ_EPSILON)
            exist[e_idx]->mode = RX_ONLY;
	if (exist_rx_freq <= FREQ_EPSILON)
            exist[e_idx]->mode = TX_ONLY;

        exist[e_idx]->band = 0;
	eff_freq = max(exist_tx_freq, exist_rx_freq);
        if (eff_freq > HIGH_VHF)
            exist[e_idx]->band = 1;
        if (eff_freq > UHF)
            exist[e_idx]->band = 2;
        if (eff_freq > UHF_800)
            exist[e_idx]->band = 3;
    }
*/
}

/*#endif */


/**********************************************************************/
/*                                                                    */
/*    Module Name   :  set_band_mode2 (esemsb0x.c)                    */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. H. Mak                                      */
/*                                                                    */
/*    Callers       :  set_band_mode (esemsb0x.c)                     */  
/*                     cosite (esemcs0f.pc)                           */
/*                                                                    */
/*    Parameters    :  tx_mode                                        */
/*                     freq_band                                      */
/*                     tx_freq                                        */
/*                     rx_freq                                        */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  Actual set-up of transmission mode and band    */
/*                     category of incoming channel pair.             */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

void set_band_mode2(tx_mode, freq_band, tx_freq, rx_freq)
int    *freq_band;

char   *tx_mode;

double tx_freq, rx_freq;
{
    int    eff_freq = max(tx_freq, rx_freq);

					
	
	*tx_mode =             (abs(tx_freq - rx_freq) <= FREQ_EPSILON)? SFX : DFX;
				
			
	if (  (abs(tx_freq - rx_freq)) <= FREQ_EPSILON) 
	{
		*tx_mode =SFX;  
	
	
}
	else
	{
		*tx_mode =DFX;  
	
}








	if (tx_freq <= FREQ_EPSILON)
	    *tx_mode = RX_ONLY;

	
	if (rx_freq <= FREQ_EPSILON)
            *tx_mode = TX_ONLY;

		
	
exist[e_idx]->mode ='\0';   //20180720 Cyrus Add for similar foe production, this function always assign nothing to "exist[e_idx]->mode" cause invalide data type int and char, in C old version cannot cast.

		
		
        *freq_band = 0;
        if (eff_freq > HIGH_VHF)
            *freq_band = 1;
        if (eff_freq > UHF)
            *freq_band = 2;
        if (eff_freq > UHF_800)
            *freq_band = 3;

		
}
