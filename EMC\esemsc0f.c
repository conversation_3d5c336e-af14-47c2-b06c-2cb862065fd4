
/* Result Sets Interface */
#ifndef SQL_CRSR
#  define SQL_CRSR
  struct sql_cursor
  {
    unsigned int curocn;
    void *ptr1;
    void *ptr2;
    unsigned int magic;
  };
  typedef struct sql_cursor sql_cursor;
  typedef struct sql_cursor SQL_CURSOR;
#endif /* SQL_CRSR */

/* Thread Safety */
typedef void * sql_context;
typedef void * SQL_CONTEXT;

/* Object support */
struct sqltvn
{
  unsigned char *tvnvsn; 
  unsigned short tvnvsnl; 
  unsigned char *tvnnm;
  unsigned short tvnnml; 
  unsigned char *tvnsnm;
  unsigned short tvnsnml;
};
typedef struct sqltvn sqltvn;

struct sqladts
{
  unsigned int adtvsn; 
  unsigned short adtmode; 
  unsigned short adtnum;  
  sqltvn adttvn[1];       
};
typedef struct sqladts sqladts;

static struct sqladts sqladt = {
  1,0,0,
};

/* Binding to PL/SQL Records */
struct sqltdss
{
  unsigned int tdsvsn; 
  unsigned short tdsnum; 
  unsigned char *tdsval[1]; 
};
typedef struct sqltdss sqltdss;
static struct sqltdss sqltds =
{
  1,
  0,
};

/* File name & Package Name */
struct sqlcxp
{
  unsigned short fillen;
           char  filnam[12];
};
static const struct sqlcxp sqlfpn =
{
    11,
    "esemsc0f.pc"
};


static unsigned int sqlctx = 149867;


static struct sqlexd {
   unsigned long  sqlvsn;
   unsigned int   arrsiz;
   unsigned int   iters;
   unsigned int   offset;
   unsigned short selerr;
   unsigned short sqlety;
   unsigned int   occurs;
      const short *cud;
   unsigned char  *sqlest;
      const char  *stmt;
   sqladts *sqladtp;
   sqltdss *sqltdsp;
   unsigned char  **sqphsv;
   unsigned long  *sqphsl;
            int   *sqphss;
            short **sqpind;
            int   *sqpins;
   unsigned long  *sqparm;
   unsigned long  **sqparc;
   unsigned short  *sqpadto;
   unsigned short  *sqptdso;
   unsigned int   sqlcmax;
   unsigned int   sqlcmin;
   unsigned int   sqlcincr;
   unsigned int   sqlctimeout;
   unsigned int   sqlcnowait;
            int   sqfoff;
   unsigned int   sqcmod;
   unsigned int   sqfmod;
   unsigned int   sqlpfmem;
   unsigned char  *sqhstv[16];
   unsigned long  sqhstl[16];
            int   sqhsts[16];
            short *sqindv[16];
            int   sqinds[16];
   unsigned long  sqharm[16];
   unsigned long  *sqharc[16];
   unsigned short  sqadto[16];
   unsigned short  sqtdso[16];
} sqlstm = {13,16};

/* SQLLIB Prototypes */
extern void sqlcxt (void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlcx2t(void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlbuft(void **, char *);
extern void sqlgs2t(void **, char *);
extern void sqlorat(void **, unsigned int *, void *);

/* Forms Interface */
static const int IAPSUCC = 0;
static const int IAPFAIL = 1403;
static const int IAPFTL  = 535;
extern void sqliem(unsigned char *, signed int *);

 static const char *sq0001 = 
"select BAND_CODE ,BASE_TX_LOW ,BASE_TX_HIGH ,BASE_RX_LOW ,BASE_RX_HIGH  from\
 FREQUENCY_BAND  order by BASE_TX_LOW            ";

 static const char *sq0002 = 
"select WEIGHTING_SET ,CATEGORY ,WEIGHTING ,REMARK  from WEIGHTING_FACTOR  or\
der by WEIGHTING_SET,CATEGORY            ";

 static const char *sq0009 = 
"select WEIGHTING  from WEIGHTING_FACTOR where WEIGHTING_SET=:b0 order by CAT\
EGORY            ";

 static const char *sq0012 = 
"select SYS_CATEGORY ,SYS_TYPE ,SYS_NO ,SYS_SUFFIX ,TX_FREQ ,NVL(RX_FREQ,0.0)\
 ,TO_CHAR(ASSIGN_DATE,'DD/MM/YY') ,NVL(TO_CHAR(CANCEL_DATE),'-')  from ASSIGN_\
CH where (TX_FREQ between :b0 and :b1 and ASSIGN_DATE<=TO_DATE(:b2)) order by \
TX_FREQ,SYS_CATEGORY,SYS_TYPE,SYS_NO,SYS_SUFFIX            ";

 static const char *sq0016 = 
"select BASE_NO  from BASE_CH where (((((TX_FREQ between :b0 and :b1 and RX_F\
REQ is null ) and SYS_CATEGORY=:b2) and SYS_TYPE=:b3) and SYS_NO=:b4) and SYS_\
SUFFIX=:b5)           ";

 static const char *sq0017 = 
"select X.BASE_NO  from BASE_CH X where ((((X.SYS_CATEGORY=:b0 and X.SYS_TYPE\
=:b1) and X.SYS_NO=:b2) and X.SYS_SUFFIX=:b3) and ((X.TX_FREQ between :b4 and \
:b5 and X.RX_FREQ between :b6 and :b7) or ((X.TX_FREQ between :b4 and :b5 and \
X.RX_FREQ is null ) and exists (select *  from BASE_CH Y where (((((Y.SYS_CATE\
GORY=:b0 and Y.SYS_TYPE=:b1) and Y.SYS_NO=:b2) and Y.SYS_SUFFIX=:b3) and Y.RX_\
FREQ between :b6 and :b7) and Y.TX_FREQ is null )))))           ";

typedef struct { unsigned short len; unsigned char arr[1]; } VARCHAR;
typedef struct { unsigned short len; unsigned char arr[1]; } varchar;

/* cud (compilation unit data) array */
static const short sqlcud0[] =
{13,4242,1,0,0,
5,0,0,1,125,0,9,986,0,0,0,0,0,1,0,
20,0,0,1,0,0,13,988,0,0,5,0,0,1,0,2,9,0,0,2,4,0,0,2,4,0,0,2,4,0,0,2,4,0,0,
55,0,0,1,0,0,15,996,0,0,0,0,0,1,0,
70,0,0,1,0,0,13,1013,0,0,5,0,0,1,0,2,9,0,0,2,4,0,0,2,4,0,0,2,4,0,0,2,4,0,0,
105,0,0,1,0,0,15,1020,0,0,0,0,0,1,0,
120,0,0,2,117,0,9,1039,0,0,0,0,0,1,0,
135,0,0,2,0,0,13,1041,0,0,4,0,0,1,0,2,3,0,0,2,3,0,0,2,4,0,0,2,9,0,0,
166,0,0,2,0,0,15,1048,0,0,0,0,0,1,0,
181,0,0,2,0,0,13,1064,0,0,4,0,0,1,0,2,3,0,0,2,3,0,0,2,4,0,0,2,9,0,0,
212,0,0,2,0,0,15,1070,0,0,0,0,0,1,0,
227,0,0,3,60,0,4,1119,0,0,2,1,0,1,0,2,1,0,0,1,9,0,0,
250,0,0,4,54,0,4,1164,0,0,2,1,0,1,0,2,1,0,0,1,9,0,0,
273,0,0,5,66,0,4,1207,0,0,2,1,0,1,0,2,1,0,0,1,3,0,0,
296,0,0,6,142,0,4,1255,0,0,6,1,0,1,0,2,4,0,0,2,4,0,0,2,4,0,0,2,4,0,0,2,4,0,0,1,
9,0,0,
335,0,0,7,78,0,4,1279,0,0,2,1,0,1,0,1,9,0,0,2,9,0,0,
358,0,0,8,146,0,4,1286,0,0,4,3,0,1,0,2,3,0,0,1,4,0,0,1,4,0,0,1,9,0,0,
389,0,0,9,93,0,9,1381,0,0,1,1,0,1,0,1,3,0,0,
408,0,0,9,0,0,13,1383,0,0,1,0,0,1,0,2,4,0,0,
427,0,0,9,0,0,13,1386,0,0,1,0,0,1,0,2,4,0,0,
446,0,0,9,0,0,13,1389,0,0,1,0,0,1,0,2,4,0,0,
465,0,0,9,0,0,15,1393,0,0,0,0,0,1,0,
480,0,0,10,86,0,4,1410,0,0,3,1,0,1,0,2,4,0,0,2,4,0,0,1,9,0,0,
507,0,0,11,78,0,4,1424,0,0,2,1,0,1,0,1,9,0,0,2,9,0,0,
530,0,0,12,291,0,9,1440,0,0,3,3,0,1,0,1,4,0,0,1,4,0,0,1,9,0,0,
557,0,0,12,0,0,13,1442,0,0,8,0,0,1,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,4,0,0,2,
4,0,0,2,9,0,0,2,9,0,0,
604,0,0,12,0,0,15,1450,0,0,0,0,0,1,0,
619,0,0,12,0,0,13,1472,0,0,8,0,0,1,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,4,0,0,2,
4,0,0,2,9,0,0,2,9,0,0,
666,0,0,12,0,0,15,1488,0,0,0,0,0,1,0,
681,0,0,13,139,0,4,1508,0,0,3,2,0,1,0,2,3,0,0,1,4,0,0,1,4,0,0,
708,0,0,14,150,0,4,1518,0,0,5,4,0,1,0,2,3,0,0,1,4,0,0,1,4,0,0,1,4,0,0,1,4,0,0,
743,0,0,15,124,0,4,1553,0,0,4,2,0,1,0,2,4,0,0,2,9,0,0,1,4,0,0,1,4,0,0,
774,0,0,16,176,0,9,1618,0,0,6,6,0,1,0,1,4,0,0,1,4,0,0,1,1,0,0,1,9,0,0,1,9,0,0,
1,9,0,0,
813,0,0,16,0,0,13,1619,0,0,1,0,0,1,0,2,3,0,0,
832,0,0,17,452,0,9,1659,0,0,16,16,0,1,0,1,1,0,0,1,9,0,0,1,9,0,0,1,9,0,0,1,4,0,
0,1,4,0,0,1,4,0,0,1,4,0,0,1,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,1,0,
0,0,1,0,0,0,
911,0,0,17,0,0,13,1660,0,0,1,0,0,1,0,2,3,0,0,
930,0,0,12,0,0,13,1666,0,0,8,0,0,1,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,4,0,0,2,
4,0,0,2,9,0,0,2,9,0,0,
977,0,0,12,0,0,15,1683,0,0,0,0,0,1,0,
992,0,0,18,139,0,4,1706,0,0,6,5,0,1,0,2,9,0,0,1,1,0,0,1,9,0,0,1,9,0,0,1,9,0,0,
1,3,0,0,
1031,0,0,16,0,0,13,1745,0,0,1,0,0,1,0,2,3,0,0,
1050,0,0,17,0,0,13,1750,0,0,1,0,0,1,0,2,3,0,0,
1069,0,0,16,0,0,15,1760,0,0,0,0,0,1,0,
1084,0,0,17,0,0,15,1764,0,0,0,0,0,1,0,
1099,0,0,19,118,0,4,1786,0,0,5,4,0,1,0,2,9,0,0,1,1,0,0,1,9,0,0,1,9,0,0,1,9,0,0,
1134,0,0,12,0,0,13,1839,0,0,8,0,0,1,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,4,0,0,
2,4,0,0,2,9,0,0,2,9,0,0,
1181,0,0,12,0,0,15,1856,0,0,0,0,0,1,0,
1196,0,0,12,0,0,13,1871,0,0,8,0,0,1,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,4,0,0,
2,4,0,0,2,9,0,0,2,9,0,0,
1243,0,0,12,0,0,15,1887,0,0,0,0,0,1,0,
1258,0,0,12,0,0,13,1953,0,0,8,0,0,1,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,4,0,0,
2,4,0,0,2,9,0,0,2,9,0,0,
1305,0,0,12,0,0,15,1970,0,0,0,0,0,1,0,
1320,0,0,20,0,0,32,2111,0,0,0,0,0,1,0,
};


/**********************************************************************/
/*                                                                    */
/*    Module Name   :  main (esemsc0f.pc)                             */
/*    Date Written  :  May, 1993                                      */
/*    Author        :  C. H. Mak                                      */
/*                                                                    */
/*    Callers       :  esemfp0f.c through 'system' statement          */  
/*                                                                    */
/*    Parameters    :  user id.                                       */
/*                     user password                                  */
/*                     EMC user id.                                   */
/*                     printer id.                                    */
/*                                                                    */
/*    Called Modules:  screen functions from 'screen.c'               */
/*                     (centre_msg, disp_err, disp_space)             */
/*                     screen function from 'cursesX'                 */
/*                     user_login and user_logout (login.pc)          */
/*                                                                    */
/*    Purpose       :  Accept user input frequency band, weighting    */
/*                     factor and business code, then print out user- */
/*                     specified no. of channels within the selected  */
/*                     frequency band in ascending order of figure    */
/*                     sharing (figure of sharing is calculated based */
/*                     on user-supplied formula).                     */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      May-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <time.h>
#include <curses.h>
#include "../include/macros.h"
#include "../include/emc.h"


#define  SLEEP_SEC      2

#define  MAX_FLD_LEN    10     /* max. field length of input field */

#define  LOGIN_OK       0

/* ORACLE status */
#define  FOUND          0
#define  NOT_FOUND      1403

#define  NEXT_START_PT  0     /* field index at which user starts his   */
                              /* input for next frequency pre-selection */

#define  MAX_BAND_NO           100   /* max. no. of bands                 */
#define  MAX_WEIGHT_NO         61    /* max. no. of weighting factor sets */
#define  MAX_BAND_LEN          77
#define  MAX_WEIGHT_LEN        61
#define  MAX_CHANNEL_NO        600   /* max. no. of channels in a band     */
#define  MAX_SHARER_NO         20    /* max. no. of sharers in a channel   */

#define  MAX_SUBDIST_NO        8     /* max. no. of sub-districts per line */
#define  MAX_SUBDIST_NO_2      (MAX_SUBDIST_NO * 2)
                               /* total no. of sub-districts for a system  */

#define  LINES_PER_PAGE        60

#define  FREQ_EPSILON          0.0005

#define  TRIGGER_WIN_MSG       "Press F2 for window select"

#include "../include/winscrn.h"


char    *band[MAX_BAND_NO];
char    *wset[MAX_WEIGHT_NO];

char    *band_head[] = 
{
" BAND CODE     BASE TX LOW     BASE TX HIGH     BASE RX LOW     BASE RX HIGH",
"                  (MHz)           (MHz)            (MHz)           (MHz)    ",
" =========     ===========     ============     ===========     ============",
(char *)NULL
};
            
char    *wset_head[] = 
{
" SET NO.      CATEGORY      WEIGHTING      REMARK           ",
" =======      ========      =========      ===============  ",
(char *)NULL
};
            
/* Report line details */
struct tag
{
    char   system_id[MAX_SHARER_NO][14];
    char   subdistrict_line1[MAX_SHARER_NO][MAX_SUBDIST_NO * 5];
    char   subdistrict_line2[MAX_SHARER_NO][MAX_SUBDIST_NO * 5];
    char   buss_code[MAX_SHARER_NO][5];
    char   assign_date[MAX_SHARER_NO][9];
    char   measure_date[9];
    int    no_of_mobiles;
    double tx_freq;
    double rx_freq;
    float  traffic;
    float  share_figure;
    int    sharer_cnt;
    int    same_buss_cnt;
};

typedef struct tag  FREQ_PRESEL;
FREQ_PRESEL  *t_rec[MAX_CHANNEL_NO];

/* field validation function declarations */
int    chk_band_code();
int    chk_buss_code();
int    chk_wgt_set();

/* For field definitions, see ../include/winscrn.h */
FIELD item[] = 
{
18,12,0,    band,band_head,TRUE,STRING, TRUE, 9, 0, NEW,"         ",   "",0,FALSE,chk_band_code,
18,43,DUMMY,NULL_AR,NULL_AR,TRUE,STRING, TRUE, 4, 0, NEW,"    ",   "",0,FALSE,chk_buss_code,
18,73,1,    wset,wset_head,TRUE,INTEGER,TRUE, 2, 0, NEW,"00", "",0,FALSE,chk_wgt_set,
19,33,DUMMY,NULL_AR,NULL_AR,TRUE,INTEGER,TRUE, 3, 0, NEW,"000","",0,FALSE,NULL_FUNC,
-1,-1,DUMMY,NULL_AR,NULL_AR,TRUE,DUMMY,  TRUE, 0, 0, NEW,"",   "",0,FALSE,NULL_FUNC
};

FIELD_WIN fld_win[] = 
{
NULL_WIN, 4, 1,  12, MAX_BAND_LEN,
NULL_WIN, 4, 8, 12, MAX_WEIGHT_LEN,
NULL_WIN, DUMMY,DUMMY,DUMMY,DUMMY
};

char   *prog_id = "emsc0f_01";
char   *screen_head = "SHARED CHANNEL ALLOCATION REPORT GENERATION";

char   *getenv();

char   passwd[20];
char   emc_uid[20];
char   buss_code[5];
char   band_code[10];
int    no_of_channels;
int    max_sharer_cnt = DUMMY;
int    max_buss_cnt = DUMMY;

extern char msg[];

/* EXEC SQL BEGIN DECLARE SECTION; */ 


    char     o_sys_category;
    /* VARCHAR  o_sys_type[3]; */ 
struct { unsigned short len; unsigned char arr[3]; } o_sys_type;

    /* VARCHAR  o_sys_no[8]; */ 
struct { unsigned short len; unsigned char arr[8]; } o_sys_no;

    /* VARCHAR  o_sys_suffix[4]; */ 
struct { unsigned short len; unsigned char arr[4]; } o_sys_suffix;

    double   o_tx_freq_hi;
    double   o_tx_freq_lo;
    double   o_rx_freq_hi;
    double   o_rx_freq_lo;
    double   o1_tx_freq_hi;
    double   o1_tx_freq_lo;
    double   o1_rx_freq_hi;
    double   o1_rx_freq_lo;
    double   o_tx_freq;
    double   o_rx_freq;
    float    o_channel_spacing;
    /* VARCHAR  o_band_code[10]; */ 
struct { unsigned short len; unsigned char arr[10]; } o_band_code;

    /* VARCHAR  o_buss_code[5]; */ 
struct { unsigned short len; unsigned char arr[5]; } o_buss_code;

    /* VARCHAR  o_subdistrict[4]; */ 
struct { unsigned short len; unsigned char arr[4]; } o_subdistrict;

    /* VARCHAR  o_assign_date[9]; */ 
struct { unsigned short len; unsigned char arr[9]; } o_assign_date;

    /* VARCHAR  o_ac_cancel_date[10]; */ 
struct { unsigned short len; unsigned char arr[10]; } o_ac_cancel_date;

    /* VARCHAR  o_sys_date[9]; */ 
struct { unsigned short len; unsigned char arr[9]; } o_sys_date;

    /* VARCHAR  o_date[10]; */ 
struct { unsigned short len; unsigned char arr[10]; } o_date;

    /* VARCHAR  o_measure_date[9]; */ 
struct { unsigned short len; unsigned char arr[9]; } o_measure_date;

    /* VARCHAR  o_remark[16]; */ 
struct { unsigned short len; unsigned char arr[16]; } o_remark;

    int      o_no_of_channels;
    int      o_base_no;
    int      o_weight_set;
    int      o_category;
    float    o_weight;
    float    o_weight_traffic;
    float    o_weight_sharer;
    float    o_weight_buss;
    float    o_load_per_mobile;
    int      o_mobile_cnt;
    char     o_dummy;

/* EXEC SQL END DECLARE SECTION; */ 


/* EXEC SQL INCLUDE SQLCA;
 */ 
/*
 * $Header: sqlca.h 24-apr-2003.12:50:58 mkandarp Exp $ sqlca.h 
 */

/* Copyright (c) 1985, 2003, Oracle Corporation.  All rights reserved.  */
 
/*
NAME
  SQLCA : SQL Communications Area.
FUNCTION
  Contains no code. Oracle fills in the SQLCA with status info
  during the execution of a SQL stmt.
NOTES
  **************************************************************
  ***                                                        ***
  *** This file is SOSD.  Porters must change the data types ***
  *** appropriately on their platform.  See notes/pcport.doc ***
  *** for more information.                                  ***
  ***                                                        ***
  **************************************************************

  If the symbol SQLCA_STORAGE_CLASS is defined, then the SQLCA
  will be defined to have this storage class. For example:
 
    #define SQLCA_STORAGE_CLASS extern
 
  will define the SQLCA as an extern.
 
  If the symbol SQLCA_INIT is defined, then the SQLCA will be
  statically initialized. Although this is not necessary in order
  to use the SQLCA, it is a good pgming practice not to have
  unitialized variables. However, some C compilers/OS's don't
  allow automatic variables to be init'd in this manner. Therefore,
  if you are INCLUDE'ing the SQLCA in a place where it would be
  an automatic AND your C compiler/OS doesn't allow this style
  of initialization, then SQLCA_INIT should be left undefined --
  all others can define SQLCA_INIT if they wish.

  If the symbol SQLCA_NONE is defined, then the SQLCA variable will
  not be defined at all.  The symbol SQLCA_NONE should not be defined
  in source modules that have embedded SQL.  However, source modules
  that have no embedded SQL, but need to manipulate a sqlca struct
  passed in as a parameter, can set the SQLCA_NONE symbol to avoid
  creation of an extraneous sqlca variable.
 
MODIFIED
    lvbcheng   07/31/98 -  long to int
    jbasu      12/12/94 -  Bug 217878: note this is an SOSD file
    losborne   08/11/92 -  No sqlca var if SQLCA_NONE macro set 
  Clare      12/06/84 - Ch SQLCA to not be an extern.
  Clare      10/21/85 - Add initialization.
  Bradbury   01/05/86 - Only initialize when SQLCA_INIT set
  Clare      06/12/86 - Add SQLCA_STORAGE_CLASS option.
*/
 
#ifndef SQLCA
#define SQLCA 1
 
struct   sqlca
         {
         /* ub1 */ char    sqlcaid[8];
         /* b4  */ int     sqlabc;
         /* b4  */ int     sqlcode;
         struct
           {
           /* ub2 */ unsigned short sqlerrml;
           /* ub1 */ char           sqlerrmc[70];
           } sqlerrm;
         /* ub1 */ char    sqlerrp[8];
         /* b4  */ int     sqlerrd[6];
         /* ub1 */ char    sqlwarn[8];
         /* ub1 */ char    sqlext[8];
         };

#ifndef SQLCA_NONE 
#ifdef   SQLCA_STORAGE_CLASS
SQLCA_STORAGE_CLASS struct sqlca sqlca
#else
         struct sqlca sqlca
#endif
 
#ifdef  SQLCA_INIT
         = {
         {'S', 'Q', 'L', 'C', 'A', ' ', ' ', ' '},
         sizeof(struct sqlca),
         0,
         { 0, {0}},
         {'N', 'O', 'T', ' ', 'S', 'E', 'T', ' '},
         {0, 0, 0, 0, 0, 0},
         {0, 0, 0, 0, 0, 0, 0, 0},
         {0, 0, 0, 0, 0, 0, 0, 0}
         }
#endif
         ;
#endif
 
#endif
 
/* end SQLCA */



main(argc, argv)
int    argc;
char   **argv;
{
    char    ns_fname[150];
    char    *confirm_msg = "** CONFIRM [Y/N]? ";
    char    err_msg[80];
    char    answer;
    FILE    *sfp;
    int     term_code;
    int     item_cnt;                   /* total no. of items               */
    int     last_item;                  /* index of last filled-up item     */

    int     x_pos, y_pos;               /* current cursor position          */
    int     curr_pos;                   /* current position of input string */
    int     loop;                       /* TRUE if entry continue on        */
                                        /* current item                     */
    int     modified = FALSE;           /* TRUE when current item has been  */
                                        /* modified                         */
    int     token;                      /* user-input character             */
    int     status = !(QUIT);
    int     direction;                  /* field shuttle direction          */
    int     select_by_window = FALSE;
    int     err_flag = FALSE;
    int     y1, y2, x1, x2;             /* co-ordinates of upper-left       */
                                        /* corner and lower-right corner of */
                                        /* selection window                 */
    int     win_cnt;                    /* no. of window lines              */
    int     entry;                      /* entry selected from selection    */
                                        /* window                           */
    int     page_cnt = 1;
    int     i;

    register int     j;
    struct tm  *tt;
char s[80];


    /* EXEC SQL WHENEVER NOT FOUND CONTINUE; */ 


    initscr();
    raw();
    noecho();
    clear();
    keypad(stdscr, TRUE); 

    if (argc != 6)
    {
       sprintf(err_msg, "Usage: esemsc0f user_id passwd emc_uid -P printer_id");
       goto force_exit;
    }

    if (strcmp(argv[4], "-P"))
    {
       sprintf(err_msg, "Usage: esemsc0f user_id passwd emc_uid -P printer_id");
       goto force_exit;
    }
    
    strcpy(user_id, argv[1]);
    strcpy(passwd, argv[2]);

    if (user_login(user_id, passwd, err_msg) != LOGIN_OK)
    {
        strip_blank(err_msg, TRAIL);
        strcat(err_msg, "  ");
        strcat(err_msg, user_id);
        strcat(err_msg, passwd);
        goto force_exit;
    }

    /* EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn; */ 


    strcpy(emc_uid, argv[3]);
    strcpy(printer_id, argv[5]);
    get_sys_date_time(sys_date, sys_time, yymmdd, hhmmss);

    for (i = 0; item[i].xpos != -1; i++)
        ;

    item_cnt = i;

    emc_dir = getenv("EMC");
    disp_heading(prog_id, screen_head, sys_date);
    disp_entry_scn(item_cnt, item_cnt, NEW_SCREEN);
 
    for (i = 0; status != QUIT; )
    {
        x_pos = item[i].xpos; 
        y_pos = item[i].ypos;

        if (with_window(&item[i]))
            centre_msg(16, TRIGGER_WIN_MSG, (A_BOLD|A_BLINK), A_REVERSE);
        else
            clear_msg(16);

         if (select_by_window)
             select_by_window = FALSE;

        /**********************************************************/
        /* if data type of current field is sign field, then skip */
        /* the 1st position of current string and increment       */
        /* screen x-coordinate by 1, else keep 1st position       */
        /**********************************************************/
        if (is_sign(&item[i]))
        {
            curr_pos = 1;
            x_pos++;
        }
        else
            curr_pos = 0;

        move(y_pos, x_pos);
        loop = TRUE;

        for (; loop == TRUE;)
        {
            refresh();
            token = getch();

            attroff(A_REVERSE);
            getyx(stdscr, y_pos, x_pos);
            if (err_flag == TRUE)
            {
                clear_err();
                err_flag = FALSE;
            }
            move(y_pos, x_pos);
            attron(A_REVERSE);

            /**************************************/
            /* Function key definitions:          */
            /*    KEY_F(1)  - quit session        */
            /*    KEY_F(2)  - trigger sub-window  */
            /*    KEY_F(3)  - refresh screen      */
            /*    KEY_F(4)  - clear field         */
            /*    TAB,                            */
            /*    NEWLINE,                        */
            /*    KEY_DOWN  - next field          */
            /*    CTRL_B,                         */
            /*    KEY_UP    - next field          */
            /*    KEY_LEFT  - next char position  */
            /*    KEY_RIGHT - next char position  */
            /*    DELETE,                         */
            /*    BACKSPACE - delete current char */
            /*                and back 1 position */
            /**************************************/

            switch (token)
            {
                case KEY_F(1):
                    status = QUIT;
                    loop = FALSE;
                    break;

                case KEY_F(2):
                    if (!(with_window(&item[i])))
                    {
                        beep();
                        break;
                    }
                    hide_cursor();
                    clear_msg(16);
                    if (item[i].first_select == TRUE)
                    {
                        int    status;

                        status = prepare_win_lines(&fld_win[item[i].win_idx],
                                                   item[i].disp_arr,
                                                   &win_cnt,
                                                   err_msg);
                        if (status == SELECT_ERROR)
                            goto force_exit;

                    }

                    y1 = fld_win[item[i].win_idx].win_ypos;
                    x1 = fld_win[item[i].win_idx].win_xpos;
                    y2 = y1 + fld_win[item[i].win_idx].win_lines - 1;
                    x2 = x1 + fld_win[item[i].win_idx].win_len - 1;
                    if (item[i].first_select == TRUE)
                    {
                        item[i].first_select = FALSE;
                        entry = select_entry(fld_win[item[i].win_idx].wp, TRUE,
                                             y1,x1,y2,x2, item[i].disp_arr,
                                             item[i].heading,
                                             fld_win[item[i].win_idx].win_len, 
                                             FALSE, win_cnt, err_msg);
                    }
                    else
                        entry = select_entry(fld_win[item[i].win_idx].wp, TRUE,
                                             y1,x1,y2,x2, item[i].disp_arr,
                                             item[i].heading,
                                             fld_win[item[i].win_idx].win_len, 
                                             TRUE, win_cnt, err_msg);

                    if (entry == SELECT_ERROR)
                        goto force_exit;

                    werase(fld_win[item[i].win_idx].wp);
                    clear_border(y1, x1, y2, x2);
                    attrset(A_REVERSE);
                    show_cursor();

                    if (entry != EXIT)
                    {
                        sscanf(item[i].disp_arr[entry], "%s", item[i].curr_str);
                        disp_space(item[i].ypos, item[i].xpos, item[i].fld_len);
                        mvaddstr(item[i].ypos, item[i].xpos, item[i].curr_str);
                        item[i].curr_len = strlen(item[i].curr_str);
                        item[i].state = MODIFIED;
                    }

                    select_by_window = TRUE;
                    loop = FALSE;
                    direction = FORWARD;
                    break;

                case KEY_F(3):
                    attroff(A_REVERSE);
                    refresh_screen(item_cnt, i);
                    if (with_window(&item[i]))
                        centre_msg(16, TRIGGER_WIN_MSG, 
                                   (A_BOLD|A_BLINK), A_REVERSE);
                    move(y_pos, x_pos);
                    break; 

                case KEY_F(4):
                    init_field(&item[i], &curr_pos);
                    loop = FALSE; 
                    direction = RESTART; 
                    break; 

                case KEY_UP:
                case CTRL_B:
                    if (i == 0)
                        beep();
                    else
                    {
                        loop = FALSE;
                        direction = BACKWARD;
                    }
                    break;

                case KEY_DOWN:
                case NEWLINE:
                case TAB:
/*
sprintf(s," i len req: %d %d %d", i, item[i].curr_len, item[i].required);
mvaddstr(23,0,s);
                    if ((item[i].state == NEW) && (item[i].required))
                    if ((is_sign(&item[i]) && (item[i].curr_len == 1))
                     || (!is_sign(&item[i]) && empty(&item[i]))
                    && (item[i].required))
*/
                    /*********************************************************/
                    /* beep user if he wants to skip an input-required field */
                    /*********************************************************/
                    if (is_sign(&item[i]))
                    {
                        if ((item[i].curr_len == 1) && (item[i].required))
                        {
                            if (with_window(&item[i]))
                                beep();
                            else
                            {
                                err_flag = TRUE;
                                disp_err(INPUT_REQ_MSG);
                                attron(A_REVERSE);
                            }
                            break;
                        }
                    }
                    else
                        if (empty(&item[i]) && (item[i].required))
                        {
                            if (with_window(&item[i]))
                                beep();
                            else
                            {
                                err_flag = TRUE;
                                disp_err(INPUT_REQ_MSG);
                                attron(A_REVERSE);
                            }
                            break;
                        }

                    if (empty(&item[i]) 
                    ||  (is_sign(&item[i]) && (item[i].curr_len == 1)))
                        strcpy(item[i].curr_str, item[i].init_str);

                    loop = FALSE;
                    direction = FORWARD;
                    break;

                case KEY_LEFT:

                    /************************************/
                    /* beep user if he has reached the  */
                    /* left-most position of a field    */
                    /************************************/
                    if (is_sign(&item[i]))
                        if (x_pos == item[i].xpos + 1)
                        {
                            beep();
                            break;
                        }

                    if (x_pos == item[i].xpos)
                        beep();
                    else
                    {
                        x_pos--; curr_pos--;
                    }
                    break;

                case KEY_RIGHT:

                    /************************************/
                    /* beep user if he has reached the  */
                    /* right boundary of current field  */
                    /************************************/
                    if (x_pos - item[i].xpos == item[i].curr_len)
                        beep();
                    else
                    {
                        x_pos++; curr_pos++;
                    }
                    break;

                case BACKSPACE:
                case DELETE:

                    /************************************/
                    /* beep user if he has reached the  */
                    /* left-most position of a field    */
                    /************************************/
                    if (is_sign(&item[i]))
                        if (x_pos == item[i].xpos + 1)
                        {
                            beep();
                            break;
                        }

                    if (x_pos == item[i].xpos)
                        beep();
                    else
                    {
                        x_pos--; curr_pos--;
                        if (is_float(&item[i]) 
                        && (item[i].curr_str[curr_pos] == DOT))
                            item[i].has_dot = FALSE;
                        move(y_pos, x_pos);
                        addch(BLANK);
                        modified = TRUE;
/*
                        if (x_pos == item[i].xpos)
                            item[i].state = NEW;
                        else
                            item[i].state = MODIFIED;
*/
                    }
                    break;

                case DOT:

                    /******************************************************/
                    /* beep user if data type of current field is integer */
                    /******************************************************/
                    if (is_int(&item[i]))
                    {
                        beep();
                        break;
                    }


                    /********************************************************/
                    /* beep user if data type of current field is float and */
                    /* user has already input one dot                       */
                    /********************************************************/
                    if (is_float(&item[i]))
                        if (item[i].has_dot)
                        {
                            beep();
                            break;
                        }
                        else
                            item[i].has_dot = TRUE;

                    if (item[i].state == NEW)
                        if (is_sign(&item[i]))
                        {
                            if (curr_pos == 1)
                            {
                                disp_space(y_pos, x_pos, item[i].fld_len - 1);
                                move(y_pos, x_pos);
                            }
                        }
                        else
                            if (curr_pos == 0)
                            {
                                disp_space(y_pos, x_pos, item[i].fld_len);
                                move(y_pos, x_pos);
                            }

                    addch(token);
                    item[i].curr_str[curr_pos] = token;
                    x_pos++; curr_pos++;
                    modified = TRUE;
                    item[i].state = MODIFIED;

                    if (x_pos - item[i].xpos == item[i].fld_len)
                    {
                        item[i].curr_str[item[i].fld_len] = '\0';
                        item[i].curr_len = item[i].fld_len;
                        loop = FALSE;
                        direction = FORWARD;
                    }

                    break;

                default:
                    if ((!isalnum(token)) && (token != MINUS) 
                    &&  (token != PLUS))
                    {
                        beep();
                        break;
                    }

                    if (!isdigit(token))
                    {
/*
mvaddstr(23, 0, "not digit");
*/
                        if (is_sign(&item[i]))
                        {
                            if ((token != MINUS) && (token != PLUS))
                            {
                                beep();
                                break;
                            }
                            else
                                if (curr_pos > 1)
                                {
                                    beep();
                                    break;
                                }
                        }
                        else
/*
sprintf(s, "should not be integer: %d %d %s", i, item[i].type, item[i].init_str);
mvaddstr(22, 0, s);
*/
                            if ((item[i].type != CHAR) 
                            &&  (item[i].type != STRING))
                            {
                                beep();
                                break;
                            }
                            else
                                if (isalpha(token))
                                    token = toupper(token); 
                    }
                    else
                        if (item[i].state == NEW)
                            if (is_sign(&item[i]))
                            {
                                if (curr_pos == 1)
                                {
                                    disp_space(y_pos, x_pos, item[i].fld_len-1);
                                    move(y_pos, x_pos);
                                }
                            }
                            else
                                if (curr_pos == 0)
                                {
                                    disp_space(y_pos, x_pos, item[i].fld_len);
                                    move(y_pos, x_pos);
                                }

                    if (((token == MINUS) || (token == PLUS)) 
                    &&  is_sign(&item[i]))
                    {
                        x_pos--; curr_pos--;  /* because we don't want to  */
                                              /* move cursor to 1 position */
                                              /* this statement is used    */
                                              /* to complement the         */
                                              /* 'x_pos++; curr_pos++'     */
                                              /* a few lines below         */
                        if (token == MINUS)
                        {
                            item[i].curr_str[0] = MINUS;
                            move(y_pos, x_pos);
                            addch(MINUS);
                        }
                            
                    }
                    else
                    {
                        item[i].curr_str[curr_pos] = token;
                        addch(token);
                    }

/*
sprintf(s, "modified");
mvaddstr(22, 0, s);
*/
                    if ((token != MINUS) && (token != PLUS))
                        item[i].state = MODIFIED;

                    modified = TRUE;
                    x_pos++; curr_pos++;

                    if (x_pos - item[i].xpos == item[i].fld_len)
                    {
                        item[i].curr_str[item[i].fld_len] = '\0';
                        item[i].curr_len = item[i].fld_len;
                        loop = FALSE;
                        direction = FORWARD;
                    }
                
                    break;
            }

            refresh();
            if (loop == FALSE)
            {
                if (!select_by_window)
                {
                    int    (*check_item)();

                    check_item = item[i].validate;
                    if (check_item != (int(*)())NULL)
                    {
                        if ((direction != BACKWARD) && (!empty(&item[i])))
                            if ((*check_item)(&i, err_msg) == ERROR)
                            {
                                err_flag = TRUE;
                                disp_err(err_msg);
                                attron(A_REVERSE);
                                loop = FALSE;
                                direction = RESTART;
                            }
                    }

/*
                    for (j = 0; j < item_cnt; j++)
                        if (is_sign(&item[j]))
                        {
                            if ((item[j].curr_len == 1) 
                            &&  (item[j].required == TRUE))
                            {
                                direction = RESTART;
                                err_flag = TRUE;
                                disp_err(INPUT_REQ_MSG);
                                attron(A_REVERSE);
                                i = j;
                                break;
                            }
                        }
*/
                } 

                switch (direction)
                {
                    case FORWARD:
                        i++;
                        break;
                    case BACKWARD:
                        i--;
                        break;
                    case RESTART:
                        break;
                }

                continue;
            }
            
            if (modified && (!select_by_window) && (!err_flag))
            {
                int    len = x_pos - item[i].xpos;
   
                if ((item[i].curr_len < len) || is_delete(token))
                {
                    item[i].curr_len = len;
                    item[i].curr_str[item[i].curr_len] = '\0';
                }
/*
disp_space(22,0,80);
sprintf(s,"i mode: %d %d", i, tx_mode);
mvaddstr(22,0,s);
*/

                modified = FALSE;
            }
  
            move(y_pos, x_pos);
        }

        if ((status != QUIT) && (i == item_cnt) && (!err_flag))
        {
            attrset(A_BOLD);
            mvaddstr(19, 47, confirm_msg);
            refresh();
            attrset(A_REVERSE);
            getyx(stdscr, y_pos, x_pos);
            x_pos += 2;
            disp_space(y_pos, x_pos, 1);
            attroff(A_REVERSE);
            move(y_pos, x_pos);
            refresh();
            read_str(&answer, "", 1, 0, 10, &term_code, A_REVERSE);
            answer = toupper(answer);
            disp_space(19, 47, 23);
            attron(A_REVERSE);
            if (answer == 'Y')
            {
                strcpy(band_code, item[0].curr_str);
                no_of_channels = atoi(item[3].curr_str);
                if (chk_no_of_channels(err_msg) == ERROR)
                {
                    disp_err(err_msg);
                    err_flag = TRUE;
                    i = 3;
                }
                else
                {
                    i = NEXT_START_PT;
    
                    hide_cursor();
                    centre_msg(23, "Processing ...", A_BLINK, A_REVERSE);
                    strcpy(buss_code, item[1].curr_str);
                    o_weight_set = atoi(item[2].curr_str);
                    if (print_schannel_rpt(err_msg) == NOT_FOUND)
                    {
                        disp_err(err_msg);
                        err_flag = TRUE;
                    }

                    show_cursor();
                    if (!err_flag)    /* clear "Processing ..." */
                    {
                        attrset(A_NORMAL);
                        disp_space(23, 0, 80);
                        refresh();
                        attrset(A_REVERSE);
                    }
                }
                
                if (!err_flag)
                    for (j = NEXT_START_PT; j < item_cnt; j++)
                        init_field(&item[j], &curr_pos);
                beep();
            }
            else
                i = 0;
        }
    }

    attroff(A_BOLD);
    clear();
    endwin();

    user_logout();
    exit(0);


sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);

force_exit:
    disp_err(err_msg); getch();
    clear(); show_cursor(); refresh();
    endwin();
    exit(1);
}


/**************************************************************/
/* Display new entry screen or refresh current screen to user */
/**************************************************************/

disp_entry_scn(item_cnt, curr_cnt, mode)
int    item_cnt;
int    curr_cnt;
int    mode;
{
    char    tmp_str[80];
    register int    i;

    attroff(A_REVERSE);
    mvaddstr(18, 0, "BAND CODE :");
    mvaddstr(18, 27, "BUSINESS CODE :");
    mvaddstr(18, 50, "WEIGHTING FACTOR SET :");
    mvaddstr(19, 0,  "NO. OF CHANNELS TO BE REPORTED :");
 
    sprintf(tmp_str, "%s",
"F1-Quit   F3-Refresh   F4-Clear field   UP-Prev   DOWN/<Tab>-next");
    centre_msg(21, tmp_str, A_BOLD, A_REVERSE);

    if (mode == REFRESH)
    {
        for (i = 0; i < curr_cnt; i++)
        {
            disp_space(item[i].ypos, item[i].xpos, item[i].fld_len);
            mvaddstr(item[i].ypos, item[i].xpos, item[i].curr_str);
        }
        for (i = curr_cnt; i < item_cnt; i++)
            if (item[i].state == NEW)
                mvaddstr(item[i].ypos, item[i].xpos, item[i].init_str);
            else
            {
                disp_space(item[i].ypos, item[i].xpos, item[i].fld_len);
                mvaddstr(item[i].ypos, item[i].xpos, item[i].curr_str);
            }
    }
    else
        for (i = 0; i < item_cnt; i++)
            mvaddstr(item[i].ypos, item[i].xpos, item[i].init_str);

    refresh();

}


/**************************************************************/
/* call 'disp_entry_scn' to refresh current screen to user    */
/**************************************************************/

refresh_screen(item_cnt, curr_cnt)
int item_cnt;
int curr_cnt;
{
    clear();
    disp_heading(prog_id, screen_head, sys_date);
    disp_entry_scn(item_cnt, curr_cnt, REFRESH);
}


/*************************************************/
/* initialise current field to its initial value */
/*************************************************/

init_field(p_item, curr_pos)
FIELD *p_item;
int   *curr_pos;
{
    mvaddstr(p_item->ypos, p_item->xpos, p_item->init_str);
    if (is_sign(p_item))
    {
        p_item->curr_len = *curr_pos = 1;
        p_item->curr_str[1] = '\0';
    }
    else
    {
        p_item->curr_len = *curr_pos = 0;
        p_item->curr_str[0] = '\0';
    }

    if (!disp_only(p_item))
    {
        p_item->state = NEW;
        if (is_float(p_item))
            p_item->has_dot = FALSE;
    }

    if (with_window(p_item) && (p_item->first_select == FALSE))
        p_item->first_select = TRUE;
}


/**********************************************************************/
/*  prepare window lines for display                                  */
/**********************************************************************/

prepare_win_lines(fw, disp_arr, win_cnt, err_msg)
FIELD_WIN  *fw;
char       **disp_arr;
int        *win_cnt;
char       *err_msg;
{
    int    i;
char s[80];

    if (disp_arr == band)
    {
        /* EXEC SQL DECLARE C03 CURSOR FOR
                 SELECT  BAND_CODE, BASE_TX_LOW, BASE_TX_HIGH,
                         BASE_RX_LOW, BASE_RX_HIGH
                 FROM    FREQUENCY_BAND
                 ORDER BY BASE_TX_LOW; */ 


        /* EXEC SQL OPEN C03; */ 

{
        struct sqlexd sqlstm;
        sqlstm.sqlvsn = 13;
        sqlstm.arrsiz = 0;
        sqlstm.sqladtp = &sqladt;
        sqlstm.sqltdsp = &sqltds;
        sqlstm.stmt = sq0001;
        sqlstm.iters = (unsigned int  )1;
        sqlstm.offset = (unsigned int  )5;
        sqlstm.selerr = (unsigned short)1;
        sqlstm.sqlpfmem = (unsigned int  )0;
        sqlstm.cud = sqlcud0;
        sqlstm.sqlest = (unsigned char  *)&sqlca;
        sqlstm.sqlety = (unsigned short)4352;
        sqlstm.occurs = (unsigned int  )0;
        sqlstm.sqcmod = (unsigned int )0;
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



        /* EXEC SQL
             FETCH C03
             INTO  :o_band_code, :o_tx_freq_lo, :o_tx_freq_hi,
                   :o_rx_freq_lo, :o_rx_freq_hi; */ 

{
        struct sqlexd sqlstm;
        sqlstm.sqlvsn = 13;
        sqlstm.arrsiz = 5;
        sqlstm.sqladtp = &sqladt;
        sqlstm.sqltdsp = &sqltds;
        sqlstm.iters = (unsigned int  )1;
        sqlstm.offset = (unsigned int  )20;
        sqlstm.selerr = (unsigned short)1;
        sqlstm.sqlpfmem = (unsigned int  )0;
        sqlstm.cud = sqlcud0;
        sqlstm.sqlest = (unsigned char  *)&sqlca;
        sqlstm.sqlety = (unsigned short)4352;
        sqlstm.occurs = (unsigned int  )0;
        sqlstm.sqfoff = (         int )0;
        sqlstm.sqfmod = (unsigned int )2;
        sqlstm.sqhstv[0] = (unsigned char  *)&o_band_code;
        sqlstm.sqhstl[0] = (unsigned long )12;
        sqlstm.sqhsts[0] = (         int  )0;
        sqlstm.sqindv[0] = (         short *)0;
        sqlstm.sqinds[0] = (         int  )0;
        sqlstm.sqharm[0] = (unsigned long )0;
        sqlstm.sqadto[0] = (unsigned short )0;
        sqlstm.sqtdso[0] = (unsigned short )0;
        sqlstm.sqhstv[1] = (unsigned char  *)&o_tx_freq_lo;
        sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
        sqlstm.sqhsts[1] = (         int  )0;
        sqlstm.sqindv[1] = (         short *)0;
        sqlstm.sqinds[1] = (         int  )0;
        sqlstm.sqharm[1] = (unsigned long )0;
        sqlstm.sqadto[1] = (unsigned short )0;
        sqlstm.sqtdso[1] = (unsigned short )0;
        sqlstm.sqhstv[2] = (unsigned char  *)&o_tx_freq_hi;
        sqlstm.sqhstl[2] = (unsigned long )sizeof(double);
        sqlstm.sqhsts[2] = (         int  )0;
        sqlstm.sqindv[2] = (         short *)0;
        sqlstm.sqinds[2] = (         int  )0;
        sqlstm.sqharm[2] = (unsigned long )0;
        sqlstm.sqadto[2] = (unsigned short )0;
        sqlstm.sqtdso[2] = (unsigned short )0;
        sqlstm.sqhstv[3] = (unsigned char  *)&o_rx_freq_lo;
        sqlstm.sqhstl[3] = (unsigned long )sizeof(double);
        sqlstm.sqhsts[3] = (         int  )0;
        sqlstm.sqindv[3] = (         short *)0;
        sqlstm.sqinds[3] = (         int  )0;
        sqlstm.sqharm[3] = (unsigned long )0;
        sqlstm.sqadto[3] = (unsigned short )0;
        sqlstm.sqtdso[3] = (unsigned short )0;
        sqlstm.sqhstv[4] = (unsigned char  *)&o_rx_freq_hi;
        sqlstm.sqhstl[4] = (unsigned long )sizeof(double);
        sqlstm.sqhsts[4] = (         int  )0;
        sqlstm.sqindv[4] = (         short *)0;
        sqlstm.sqinds[4] = (         int  )0;
        sqlstm.sqharm[4] = (unsigned long )0;
        sqlstm.sqadto[4] = (unsigned short )0;
        sqlstm.sqtdso[4] = (unsigned short )0;
        sqlstm.sqphsv = sqlstm.sqhstv;
        sqlstm.sqphsl = sqlstm.sqhstl;
        sqlstm.sqphss = sqlstm.sqhsts;
        sqlstm.sqpind = sqlstm.sqindv;
        sqlstm.sqpins = sqlstm.sqinds;
        sqlstm.sqparm = sqlstm.sqharm;
        sqlstm.sqparc = sqlstm.sqharc;
        sqlstm.sqpadto = sqlstm.sqadto;
        sqlstm.sqptdso = sqlstm.sqtdso;
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



        if (sqlca.sqlcode == NOT_FOUND)
        {
            sprintf(err_msg, "No band codes found in FREQUENCY_BAND");
            /* EXEC SQL CLOSE C03; */ 

{
            struct sqlexd sqlstm;
            sqlstm.sqlvsn = 13;
            sqlstm.arrsiz = 5;
            sqlstm.sqladtp = &sqladt;
            sqlstm.sqltdsp = &sqltds;
            sqlstm.iters = (unsigned int  )1;
            sqlstm.offset = (unsigned int  )55;
            sqlstm.cud = sqlcud0;
            sqlstm.sqlest = (unsigned char  *)&sqlca;
            sqlstm.sqlety = (unsigned short)4352;
            sqlstm.occurs = (unsigned int  )0;
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


            return ERROR;
        }
    
        for (i = 0; i < MAX_BAND_NO; i++)
        {
            o_band_code.arr[o_band_code.len] = '\0';
    
            if ((disp_arr[i] = (char *) malloc(MAX_BAND_LEN)) == (char *) NULL)
            {
                sprintf(err_msg, "Fail to allocate memory for disp_arr");
                return ERROR;
            }
            sprintf(disp_arr[i]," %9s%5s%11.5lf%5s%11.5lf%6s%11.5lf%5s%11.5lf  ",
                    o_band_code.arr, "", o_tx_freq_lo, "", o_tx_freq_hi, "",
                    o_rx_freq_lo, "", o_rx_freq_hi);

            /* EXEC SQL
                 FETCH C03
                 INTO  :o_band_code, :o_tx_freq_lo, :o_tx_freq_hi,
                       :o_rx_freq_lo, :o_rx_freq_hi; */ 

{
            struct sqlexd sqlstm;
            sqlstm.sqlvsn = 13;
            sqlstm.arrsiz = 5;
            sqlstm.sqladtp = &sqladt;
            sqlstm.sqltdsp = &sqltds;
            sqlstm.iters = (unsigned int  )1;
            sqlstm.offset = (unsigned int  )70;
            sqlstm.selerr = (unsigned short)1;
            sqlstm.sqlpfmem = (unsigned int  )0;
            sqlstm.cud = sqlcud0;
            sqlstm.sqlest = (unsigned char  *)&sqlca;
            sqlstm.sqlety = (unsigned short)4352;
            sqlstm.occurs = (unsigned int  )0;
            sqlstm.sqfoff = (         int )0;
            sqlstm.sqfmod = (unsigned int )2;
            sqlstm.sqhstv[0] = (unsigned char  *)&o_band_code;
            sqlstm.sqhstl[0] = (unsigned long )12;
            sqlstm.sqhsts[0] = (         int  )0;
            sqlstm.sqindv[0] = (         short *)0;
            sqlstm.sqinds[0] = (         int  )0;
            sqlstm.sqharm[0] = (unsigned long )0;
            sqlstm.sqadto[0] = (unsigned short )0;
            sqlstm.sqtdso[0] = (unsigned short )0;
            sqlstm.sqhstv[1] = (unsigned char  *)&o_tx_freq_lo;
            sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
            sqlstm.sqhsts[1] = (         int  )0;
            sqlstm.sqindv[1] = (         short *)0;
            sqlstm.sqinds[1] = (         int  )0;
            sqlstm.sqharm[1] = (unsigned long )0;
            sqlstm.sqadto[1] = (unsigned short )0;
            sqlstm.sqtdso[1] = (unsigned short )0;
            sqlstm.sqhstv[2] = (unsigned char  *)&o_tx_freq_hi;
            sqlstm.sqhstl[2] = (unsigned long )sizeof(double);
            sqlstm.sqhsts[2] = (         int  )0;
            sqlstm.sqindv[2] = (         short *)0;
            sqlstm.sqinds[2] = (         int  )0;
            sqlstm.sqharm[2] = (unsigned long )0;
            sqlstm.sqadto[2] = (unsigned short )0;
            sqlstm.sqtdso[2] = (unsigned short )0;
            sqlstm.sqhstv[3] = (unsigned char  *)&o_rx_freq_lo;
            sqlstm.sqhstl[3] = (unsigned long )sizeof(double);
            sqlstm.sqhsts[3] = (         int  )0;
            sqlstm.sqindv[3] = (         short *)0;
            sqlstm.sqinds[3] = (         int  )0;
            sqlstm.sqharm[3] = (unsigned long )0;
            sqlstm.sqadto[3] = (unsigned short )0;
            sqlstm.sqtdso[3] = (unsigned short )0;
            sqlstm.sqhstv[4] = (unsigned char  *)&o_rx_freq_hi;
            sqlstm.sqhstl[4] = (unsigned long )sizeof(double);
            sqlstm.sqhsts[4] = (         int  )0;
            sqlstm.sqindv[4] = (         short *)0;
            sqlstm.sqinds[4] = (         int  )0;
            sqlstm.sqharm[4] = (unsigned long )0;
            sqlstm.sqadto[4] = (unsigned short )0;
            sqlstm.sqtdso[4] = (unsigned short )0;
            sqlstm.sqphsv = sqlstm.sqhstv;
            sqlstm.sqphsl = sqlstm.sqhstl;
            sqlstm.sqphss = sqlstm.sqhsts;
            sqlstm.sqpind = sqlstm.sqindv;
            sqlstm.sqpins = sqlstm.sqinds;
            sqlstm.sqparm = sqlstm.sqharm;
            sqlstm.sqparc = sqlstm.sqharc;
            sqlstm.sqpadto = sqlstm.sqadto;
            sqlstm.sqptdso = sqlstm.sqtdso;
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


    
            if (sqlca.sqlcode == NOT_FOUND)
            {
                /* EXEC SQL CLOSE C03; */ 

{
                struct sqlexd sqlstm;
                sqlstm.sqlvsn = 13;
                sqlstm.arrsiz = 5;
                sqlstm.sqladtp = &sqladt;
                sqlstm.sqltdsp = &sqltds;
                sqlstm.iters = (unsigned int  )1;
                sqlstm.offset = (unsigned int  )105;
                sqlstm.cud = sqlcud0;
                sqlstm.sqlest = (unsigned char  *)&sqlca;
                sqlstm.sqlety = (unsigned short)4352;
                sqlstm.occurs = (unsigned int  )0;
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


                break;
            }
        }
    }

    if (i == MAX_BAND_NO)
    {
        sprintf(err_msg, "No. of band codes > MAX_BAND_NO");
        disp_err(err_msg);
    }

    if (disp_arr == wset)
    {
        /* EXEC SQL DECLARE C04 CURSOR FOR
                 SELECT  WEIGHTING_SET, CATEGORY, WEIGHTING, REMARK
                 FROM    WEIGHTING_FACTOR
                 ORDER BY WEIGHTING_SET, CATEGORY; */ 


        /* EXEC SQL OPEN C04; */ 

{
        struct sqlexd sqlstm;
        sqlstm.sqlvsn = 13;
        sqlstm.arrsiz = 5;
        sqlstm.sqladtp = &sqladt;
        sqlstm.sqltdsp = &sqltds;
        sqlstm.stmt = sq0002;
        sqlstm.iters = (unsigned int  )1;
        sqlstm.offset = (unsigned int  )120;
        sqlstm.selerr = (unsigned short)1;
        sqlstm.sqlpfmem = (unsigned int  )0;
        sqlstm.cud = sqlcud0;
        sqlstm.sqlest = (unsigned char  *)&sqlca;
        sqlstm.sqlety = (unsigned short)4352;
        sqlstm.occurs = (unsigned int  )0;
        sqlstm.sqcmod = (unsigned int )0;
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



        /* EXEC SQL
             FETCH C04
             INTO  :o_weight_set, :o_category, :o_weight, :o_remark; */ 

{
        struct sqlexd sqlstm;
        sqlstm.sqlvsn = 13;
        sqlstm.arrsiz = 5;
        sqlstm.sqladtp = &sqladt;
        sqlstm.sqltdsp = &sqltds;
        sqlstm.iters = (unsigned int  )1;
        sqlstm.offset = (unsigned int  )135;
        sqlstm.selerr = (unsigned short)1;
        sqlstm.sqlpfmem = (unsigned int  )0;
        sqlstm.cud = sqlcud0;
        sqlstm.sqlest = (unsigned char  *)&sqlca;
        sqlstm.sqlety = (unsigned short)4352;
        sqlstm.occurs = (unsigned int  )0;
        sqlstm.sqfoff = (         int )0;
        sqlstm.sqfmod = (unsigned int )2;
        sqlstm.sqhstv[0] = (unsigned char  *)&o_weight_set;
        sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
        sqlstm.sqhsts[0] = (         int  )0;
        sqlstm.sqindv[0] = (         short *)0;
        sqlstm.sqinds[0] = (         int  )0;
        sqlstm.sqharm[0] = (unsigned long )0;
        sqlstm.sqadto[0] = (unsigned short )0;
        sqlstm.sqtdso[0] = (unsigned short )0;
        sqlstm.sqhstv[1] = (unsigned char  *)&o_category;
        sqlstm.sqhstl[1] = (unsigned long )sizeof(int);
        sqlstm.sqhsts[1] = (         int  )0;
        sqlstm.sqindv[1] = (         short *)0;
        sqlstm.sqinds[1] = (         int  )0;
        sqlstm.sqharm[1] = (unsigned long )0;
        sqlstm.sqadto[1] = (unsigned short )0;
        sqlstm.sqtdso[1] = (unsigned short )0;
        sqlstm.sqhstv[2] = (unsigned char  *)&o_weight;
        sqlstm.sqhstl[2] = (unsigned long )sizeof(float);
        sqlstm.sqhsts[2] = (         int  )0;
        sqlstm.sqindv[2] = (         short *)0;
        sqlstm.sqinds[2] = (         int  )0;
        sqlstm.sqharm[2] = (unsigned long )0;
        sqlstm.sqadto[2] = (unsigned short )0;
        sqlstm.sqtdso[2] = (unsigned short )0;
        sqlstm.sqhstv[3] = (unsigned char  *)&o_remark;
        sqlstm.sqhstl[3] = (unsigned long )18;
        sqlstm.sqhsts[3] = (         int  )0;
        sqlstm.sqindv[3] = (         short *)0;
        sqlstm.sqinds[3] = (         int  )0;
        sqlstm.sqharm[3] = (unsigned long )0;
        sqlstm.sqadto[3] = (unsigned short )0;
        sqlstm.sqtdso[3] = (unsigned short )0;
        sqlstm.sqphsv = sqlstm.sqhstv;
        sqlstm.sqphsl = sqlstm.sqhstl;
        sqlstm.sqphss = sqlstm.sqhsts;
        sqlstm.sqpind = sqlstm.sqindv;
        sqlstm.sqpins = sqlstm.sqinds;
        sqlstm.sqparm = sqlstm.sqharm;
        sqlstm.sqparc = sqlstm.sqharc;
        sqlstm.sqpadto = sqlstm.sqadto;
        sqlstm.sqptdso = sqlstm.sqtdso;
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



        if (sqlca.sqlcode == NOT_FOUND)
        {
            sprintf(err_msg, "No weighting factor sets in WEIGHTING_FACTOR");
            /* EXEC SQL CLOSE C04; */ 

{
            struct sqlexd sqlstm;
            sqlstm.sqlvsn = 13;
            sqlstm.arrsiz = 5;
            sqlstm.sqladtp = &sqladt;
            sqlstm.sqltdsp = &sqltds;
            sqlstm.iters = (unsigned int  )1;
            sqlstm.offset = (unsigned int  )166;
            sqlstm.cud = sqlcud0;
            sqlstm.sqlest = (unsigned char  *)&sqlca;
            sqlstm.sqlety = (unsigned short)4352;
            sqlstm.occurs = (unsigned int  )0;
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


            return ERROR;
        }
    
        for (i = 0; i < MAX_WEIGHT_NO; i++)
        {
            if ((disp_arr[i] = (char *)malloc(MAX_WEIGHT_LEN)) == (char *) NULL)
            {
                sprintf(err_msg, "Fail to allocate memory for disp_arr");
                return ERROR;
            }
            o_remark.arr[o_remark.len] = '\0';
            sprintf(disp_arr[i], "   %2d%12s%2d%11s%5.2f%8s%-15s  ",
                    o_weight_set, "", o_category, "", o_weight, 
                    "", o_remark.arr);
    
            /* EXEC SQL
                 FETCH C04
                 INTO  :o_weight_set, :o_category, :o_weight, :o_remark; */ 

{
            struct sqlexd sqlstm;
            sqlstm.sqlvsn = 13;
            sqlstm.arrsiz = 5;
            sqlstm.sqladtp = &sqladt;
            sqlstm.sqltdsp = &sqltds;
            sqlstm.iters = (unsigned int  )1;
            sqlstm.offset = (unsigned int  )181;
            sqlstm.selerr = (unsigned short)1;
            sqlstm.sqlpfmem = (unsigned int  )0;
            sqlstm.cud = sqlcud0;
            sqlstm.sqlest = (unsigned char  *)&sqlca;
            sqlstm.sqlety = (unsigned short)4352;
            sqlstm.occurs = (unsigned int  )0;
            sqlstm.sqfoff = (         int )0;
            sqlstm.sqfmod = (unsigned int )2;
            sqlstm.sqhstv[0] = (unsigned char  *)&o_weight_set;
            sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
            sqlstm.sqhsts[0] = (         int  )0;
            sqlstm.sqindv[0] = (         short *)0;
            sqlstm.sqinds[0] = (         int  )0;
            sqlstm.sqharm[0] = (unsigned long )0;
            sqlstm.sqadto[0] = (unsigned short )0;
            sqlstm.sqtdso[0] = (unsigned short )0;
            sqlstm.sqhstv[1] = (unsigned char  *)&o_category;
            sqlstm.sqhstl[1] = (unsigned long )sizeof(int);
            sqlstm.sqhsts[1] = (         int  )0;
            sqlstm.sqindv[1] = (         short *)0;
            sqlstm.sqinds[1] = (         int  )0;
            sqlstm.sqharm[1] = (unsigned long )0;
            sqlstm.sqadto[1] = (unsigned short )0;
            sqlstm.sqtdso[1] = (unsigned short )0;
            sqlstm.sqhstv[2] = (unsigned char  *)&o_weight;
            sqlstm.sqhstl[2] = (unsigned long )sizeof(float);
            sqlstm.sqhsts[2] = (         int  )0;
            sqlstm.sqindv[2] = (         short *)0;
            sqlstm.sqinds[2] = (         int  )0;
            sqlstm.sqharm[2] = (unsigned long )0;
            sqlstm.sqadto[2] = (unsigned short )0;
            sqlstm.sqtdso[2] = (unsigned short )0;
            sqlstm.sqhstv[3] = (unsigned char  *)&o_remark;
            sqlstm.sqhstl[3] = (unsigned long )18;
            sqlstm.sqhsts[3] = (         int  )0;
            sqlstm.sqindv[3] = (         short *)0;
            sqlstm.sqinds[3] = (         int  )0;
            sqlstm.sqharm[3] = (unsigned long )0;
            sqlstm.sqadto[3] = (unsigned short )0;
            sqlstm.sqtdso[3] = (unsigned short )0;
            sqlstm.sqphsv = sqlstm.sqhstv;
            sqlstm.sqphsl = sqlstm.sqhstl;
            sqlstm.sqphss = sqlstm.sqhsts;
            sqlstm.sqpind = sqlstm.sqindv;
            sqlstm.sqpins = sqlstm.sqinds;
            sqlstm.sqparm = sqlstm.sqharm;
            sqlstm.sqparc = sqlstm.sqharc;
            sqlstm.sqpadto = sqlstm.sqadto;
            sqlstm.sqptdso = sqlstm.sqtdso;
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


    
            if (sqlca.sqlcode == NOT_FOUND)
            {
                /* EXEC SQL CLOSE C04; */ 

{
                struct sqlexd sqlstm;
                sqlstm.sqlvsn = 13;
                sqlstm.arrsiz = 5;
                sqlstm.sqladtp = &sqladt;
                sqlstm.sqltdsp = &sqltds;
                sqlstm.iters = (unsigned int  )1;
                sqlstm.offset = (unsigned int  )212;
                sqlstm.cud = sqlcud0;
                sqlstm.sqlest = (unsigned char  *)&sqlca;
                sqlstm.sqlety = (unsigned short)4352;
                sqlstm.occurs = (unsigned int  )0;
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


                break;
            }
        }
    }

    if (i == MAX_WEIGHT_NO)
    {
        sprintf(err_msg, "No. of weighting factor sets > MAX_WEIGHT_NO");
        disp_err(err_msg);
    }

    fw->wp = newwin(fw->win_lines, fw->win_len, fw->win_ypos, fw->win_xpos);
    keypad(fw->wp, TRUE);

    *win_cnt = i + 1;
    return OK;


sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    beep(); sleep(SLEEP_SEC);
    clear(); refresh();
    endwin();
    exit(1);
}


/************************************************************************/
/* check that input frequency band code exists in FREQUENCY_BAND        */
/************************************************************************/

int    chk_band_code(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{
/*    strcpy(o_band_code.arr, item[*curr_cnt].curr_str); 
    o_band_code.len = strlen(o_band_code.arr); */
	strcpy((char *)o_band_code.arr, item[*curr_cnt].curr_str);
    o_band_code.len = strlen((char *)o_band_code.arr);

/*
system("echo \"esemsc0f 1\" >> /tmp/debug");
sprintf(msg, "echo \"%s\" >> /tmp/debug", o_band_code.arr );
system(msg);
*/

    /* EXEC SQL
         SELECT  'X'
         INTO    :o_dummy
         FROM    FREQUENCY_BAND
         WHERE   BAND_CODE = :o_band_code; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 5;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "select 'X' into :b0  from FREQUENCY_BAND where BAND_CODE=\
:b1";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )227;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_dummy;
    sqlstm.sqhstl[0] = (unsigned long )1;
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_band_code;
    sqlstm.sqhstl[1] = (unsigned long )12;
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    if (sqlca.sqlcode == NOT_FOUND)
    {
        sprintf(err_msg, "Invalid band code");
        return ERROR;
    }

    return OK;


sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    beep(); sleep(SLEEP_SEC);
    clear(); refresh();
    endwin();
    exit(1);
}


/************************************************************************/
/* check that input business code exists in BUSINESS                    */
/************************************************************************/

int    chk_buss_code(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{
/*    strcpy(o_buss_code.arr, item[*curr_cnt].curr_str);
    o_buss_code.len = strlen(o_buss_code.arr); */
    strcpy((char *)o_buss_code.arr, item[*curr_cnt].curr_str);
    o_buss_code.len = strlen((char *)o_buss_code.arr);

/*
system("echo \"esemsc0f 2\" >> /tmp/debug");
sprintf(msg, "echo \"%s\" >> /tmp/debug", o_buss_code.arr );
system(msg);
*/

    /* EXEC SQL
         SELECT  'X'
         INTO    :o_dummy
         FROM    BUSINESS
         WHERE   BUSS_CODE = :o_buss_code; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 5;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "select 'X' into :b0  from BUSINESS where BUSS_CODE=:b1";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )250;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_dummy;
    sqlstm.sqhstl[0] = (unsigned long )1;
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_buss_code;
    sqlstm.sqhstl[1] = (unsigned long )7;
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    if (sqlca.sqlcode == NOT_FOUND)
    {
        sprintf(err_msg, "Invalid business code");
        return ERROR;
    }

    return OK;


sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    beep(); sleep(SLEEP_SEC);
    clear(); refresh();
    endwin();
    exit(1);
}


/************************************************************************/
/* check that input weighting factor set exists in WEIGHTING_FACTOR     */
/************************************************************************/

int    chk_wgt_set(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{

    o_weight_set = atoi(item[*curr_cnt].curr_str);

/*
system("echo \"esemsc0f 3\" >> /tmp/debug");
sprintf(msg, "echo \"%d\" >> /tmp/debug", o_weight_set );
system(msg);
*/

    /* EXEC SQL
         SELECT  'X'
         INTO    :o_dummy
         FROM    WEIGHTING_FACTOR
         WHERE   WEIGHTING_SET = :o_weight_set; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 5;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "select 'X' into :b0  from WEIGHTING_FACTOR where WEIGHTIN\
G_SET=:b1";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )273;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_dummy;
    sqlstm.sqhstl[0] = (unsigned long )1;
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_weight_set;
    sqlstm.sqhstl[1] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    if (sqlca.sqlcode == NOT_FOUND)
    {
        sprintf(err_msg, "Invalid weighting factor set");
        return ERROR;
    }

    return OK;


sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    beep(); sleep(SLEEP_SEC);
    clear(); refresh();
    endwin();
    exit(1);
}


/************************************************************************/
/* check that input no. of channels to be printed is within the max.    */
/* no. of channels of the specified frequency band.                     */
/************************************************************************/

int    chk_no_of_channels(err_msg)
char    *err_msg;
{
    float  channel_width;
    int    band_channel_no;

/*    strcpy(o_band_code.arr, band_code);
    o_band_code.len = strlen(o_band_code.arr); */
    strcpy((char *)o_band_code.arr, band_code);
    o_band_code.len = strlen((char *)o_band_code.arr);

/*
system("echo \"esemsc0f 4\" >> /tmp/debug");
sprintf(msg, "echo \"%s\" >> /tmp/debug", o_band_code.arr );
system(msg);
*/

    /* EXEC SQL
         SELECT  BASE_TX_LOW, BASE_TX_HIGH, BASE_RX_LOW, BASE_RX_HIGH,
                 CHANNEL_SPACING
         INTO    :o_tx_freq_lo, :o_tx_freq_hi, :o_rx_freq_lo, :o_rx_freq_hi,
                 :o_channel_spacing
         FROM    FREQUENCY_BAND
         WHERE   BAND_CODE = :o_band_code; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 6;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "select BASE_TX_LOW ,BASE_TX_HIGH ,BASE_RX_LOW ,BASE_RX_HI\
GH ,CHANNEL_SPACING into :b0,:b1,:b2,:b3,:b4  from FREQUENCY_BAND where BAND_C\
ODE=:b5";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )296;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_tx_freq_lo;
    sqlstm.sqhstl[0] = (unsigned long )sizeof(double);
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_tx_freq_hi;
    sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqhstv[2] = (unsigned char  *)&o_rx_freq_lo;
    sqlstm.sqhstl[2] = (unsigned long )sizeof(double);
    sqlstm.sqhsts[2] = (         int  )0;
    sqlstm.sqindv[2] = (         short *)0;
    sqlstm.sqinds[2] = (         int  )0;
    sqlstm.sqharm[2] = (unsigned long )0;
    sqlstm.sqadto[2] = (unsigned short )0;
    sqlstm.sqtdso[2] = (unsigned short )0;
    sqlstm.sqhstv[3] = (unsigned char  *)&o_rx_freq_hi;
    sqlstm.sqhstl[3] = (unsigned long )sizeof(double);
    sqlstm.sqhsts[3] = (         int  )0;
    sqlstm.sqindv[3] = (         short *)0;
    sqlstm.sqinds[3] = (         int  )0;
    sqlstm.sqharm[3] = (unsigned long )0;
    sqlstm.sqadto[3] = (unsigned short )0;
    sqlstm.sqtdso[3] = (unsigned short )0;
    sqlstm.sqhstv[4] = (unsigned char  *)&o_channel_spacing;
    sqlstm.sqhstl[4] = (unsigned long )sizeof(float);
    sqlstm.sqhsts[4] = (         int  )0;
    sqlstm.sqindv[4] = (         short *)0;
    sqlstm.sqinds[4] = (         int  )0;
    sqlstm.sqharm[4] = (unsigned long )0;
    sqlstm.sqadto[4] = (unsigned short )0;
    sqlstm.sqtdso[4] = (unsigned short )0;
    sqlstm.sqhstv[5] = (unsigned char  *)&o_band_code;
    sqlstm.sqhstl[5] = (unsigned long )12;
    sqlstm.sqhsts[5] = (         int  )0;
    sqlstm.sqindv[5] = (         short *)0;
    sqlstm.sqinds[5] = (         int  )0;
    sqlstm.sqharm[5] = (unsigned long )0;
    sqlstm.sqadto[5] = (unsigned short )0;
    sqlstm.sqtdso[5] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    
    if (sqlca.sqlcode == NOT_FOUND) 
    {
        sprintf(err_msg, "Fatal: band code (%s) not in FREQUENCY BAND",
                band_code);
        goto sqlerr_rtn;
    }

    /********************************************************************/
    /* get assigned date boundary (currently 3 months from system date) */
    /* for channel selection                                            */
    /********************************************************************/
/*    strcpy(o_sys_date.arr, sys_date);
    o_sys_date.len = strlen(o_sys_date.arr); */
    strcpy((char *)o_sys_date.arr, sys_date);
    o_sys_date.len = strlen((char *)o_sys_date.arr);
    /* EXEC SQL 
         SELECT TO_CHAR(ADD_MONTHS(TO_DATE(:o_sys_date, 'DD/MM/YY'), -3))
         INTO   :o_date
         FROM DUMMY; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 6;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "select TO_CHAR(ADD_MONTHS(TO_DATE(:b0,'DD/MM/YY'),(-3))) \
into :b1  from DUMMY ";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )335;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_date;
    sqlstm.sqhstl[0] = (unsigned long )11;
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_date;
    sqlstm.sqhstl[1] = (unsigned long )12;
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    o_date.arr[o_date.len] = '\0';

    /* EXEC SQL
         SELECT COUNT(UNIQUE TX_FREQ)
         INTO   :o_no_of_channels
         FROM   ASSIGN_CH
         WHERE  TX_FREQ between :o_tx_freq_lo and :o_tx_freq_hi
         AND    CANCEL_DATE IS NULL
         AND    ASSIGN_DATE <= TO_DATE(:o_date); */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 6;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "select count(unique TX_FREQ) into :b0  from ASSIGN_CH whe\
re ((TX_FREQ between :b1 and :b2 and CANCEL_DATE is null ) and ASSIGN_DATE<=TO\
_DATE(:b3))";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )358;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_no_of_channels;
    sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_tx_freq_lo;
    sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqhstv[2] = (unsigned char  *)&o_tx_freq_hi;
    sqlstm.sqhstl[2] = (unsigned long )sizeof(double);
    sqlstm.sqhsts[2] = (         int  )0;
    sqlstm.sqindv[2] = (         short *)0;
    sqlstm.sqinds[2] = (         int  )0;
    sqlstm.sqharm[2] = (unsigned long )0;
    sqlstm.sqadto[2] = (unsigned short )0;
    sqlstm.sqtdso[2] = (unsigned short )0;
    sqlstm.sqhstv[3] = (unsigned char  *)&o_date;
    sqlstm.sqhstl[3] = (unsigned long )12;
    sqlstm.sqhsts[3] = (         int  )0;
    sqlstm.sqindv[3] = (         short *)0;
    sqlstm.sqinds[3] = (         int  )0;
    sqlstm.sqharm[3] = (unsigned long )0;
    sqlstm.sqadto[3] = (unsigned short )0;
    sqlstm.sqtdso[3] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    if (no_of_channels > o_no_of_channels)
    {
        sprintf(err_msg, "There are only %d assigned channels, so select a no. which is <= %d",
                o_no_of_channels, o_no_of_channels);
        return ERROR;
    }

    if (o_no_of_channels > MAX_CHANNEL_NO)
    {
        sprintf(err_msg, "No. of shared channels in band > %s, press any key to continue",
                MAX_CHANNEL_NO); 
        return ERROR;
    }
/*
    channel_width = o_tx_freq_hi - o_tx_freq_lo;
    band_channel_no = (int)(channel_width / o_channel_spacing) + 1;
    if (band_channel_no > no_of_channels)
    {
        sprintf(err_msg, "No. of channel to be printed must be < %d",
                band_channel_no);
        return ERROR;
    }
*/

    return OK;

sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    beep(); sleep(SLEEP_SEC);
    clear(); refresh();
    endwin();
    exit(1);
}


/**********************************************************************/
/*  print shared channels report                                      */
/**********************************************************************/

print_schannel_rpt(err_msg)
char   *err_msg;
{
    char   sc_fname[120], err_fname[100];
    char   prev_sys_id[14];
    char   cmdline[150];
    double prev_tx_freq = (double)0;
    double prev_rx_freq = (double)0;
    FILE   *sfp, *efp;
    int    line_cnt = 0;
    int    page_cnt = 1;
/*
    int    sys_cnt = 0;
*/
    int    err_flag = FALSE;
    int    i;

    register int j = 0, k = 0, gap;
    FREQ_PRESEL  *tmp_rec;
char s[80];


    sprintf(sc_fname, "%s/schannel/%s.%s.%s", 
            emc_dir, emc_uid, o_buss_code.arr, band_code);
    if((sfp = fopen(sc_fname, "w")) == (FILE *) NULL)
    {
        sprintf(err_msg, "Fail to open %s", sc_fname);
        goto force_exit;
    }

    sprintf(err_fname, "%s/schannel/%s.err", emc_dir, emc_uid);
    if((efp = fopen(err_fname, "w")) == (FILE *) NULL)
    {
        sprintf(err_msg, "Fail to open %s", err_fname);
        goto force_exit;
    }

    /****************************/
    /* get weighting factor set */
    /****************************/
    /* EXEC SQL DECLARE C01 CURSOR FOR
         SELECT WEIGHTING
         FROM   WEIGHTING_FACTOR
         WHERE  WEIGHTING_SET = :o_weight_set
         ORDER BY CATEGORY; */ 


    /* EXEC SQL OPEN C01; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 6;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = sq0009;
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )389;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqcmod = (unsigned int )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_weight_set;
    sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    /* EXEC SQL
         FETCH C01
         INTO  :o_weight_traffic; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 6;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )408;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqfoff = (         int )0;
    sqlstm.sqfmod = (unsigned int )2;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_weight_traffic;
    sqlstm.sqhstl[0] = (unsigned long )sizeof(float);
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


    /* EXEC SQL
         FETCH C01
         INTO  :o_weight_sharer; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 6;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )427;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqfoff = (         int )0;
    sqlstm.sqfmod = (unsigned int )2;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_weight_sharer;
    sqlstm.sqhstl[0] = (unsigned long )sizeof(float);
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


    /* EXEC SQL
         FETCH C01
         INTO  :o_weight_buss; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 6;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )446;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqfoff = (         int )0;
    sqlstm.sqfmod = (unsigned int )2;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_weight_buss;
    sqlstm.sqhstl[0] = (unsigned long )sizeof(float);
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


         
    /* EXEC SQL CLOSE C01; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 6;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )465;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}




    /**************************************************/
    /* get frequency boundaries for channel selection */
    /**************************************************/
/*    strcpy(o_band_code.arr, band_code);
    o_band_code.len = strlen(o_band_code.arr); */
    strcpy((char *)o_band_code.arr, band_code);
    o_band_code.len = strlen((char *)o_band_code.arr);

/*
system("echo \"esemsc0f 5\" >> /tmp/debug");
sprintf(msg, "echo \"%s\" >> /tmp/debug", o_band_code.arr );
system(msg);
*/

    /* EXEC SQL
         SELECT BASE_TX_LOW, BASE_TX_HIGH
         INTO   :o_tx_freq_lo, :o_tx_freq_hi
         FROM   FREQUENCY_BAND
         WHERE  BAND_CODE = :o_band_code; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 6;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "select BASE_TX_LOW ,BASE_TX_HIGH into :b0,:b1  from FREQU\
ENCY_BAND where BAND_CODE=:b2";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )480;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_tx_freq_lo;
    sqlstm.sqhstl[0] = (unsigned long )sizeof(double);
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_tx_freq_hi;
    sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqhstv[2] = (unsigned char  *)&o_band_code;
    sqlstm.sqhstl[2] = (unsigned long )12;
    sqlstm.sqhsts[2] = (         int  )0;
    sqlstm.sqindv[2] = (         short *)0;
    sqlstm.sqinds[2] = (         int  )0;
    sqlstm.sqharm[2] = (unsigned long )0;
    sqlstm.sqadto[2] = (unsigned short )0;
    sqlstm.sqtdso[2] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    /********************************************************************/
    /* get assigned date boundary (currently 3 months from system date) */
    /* for channel selection                                            */
    /********************************************************************/
/*    strcpy(o_sys_date.arr, sys_date);
    o_sys_date.len = strlen(o_sys_date.arr); */
    strcpy((char *)o_sys_date.arr, sys_date);
    o_sys_date.len = strlen((char *)o_sys_date.arr);
    /* EXEC SQL 
         SELECT TO_CHAR(ADD_MONTHS(TO_DATE(:o_sys_date, 'DD/MM/YY'), -3))
         INTO   :o_date
         FROM DUMMY; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 6;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "select TO_CHAR(ADD_MONTHS(TO_DATE(:b0,'DD/MM/YY'),(-3))) \
into :b1  from DUMMY ";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )507;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_date;
    sqlstm.sqhstl[0] = (unsigned long )11;
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_date;
    sqlstm.sqhstl[1] = (unsigned long )12;
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    o_date.arr[o_date.len] = '\0';

    /* EXEC SQL DECLARE C05 CURSOR FOR
         SELECT SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, TX_FREQ,
                NVL(RX_FREQ, 0.0), TO_CHAR(ASSIGN_DATE, 'DD/MM/YY'),
                NVL(TO_CHAR(CANCEL_DATE), '-')
         FROM   ASSIGN_CH
         WHERE  TX_FREQ between :o_tx_freq_lo and :o_tx_freq_hi
         AND    ASSIGN_DATE <= TO_DATE(:o_date)
         ORDER BY TX_FREQ, SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX; */ 


    /* EXEC SQL OPEN C05; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 6;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = sq0012;
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )530;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqcmod = (unsigned int )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_tx_freq_lo;
    sqlstm.sqhstl[0] = (unsigned long )sizeof(double);
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_tx_freq_hi;
    sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqhstv[2] = (unsigned char  *)&o_date;
    sqlstm.sqhstl[2] = (unsigned long )12;
    sqlstm.sqhsts[2] = (         int  )0;
    sqlstm.sqindv[2] = (         short *)0;
    sqlstm.sqinds[2] = (         int  )0;
    sqlstm.sqharm[2] = (unsigned long )0;
    sqlstm.sqadto[2] = (unsigned short )0;
    sqlstm.sqtdso[2] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    /* EXEC SQL
         FETCH C05
         INTO  :o_sys_category, :o_sys_type, :o_sys_no, :o_sys_suffix,
               :o_tx_freq, :o_rx_freq, :o_assign_date, :o_ac_cancel_date; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 8;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )557;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqfoff = (         int )0;
    sqlstm.sqfmod = (unsigned int )2;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
    sqlstm.sqhstl[0] = (unsigned long )1;
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
    sqlstm.sqhstl[1] = (unsigned long )5;
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
    sqlstm.sqhstl[2] = (unsigned long )10;
    sqlstm.sqhsts[2] = (         int  )0;
    sqlstm.sqindv[2] = (         short *)0;
    sqlstm.sqinds[2] = (         int  )0;
    sqlstm.sqharm[2] = (unsigned long )0;
    sqlstm.sqadto[2] = (unsigned short )0;
    sqlstm.sqtdso[2] = (unsigned short )0;
    sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
    sqlstm.sqhstl[3] = (unsigned long )6;
    sqlstm.sqhsts[3] = (         int  )0;
    sqlstm.sqindv[3] = (         short *)0;
    sqlstm.sqinds[3] = (         int  )0;
    sqlstm.sqharm[3] = (unsigned long )0;
    sqlstm.sqadto[3] = (unsigned short )0;
    sqlstm.sqtdso[3] = (unsigned short )0;
    sqlstm.sqhstv[4] = (unsigned char  *)&o_tx_freq;
    sqlstm.sqhstl[4] = (unsigned long )sizeof(double);
    sqlstm.sqhsts[4] = (         int  )0;
    sqlstm.sqindv[4] = (         short *)0;
    sqlstm.sqinds[4] = (         int  )0;
    sqlstm.sqharm[4] = (unsigned long )0;
    sqlstm.sqadto[4] = (unsigned short )0;
    sqlstm.sqtdso[4] = (unsigned short )0;
    sqlstm.sqhstv[5] = (unsigned char  *)&o_rx_freq;
    sqlstm.sqhstl[5] = (unsigned long )sizeof(double);
    sqlstm.sqhsts[5] = (         int  )0;
    sqlstm.sqindv[5] = (         short *)0;
    sqlstm.sqinds[5] = (         int  )0;
    sqlstm.sqharm[5] = (unsigned long )0;
    sqlstm.sqadto[5] = (unsigned short )0;
    sqlstm.sqtdso[5] = (unsigned short )0;
    sqlstm.sqhstv[6] = (unsigned char  *)&o_assign_date;
    sqlstm.sqhstl[6] = (unsigned long )11;
    sqlstm.sqhsts[6] = (         int  )0;
    sqlstm.sqindv[6] = (         short *)0;
    sqlstm.sqinds[6] = (         int  )0;
    sqlstm.sqharm[6] = (unsigned long )0;
    sqlstm.sqadto[6] = (unsigned short )0;
    sqlstm.sqtdso[6] = (unsigned short )0;
    sqlstm.sqhstv[7] = (unsigned char  *)&o_ac_cancel_date;
    sqlstm.sqhstl[7] = (unsigned long )12;
    sqlstm.sqhsts[7] = (         int  )0;
    sqlstm.sqindv[7] = (         short *)0;
    sqlstm.sqinds[7] = (         int  )0;
    sqlstm.sqharm[7] = (unsigned long )0;
    sqlstm.sqadto[7] = (unsigned short )0;
    sqlstm.sqtdso[7] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    if (sqlca.sqlcode == NOT_FOUND)
    {
        sprintf(err_msg, "No assigned channels found");
        /* EXEC SQL CLOSE C05; */ 

{
        struct sqlexd sqlstm;
        sqlstm.sqlvsn = 13;
        sqlstm.arrsiz = 8;
        sqlstm.sqladtp = &sqladt;
        sqlstm.sqltdsp = &sqltds;
        sqlstm.iters = (unsigned int  )1;
        sqlstm.offset = (unsigned int  )604;
        sqlstm.cud = sqlcud0;
        sqlstm.sqlest = (unsigned char  *)&sqlca;
        sqlstm.sqlety = (unsigned short)4352;
        sqlstm.occurs = (unsigned int  )0;
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


        fclose(efp); fclose(sfp);
        return NOT_FOUND;
    }

    for (i = 0; i < MAX_CHANNEL_NO; i++)
        t_rec[i] = NULL;

    i = 0;
    prev_sys_id[0] = '\0';

    for (; ;)
    {
        o_sys_type.arr[o_sys_type.len] = '\0';
        o_sys_no.arr[o_sys_no.len] = '\0';
        o_sys_suffix.arr[o_sys_suffix.len] = '\0';

        /*****************************************************/
        /* if this is a cancelled channel, skip it           */
        /*****************************************************/
        if (o_ac_cancel_date.arr[0] != '-')
        {
            /* EXEC SQL
                 FETCH C05
                 INTO  :o_sys_category, :o_sys_type, :o_sys_no, :o_sys_suffix,
                       :o_tx_freq, :o_rx_freq, :o_assign_date, :o_ac_cancel_date; */ 

{
            struct sqlexd sqlstm;
            sqlstm.sqlvsn = 13;
            sqlstm.arrsiz = 8;
            sqlstm.sqladtp = &sqladt;
            sqlstm.sqltdsp = &sqltds;
            sqlstm.iters = (unsigned int  )1;
            sqlstm.offset = (unsigned int  )619;
            sqlstm.selerr = (unsigned short)1;
            sqlstm.sqlpfmem = (unsigned int  )0;
            sqlstm.cud = sqlcud0;
            sqlstm.sqlest = (unsigned char  *)&sqlca;
            sqlstm.sqlety = (unsigned short)4352;
            sqlstm.occurs = (unsigned int  )0;
            sqlstm.sqfoff = (         int )0;
            sqlstm.sqfmod = (unsigned int )2;
            sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
            sqlstm.sqhstl[0] = (unsigned long )1;
            sqlstm.sqhsts[0] = (         int  )0;
            sqlstm.sqindv[0] = (         short *)0;
            sqlstm.sqinds[0] = (         int  )0;
            sqlstm.sqharm[0] = (unsigned long )0;
            sqlstm.sqadto[0] = (unsigned short )0;
            sqlstm.sqtdso[0] = (unsigned short )0;
            sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
            sqlstm.sqhstl[1] = (unsigned long )5;
            sqlstm.sqhsts[1] = (         int  )0;
            sqlstm.sqindv[1] = (         short *)0;
            sqlstm.sqinds[1] = (         int  )0;
            sqlstm.sqharm[1] = (unsigned long )0;
            sqlstm.sqadto[1] = (unsigned short )0;
            sqlstm.sqtdso[1] = (unsigned short )0;
            sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
            sqlstm.sqhstl[2] = (unsigned long )10;
            sqlstm.sqhsts[2] = (         int  )0;
            sqlstm.sqindv[2] = (         short *)0;
            sqlstm.sqinds[2] = (         int  )0;
            sqlstm.sqharm[2] = (unsigned long )0;
            sqlstm.sqadto[2] = (unsigned short )0;
            sqlstm.sqtdso[2] = (unsigned short )0;
            sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
            sqlstm.sqhstl[3] = (unsigned long )6;
            sqlstm.sqhsts[3] = (         int  )0;
            sqlstm.sqindv[3] = (         short *)0;
            sqlstm.sqinds[3] = (         int  )0;
            sqlstm.sqharm[3] = (unsigned long )0;
            sqlstm.sqadto[3] = (unsigned short )0;
            sqlstm.sqtdso[3] = (unsigned short )0;
            sqlstm.sqhstv[4] = (unsigned char  *)&o_tx_freq;
            sqlstm.sqhstl[4] = (unsigned long )sizeof(double);
            sqlstm.sqhsts[4] = (         int  )0;
            sqlstm.sqindv[4] = (         short *)0;
            sqlstm.sqinds[4] = (         int  )0;
            sqlstm.sqharm[4] = (unsigned long )0;
            sqlstm.sqadto[4] = (unsigned short )0;
            sqlstm.sqtdso[4] = (unsigned short )0;
            sqlstm.sqhstv[5] = (unsigned char  *)&o_rx_freq;
            sqlstm.sqhstl[5] = (unsigned long )sizeof(double);
            sqlstm.sqhsts[5] = (         int  )0;
            sqlstm.sqindv[5] = (         short *)0;
            sqlstm.sqinds[5] = (         int  )0;
            sqlstm.sqharm[5] = (unsigned long )0;
            sqlstm.sqadto[5] = (unsigned short )0;
            sqlstm.sqtdso[5] = (unsigned short )0;
            sqlstm.sqhstv[6] = (unsigned char  *)&o_assign_date;
            sqlstm.sqhstl[6] = (unsigned long )11;
            sqlstm.sqhsts[6] = (         int  )0;
            sqlstm.sqindv[6] = (         short *)0;
            sqlstm.sqinds[6] = (         int  )0;
            sqlstm.sqharm[6] = (unsigned long )0;
            sqlstm.sqadto[6] = (unsigned short )0;
            sqlstm.sqtdso[6] = (unsigned short )0;
            sqlstm.sqhstv[7] = (unsigned char  *)&o_ac_cancel_date;
            sqlstm.sqhstl[7] = (unsigned long )12;
            sqlstm.sqhsts[7] = (         int  )0;
            sqlstm.sqindv[7] = (         short *)0;
            sqlstm.sqinds[7] = (         int  )0;
            sqlstm.sqharm[7] = (unsigned long )0;
            sqlstm.sqadto[7] = (unsigned short )0;
            sqlstm.sqtdso[7] = (unsigned short )0;
            sqlstm.sqphsv = sqlstm.sqhstv;
            sqlstm.sqphsl = sqlstm.sqhstl;
            sqlstm.sqphss = sqlstm.sqhsts;
            sqlstm.sqpind = sqlstm.sqindv;
            sqlstm.sqpins = sqlstm.sqinds;
            sqlstm.sqparm = sqlstm.sqharm;
            sqlstm.sqparc = sqlstm.sqharc;
            sqlstm.sqpadto = sqlstm.sqadto;
            sqlstm.sqptdso = sqlstm.sqtdso;
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



            if (sqlca.sqlcode == NOT_FOUND)
            {
                if (t_rec[i] != (FREQ_PRESEL *)NULL)
                {
                    t_rec[i]->sharer_cnt = j;
                    t_rec[i]->same_buss_cnt = k;
                    max_sharer_cnt = max(max_sharer_cnt, t_rec[i]->sharer_cnt);
                    max_buss_cnt = max(max_buss_cnt, t_rec[i]->same_buss_cnt);
                    i++;
                }

                /* EXEC SQL CLOSE C05; */ 

{
                struct sqlexd sqlstm;
                sqlstm.sqlvsn = 13;
                sqlstm.arrsiz = 8;
                sqlstm.sqladtp = &sqladt;
                sqlstm.sqltdsp = &sqltds;
                sqlstm.iters = (unsigned int  )1;
                sqlstm.offset = (unsigned int  )666;
                sqlstm.cud = sqlcud0;
                sqlstm.sqlest = (unsigned char  *)&sqlca;
                sqlstm.sqlety = (unsigned short)4352;
                sqlstm.occurs = (unsigned int  )0;
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


                break;
            }

            continue;
        }

        /*****************************************************/
        /* This is done only if a new channel is encountered */
        /*****************************************************/
        if ((abs(o_tx_freq - prev_tx_freq) > FREQ_EPSILON) 
        ||  (abs(o_rx_freq - prev_rx_freq) > FREQ_EPSILON))
        {
            o1_tx_freq_lo = o_tx_freq - FREQ_EPSILON;
            o1_tx_freq_hi = o_tx_freq + FREQ_EPSILON;
            o1_rx_freq_lo = o_rx_freq - FREQ_EPSILON;
            o1_rx_freq_hi = o_rx_freq + FREQ_EPSILON;

            if (abs(o_rx_freq) < FREQ_EPSILON)
            {
                /* EXEC SQL
                     SELECT COUNT(UNIQUE MOBILE_NO)
                     INTO   :o_mobile_cnt
                     FROM   MOBILE_CH
                     WHERE  RX_FREQ between :o1_tx_freq_lo and :o1_tx_freq_hi
                     AND    TX_FREQ IS NULL
                     AND    CANCEL_DATE IS NULL; */ 

{
                struct sqlexd sqlstm;
                sqlstm.sqlvsn = 13;
                sqlstm.arrsiz = 8;
                sqlstm.sqladtp = &sqladt;
                sqlstm.sqltdsp = &sqltds;
                sqlstm.stmt = "select count(unique MOBILE_NO) into :b0  from\
 MOBILE_CH where ((RX_FREQ between :b1 and :b2 and TX_FREQ is null ) and CANCE\
L_DATE is null )";
                sqlstm.iters = (unsigned int  )1;
                sqlstm.offset = (unsigned int  )681;
                sqlstm.selerr = (unsigned short)1;
                sqlstm.sqlpfmem = (unsigned int  )0;
                sqlstm.cud = sqlcud0;
                sqlstm.sqlest = (unsigned char  *)&sqlca;
                sqlstm.sqlety = (unsigned short)4352;
                sqlstm.occurs = (unsigned int  )0;
                sqlstm.sqhstv[0] = (unsigned char  *)&o_mobile_cnt;
                sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
                sqlstm.sqhsts[0] = (         int  )0;
                sqlstm.sqindv[0] = (         short *)0;
                sqlstm.sqinds[0] = (         int  )0;
                sqlstm.sqharm[0] = (unsigned long )0;
                sqlstm.sqadto[0] = (unsigned short )0;
                sqlstm.sqtdso[0] = (unsigned short )0;
                sqlstm.sqhstv[1] = (unsigned char  *)&o1_tx_freq_lo;
                sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
                sqlstm.sqhsts[1] = (         int  )0;
                sqlstm.sqindv[1] = (         short *)0;
                sqlstm.sqinds[1] = (         int  )0;
                sqlstm.sqharm[1] = (unsigned long )0;
                sqlstm.sqadto[1] = (unsigned short )0;
                sqlstm.sqtdso[1] = (unsigned short )0;
                sqlstm.sqhstv[2] = (unsigned char  *)&o1_tx_freq_hi;
                sqlstm.sqhstl[2] = (unsigned long )sizeof(double);
                sqlstm.sqhsts[2] = (         int  )0;
                sqlstm.sqindv[2] = (         short *)0;
                sqlstm.sqinds[2] = (         int  )0;
                sqlstm.sqharm[2] = (unsigned long )0;
                sqlstm.sqadto[2] = (unsigned short )0;
                sqlstm.sqtdso[2] = (unsigned short )0;
                sqlstm.sqphsv = sqlstm.sqhstv;
                sqlstm.sqphsl = sqlstm.sqhstl;
                sqlstm.sqphss = sqlstm.sqhsts;
                sqlstm.sqpind = sqlstm.sqindv;
                sqlstm.sqpins = sqlstm.sqinds;
                sqlstm.sqparm = sqlstm.sqharm;
                sqlstm.sqparc = sqlstm.sqharc;
                sqlstm.sqpadto = sqlstm.sqadto;
                sqlstm.sqptdso = sqlstm.sqtdso;
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


            }
            else
            {
                /* EXEC SQL
                     SELECT COUNT(UNIQUE MOBILE_NO)
                     INTO   :o_mobile_cnt
                     FROM   MOBILE_CH
                     WHERE  RX_FREQ between :o1_tx_freq_lo and :o1_tx_freq_hi
                     AND    TX_FREQ between :o1_rx_freq_lo and :o1_rx_freq_hi
                     AND    CANCEL_DATE IS NULL; */ 

{
                struct sqlexd sqlstm;
                sqlstm.sqlvsn = 13;
                sqlstm.arrsiz = 8;
                sqlstm.sqladtp = &sqladt;
                sqlstm.sqltdsp = &sqltds;
                sqlstm.stmt = "select count(unique MOBILE_NO) into :b0  from\
 MOBILE_CH where ((RX_FREQ between :b1 and :b2 and TX_FREQ between :b3 and :b4\
) and CANCEL_DATE is null )";
                sqlstm.iters = (unsigned int  )1;
                sqlstm.offset = (unsigned int  )708;
                sqlstm.selerr = (unsigned short)1;
                sqlstm.sqlpfmem = (unsigned int  )0;
                sqlstm.cud = sqlcud0;
                sqlstm.sqlest = (unsigned char  *)&sqlca;
                sqlstm.sqlety = (unsigned short)4352;
                sqlstm.occurs = (unsigned int  )0;
                sqlstm.sqhstv[0] = (unsigned char  *)&o_mobile_cnt;
                sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
                sqlstm.sqhsts[0] = (         int  )0;
                sqlstm.sqindv[0] = (         short *)0;
                sqlstm.sqinds[0] = (         int  )0;
                sqlstm.sqharm[0] = (unsigned long )0;
                sqlstm.sqadto[0] = (unsigned short )0;
                sqlstm.sqtdso[0] = (unsigned short )0;
                sqlstm.sqhstv[1] = (unsigned char  *)&o1_tx_freq_lo;
                sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
                sqlstm.sqhsts[1] = (         int  )0;
                sqlstm.sqindv[1] = (         short *)0;
                sqlstm.sqinds[1] = (         int  )0;
                sqlstm.sqharm[1] = (unsigned long )0;
                sqlstm.sqadto[1] = (unsigned short )0;
                sqlstm.sqtdso[1] = (unsigned short )0;
                sqlstm.sqhstv[2] = (unsigned char  *)&o1_tx_freq_hi;
                sqlstm.sqhstl[2] = (unsigned long )sizeof(double);
                sqlstm.sqhsts[2] = (         int  )0;
                sqlstm.sqindv[2] = (         short *)0;
                sqlstm.sqinds[2] = (         int  )0;
                sqlstm.sqharm[2] = (unsigned long )0;
                sqlstm.sqadto[2] = (unsigned short )0;
                sqlstm.sqtdso[2] = (unsigned short )0;
                sqlstm.sqhstv[3] = (unsigned char  *)&o1_rx_freq_lo;
                sqlstm.sqhstl[3] = (unsigned long )sizeof(double);
                sqlstm.sqhsts[3] = (         int  )0;
                sqlstm.sqindv[3] = (         short *)0;
                sqlstm.sqinds[3] = (         int  )0;
                sqlstm.sqharm[3] = (unsigned long )0;
                sqlstm.sqadto[3] = (unsigned short )0;
                sqlstm.sqtdso[3] = (unsigned short )0;
                sqlstm.sqhstv[4] = (unsigned char  *)&o1_rx_freq_hi;
                sqlstm.sqhstl[4] = (unsigned long )sizeof(double);
                sqlstm.sqhsts[4] = (         int  )0;
                sqlstm.sqindv[4] = (         short *)0;
                sqlstm.sqinds[4] = (         int  )0;
                sqlstm.sqharm[4] = (unsigned long )0;
                sqlstm.sqadto[4] = (unsigned short )0;
                sqlstm.sqtdso[4] = (unsigned short )0;
                sqlstm.sqphsv = sqlstm.sqhstv;
                sqlstm.sqphsl = sqlstm.sqhstl;
                sqlstm.sqphss = sqlstm.sqhsts;
                sqlstm.sqpind = sqlstm.sqindv;
                sqlstm.sqpins = sqlstm.sqinds;
                sqlstm.sqparm = sqlstm.sqharm;
                sqlstm.sqparc = sqlstm.sqharc;
                sqlstm.sqpadto = sqlstm.sqadto;
                sqlstm.sqptdso = sqlstm.sqtdso;
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


            }
/*
                 WHERE  TX_FREQ = :o_rx_freq
                 AND    RX_FREQ = :o_tx_freq
                 AND    CANCEL_DATE IS NULL;
sprintf(s,"mobile cnt: %d", o_mobile_cnt);
mvaddstr(17,0,s);refresh();getch();
*/

/*
            if ((o_mobile_cnt > 0) && (t_rec[i] == (FREQ_PRESEL *)NULL))
*/
            if (o_mobile_cnt > 0)
            {
                t_rec[i] = (FREQ_PRESEL *) malloc(sizeof(FREQ_PRESEL));
                if (t_rec[i] == (FREQ_PRESEL *) NULL)
                {
                    sprintf(err_msg, "Cannot allocate space for FREQ_PRESEL");
                    fclose(efp); fclose(sfp);
                    goto force_exit;
                }

/*
system("echo \"esemsc0f 6\" >> /tmp/debug");
sprintf(msg, "echo \"%f %f\" >> /tmp/debug", o1_tx_freq_lo, o1_tx_freq_hi );
system(msg);
*/

                /* EXEC SQL
                     SELECT LOAD_PER_MOBILE, TO_CHAR(LAST_UPDATE, 'DD/MM/YY')
                     INTO  :o_load_per_mobile, :o_measure_date
                     FROM   CHANNEL_TRAFFIC
                     WHERE  TX_FREQ between :o1_tx_freq_lo and :o1_tx_freq_hi; */ 

{
                struct sqlexd sqlstm;
                sqlstm.sqlvsn = 13;
                sqlstm.arrsiz = 8;
                sqlstm.sqladtp = &sqladt;
                sqlstm.sqltdsp = &sqltds;
                sqlstm.stmt = "select LOAD_PER_MOBILE ,TO_CHAR(LAST_UPDATE,'\
DD/MM/YY') into :b0,:b1  from CHANNEL_TRAFFIC where TX_FREQ between :b2 and :b\
3";
                sqlstm.iters = (unsigned int  )1;
                sqlstm.offset = (unsigned int  )743;
                sqlstm.selerr = (unsigned short)1;
                sqlstm.sqlpfmem = (unsigned int  )0;
                sqlstm.cud = sqlcud0;
                sqlstm.sqlest = (unsigned char  *)&sqlca;
                sqlstm.sqlety = (unsigned short)4352;
                sqlstm.occurs = (unsigned int  )0;
                sqlstm.sqhstv[0] = (unsigned char  *)&o_load_per_mobile;
                sqlstm.sqhstl[0] = (unsigned long )sizeof(float);
                sqlstm.sqhsts[0] = (         int  )0;
                sqlstm.sqindv[0] = (         short *)0;
                sqlstm.sqinds[0] = (         int  )0;
                sqlstm.sqharm[0] = (unsigned long )0;
                sqlstm.sqadto[0] = (unsigned short )0;
                sqlstm.sqtdso[0] = (unsigned short )0;
                sqlstm.sqhstv[1] = (unsigned char  *)&o_measure_date;
                sqlstm.sqhstl[1] = (unsigned long )11;
                sqlstm.sqhsts[1] = (         int  )0;
                sqlstm.sqindv[1] = (         short *)0;
                sqlstm.sqinds[1] = (         int  )0;
                sqlstm.sqharm[1] = (unsigned long )0;
                sqlstm.sqadto[1] = (unsigned short )0;
                sqlstm.sqtdso[1] = (unsigned short )0;
                sqlstm.sqhstv[2] = (unsigned char  *)&o1_tx_freq_lo;
                sqlstm.sqhstl[2] = (unsigned long )sizeof(double);
                sqlstm.sqhsts[2] = (         int  )0;
                sqlstm.sqindv[2] = (         short *)0;
                sqlstm.sqinds[2] = (         int  )0;
                sqlstm.sqharm[2] = (unsigned long )0;
                sqlstm.sqadto[2] = (unsigned short )0;
                sqlstm.sqtdso[2] = (unsigned short )0;
                sqlstm.sqhstv[3] = (unsigned char  *)&o1_tx_freq_hi;
                sqlstm.sqhstl[3] = (unsigned long )sizeof(double);
                sqlstm.sqhsts[3] = (         int  )0;
                sqlstm.sqindv[3] = (         short *)0;
                sqlstm.sqinds[3] = (         int  )0;
                sqlstm.sqharm[3] = (unsigned long )0;
                sqlstm.sqadto[3] = (unsigned short )0;
                sqlstm.sqtdso[3] = (unsigned short )0;
                sqlstm.sqphsv = sqlstm.sqhstv;
                sqlstm.sqphsl = sqlstm.sqhstl;
                sqlstm.sqphss = sqlstm.sqhsts;
                sqlstm.sqpind = sqlstm.sqindv;
                sqlstm.sqpins = sqlstm.sqinds;
                sqlstm.sqparm = sqlstm.sqharm;
                sqlstm.sqparc = sqlstm.sqharc;
                sqlstm.sqpadto = sqlstm.sqadto;
                sqlstm.sqptdso = sqlstm.sqtdso;
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


/*
disp_space(17, 0, 80);
sprintf(s,"traffic: %d", o_load_per_mobile);
mvaddstr(17,0,s);refresh();getch();
disp_space(17, 0, 80);
*/
                if (sqlca.sqlcode == NOT_FOUND)
                {
                    o_load_per_mobile = .005;
/*                    strcpy(o_measure_date.arr, "--------"); */
                    strcpy((char *)o_measure_date.arr, "--------");
                }
                else
                    o_measure_date.arr[o_measure_date.len] = '\0';

/*                strcpy(t_rec[i]->measure_date, o_measure_date.arr); */
                strcpy(t_rec[i]->measure_date, (char *)o_measure_date.arr);
                t_rec[i]->tx_freq = o_tx_freq;
                t_rec[i]->rx_freq = o_rx_freq;
                t_rec[i]->no_of_mobiles = o_mobile_cnt;
                t_rec[i]->traffic = o_load_per_mobile * o_mobile_cnt;
                prev_tx_freq = o_tx_freq;
                prev_rx_freq = o_rx_freq;
            }
            else
            {
                err_flag = TRUE;
                fprintf(efp, "No mobile channel for %lf/%lf\n", 
                        o_tx_freq, o_rx_freq);
            }
        }

        if (t_rec[i] != (FREQ_PRESEL *)NULL)
        {
            register int    l;
/*
sprintf(s,"%d %lf %lf %d %f     ", i, o_tx_freq, o_rx_freq, o_mobile_cnt, o_load_per_mobile);
mvaddstr(23,0,s);refresh();getch();
sprintf(s,"system id: %s", t_rec[i]->system_id[j]);
mvaddstr(17,0,s);refresh();getch();
disp_space(17, 0, 80);
sprintf(s,"buss code: %s", t_rec[i]->buss_code[j]);
mvaddstr(17,0,s);refresh();getch();
disp_space(17, 0, 80);
*/
            o1_tx_freq_lo = o_tx_freq - FREQ_EPSILON;
            o1_tx_freq_hi = o_tx_freq + FREQ_EPSILON;

            if (abs(o_rx_freq) < FREQ_EPSILON)
            {
                /* EXEC SQL DECLARE C060 CURSOR FOR
                     SELECT BASE_NO
                     FROM   BASE_CH
                     WHERE  TX_FREQ between :o1_tx_freq_lo and :o1_tx_freq_hi
                     AND    RX_FREQ IS NULL
                     AND    SYS_CATEGORY = :o_sys_category
                     AND    SYS_TYPE = :o_sys_type
                     AND    SYS_NO = :o_sys_no
                     AND    SYS_SUFFIX = :o_sys_suffix; */ 


                /* EXEC SQL OPEN C060; */ 

{
                struct sqlexd sqlstm;
                sqlstm.sqlvsn = 13;
                sqlstm.arrsiz = 8;
                sqlstm.sqladtp = &sqladt;
                sqlstm.sqltdsp = &sqltds;
                sqlstm.stmt = sq0016;
                sqlstm.iters = (unsigned int  )1;
                sqlstm.offset = (unsigned int  )774;
                sqlstm.selerr = (unsigned short)1;
                sqlstm.sqlpfmem = (unsigned int  )0;
                sqlstm.cud = sqlcud0;
                sqlstm.sqlest = (unsigned char  *)&sqlca;
                sqlstm.sqlety = (unsigned short)4352;
                sqlstm.occurs = (unsigned int  )0;
                sqlstm.sqcmod = (unsigned int )0;
                sqlstm.sqhstv[0] = (unsigned char  *)&o1_tx_freq_lo;
                sqlstm.sqhstl[0] = (unsigned long )sizeof(double);
                sqlstm.sqhsts[0] = (         int  )0;
                sqlstm.sqindv[0] = (         short *)0;
                sqlstm.sqinds[0] = (         int  )0;
                sqlstm.sqharm[0] = (unsigned long )0;
                sqlstm.sqadto[0] = (unsigned short )0;
                sqlstm.sqtdso[0] = (unsigned short )0;
                sqlstm.sqhstv[1] = (unsigned char  *)&o1_tx_freq_hi;
                sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
                sqlstm.sqhsts[1] = (         int  )0;
                sqlstm.sqindv[1] = (         short *)0;
                sqlstm.sqinds[1] = (         int  )0;
                sqlstm.sqharm[1] = (unsigned long )0;
                sqlstm.sqadto[1] = (unsigned short )0;
                sqlstm.sqtdso[1] = (unsigned short )0;
                sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_category;
                sqlstm.sqhstl[2] = (unsigned long )1;
                sqlstm.sqhsts[2] = (         int  )0;
                sqlstm.sqindv[2] = (         short *)0;
                sqlstm.sqinds[2] = (         int  )0;
                sqlstm.sqharm[2] = (unsigned long )0;
                sqlstm.sqadto[2] = (unsigned short )0;
                sqlstm.sqtdso[2] = (unsigned short )0;
                sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_type;
                sqlstm.sqhstl[3] = (unsigned long )5;
                sqlstm.sqhsts[3] = (         int  )0;
                sqlstm.sqindv[3] = (         short *)0;
                sqlstm.sqinds[3] = (         int  )0;
                sqlstm.sqharm[3] = (unsigned long )0;
                sqlstm.sqadto[3] = (unsigned short )0;
                sqlstm.sqtdso[3] = (unsigned short )0;
                sqlstm.sqhstv[4] = (unsigned char  *)&o_sys_no;
                sqlstm.sqhstl[4] = (unsigned long )10;
                sqlstm.sqhsts[4] = (         int  )0;
                sqlstm.sqindv[4] = (         short *)0;
                sqlstm.sqinds[4] = (         int  )0;
                sqlstm.sqharm[4] = (unsigned long )0;
                sqlstm.sqadto[4] = (unsigned short )0;
                sqlstm.sqtdso[4] = (unsigned short )0;
                sqlstm.sqhstv[5] = (unsigned char  *)&o_sys_suffix;
                sqlstm.sqhstl[5] = (unsigned long )6;
                sqlstm.sqhsts[5] = (         int  )0;
                sqlstm.sqindv[5] = (         short *)0;
                sqlstm.sqinds[5] = (         int  )0;
                sqlstm.sqharm[5] = (unsigned long )0;
                sqlstm.sqadto[5] = (unsigned short )0;
                sqlstm.sqtdso[5] = (unsigned short )0;
                sqlstm.sqphsv = sqlstm.sqhstv;
                sqlstm.sqphsl = sqlstm.sqhstl;
                sqlstm.sqphss = sqlstm.sqhsts;
                sqlstm.sqpind = sqlstm.sqindv;
                sqlstm.sqpins = sqlstm.sqinds;
                sqlstm.sqparm = sqlstm.sqharm;
                sqlstm.sqparc = sqlstm.sqharc;
                sqlstm.sqpadto = sqlstm.sqadto;
                sqlstm.sqptdso = sqlstm.sqtdso;
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


                /* EXEC SQL FETCH C060 
                     INTO   :o_base_no; */ 

{
                struct sqlexd sqlstm;
                sqlstm.sqlvsn = 13;
                sqlstm.arrsiz = 8;
                sqlstm.sqladtp = &sqladt;
                sqlstm.sqltdsp = &sqltds;
                sqlstm.iters = (unsigned int  )1;
                sqlstm.offset = (unsigned int  )813;
                sqlstm.selerr = (unsigned short)1;
                sqlstm.sqlpfmem = (unsigned int  )0;
                sqlstm.cud = sqlcud0;
                sqlstm.sqlest = (unsigned char  *)&sqlca;
                sqlstm.sqlety = (unsigned short)4352;
                sqlstm.occurs = (unsigned int  )0;
                sqlstm.sqfoff = (         int )0;
                sqlstm.sqfmod = (unsigned int )2;
                sqlstm.sqhstv[0] = (unsigned char  *)&o_base_no;
                sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
                sqlstm.sqhsts[0] = (         int  )0;
                sqlstm.sqindv[0] = (         short *)0;
                sqlstm.sqinds[0] = (         int  )0;
                sqlstm.sqharm[0] = (unsigned long )0;
                sqlstm.sqadto[0] = (unsigned short )0;
                sqlstm.sqtdso[0] = (unsigned short )0;
                sqlstm.sqphsv = sqlstm.sqhstv;
                sqlstm.sqphsl = sqlstm.sqhstl;
                sqlstm.sqphss = sqlstm.sqhsts;
                sqlstm.sqpind = sqlstm.sqindv;
                sqlstm.sqpins = sqlstm.sqinds;
                sqlstm.sqparm = sqlstm.sqharm;
                sqlstm.sqparc = sqlstm.sqharc;
                sqlstm.sqpadto = sqlstm.sqadto;
                sqlstm.sqptdso = sqlstm.sqtdso;
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


            }
            else
            {
                o1_rx_freq_lo = o_rx_freq - FREQ_EPSILON;
                o1_rx_freq_hi = o_rx_freq + FREQ_EPSILON;
/*
                EXEC SQL DECLARE C061 CURSOR FOR
                     SELECT BASE_NO
                     FROM   BASE_CH
                     WHERE  TX_FREQ between :o1_tx_freq_lo and :o1_tx_freq_hi
                     AND    RX_FREQ between :o1_rx_freq_lo and :o1_rx_freq_hi
                     AND    SYS_CATEGORY = :o_sys_category
                     AND    SYS_TYPE = :o_sys_type
                     AND    SYS_NO = :o_sys_no
                     AND    SYS_SUFFIX = :o_sys_suffix;
*/
                /* EXEC SQL DECLARE C061 CURSOR FOR
                     SELECT X.BASE_NO
                     FROM   BASE_CH X
                     WHERE  X.SYS_CATEGORY = :o_sys_category
                     AND    X.SYS_TYPE = :o_sys_type
                     AND    X.SYS_NO = :o_sys_no
                     AND    X.SYS_SUFFIX = :o_sys_suffix
                     AND    
                     (   (    X.TX_FREQ between :o1_tx_freq_lo and :o1_tx_freq_hi
                          AND X.RX_FREQ between :o1_rx_freq_lo and :o1_rx_freq_hi)
                      OR (    X.TX_FREQ between :o1_tx_freq_lo and :o1_tx_freq_hi
                          AND X.RX_FREQ IS NULL
                          AND EXISTS
                              (SELECT * 
                               FROM   BASE_CH Y
                               WHERE  Y.SYS_CATEGORY = :o_sys_category
                               AND    Y.SYS_TYPE = :o_sys_type
                               AND    Y.SYS_NO = :o_sys_no
                               AND    Y.SYS_SUFFIX = :o_sys_suffix
                               AND    Y.RX_FREQ between :o1_rx_freq_lo and :o1_rx_freq_hi
                               AND    Y.TX_FREQ IS NULL))); */ 


                /* EXEC SQL OPEN C061; */ 

{
                struct sqlexd sqlstm;
                sqlstm.sqlvsn = 13;
                sqlstm.arrsiz = 16;
                sqlstm.sqladtp = &sqladt;
                sqlstm.sqltdsp = &sqltds;
                sqlstm.stmt = sq0017;
                sqlstm.iters = (unsigned int  )1;
                sqlstm.offset = (unsigned int  )832;
                sqlstm.selerr = (unsigned short)1;
                sqlstm.sqlpfmem = (unsigned int  )0;
                sqlstm.cud = sqlcud0;
                sqlstm.sqlest = (unsigned char  *)&sqlca;
                sqlstm.sqlety = (unsigned short)4352;
                sqlstm.occurs = (unsigned int  )0;
                sqlstm.sqcmod = (unsigned int )0;
                sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
                sqlstm.sqhstl[0] = (unsigned long )1;
                sqlstm.sqhsts[0] = (         int  )0;
                sqlstm.sqindv[0] = (         short *)0;
                sqlstm.sqinds[0] = (         int  )0;
                sqlstm.sqharm[0] = (unsigned long )0;
                sqlstm.sqadto[0] = (unsigned short )0;
                sqlstm.sqtdso[0] = (unsigned short )0;
                sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
                sqlstm.sqhstl[1] = (unsigned long )5;
                sqlstm.sqhsts[1] = (         int  )0;
                sqlstm.sqindv[1] = (         short *)0;
                sqlstm.sqinds[1] = (         int  )0;
                sqlstm.sqharm[1] = (unsigned long )0;
                sqlstm.sqadto[1] = (unsigned short )0;
                sqlstm.sqtdso[1] = (unsigned short )0;
                sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
                sqlstm.sqhstl[2] = (unsigned long )10;
                sqlstm.sqhsts[2] = (         int  )0;
                sqlstm.sqindv[2] = (         short *)0;
                sqlstm.sqinds[2] = (         int  )0;
                sqlstm.sqharm[2] = (unsigned long )0;
                sqlstm.sqadto[2] = (unsigned short )0;
                sqlstm.sqtdso[2] = (unsigned short )0;
                sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
                sqlstm.sqhstl[3] = (unsigned long )6;
                sqlstm.sqhsts[3] = (         int  )0;
                sqlstm.sqindv[3] = (         short *)0;
                sqlstm.sqinds[3] = (         int  )0;
                sqlstm.sqharm[3] = (unsigned long )0;
                sqlstm.sqadto[3] = (unsigned short )0;
                sqlstm.sqtdso[3] = (unsigned short )0;
                sqlstm.sqhstv[4] = (unsigned char  *)&o1_tx_freq_lo;
                sqlstm.sqhstl[4] = (unsigned long )sizeof(double);
                sqlstm.sqhsts[4] = (         int  )0;
                sqlstm.sqindv[4] = (         short *)0;
                sqlstm.sqinds[4] = (         int  )0;
                sqlstm.sqharm[4] = (unsigned long )0;
                sqlstm.sqadto[4] = (unsigned short )0;
                sqlstm.sqtdso[4] = (unsigned short )0;
                sqlstm.sqhstv[5] = (unsigned char  *)&o1_tx_freq_hi;
                sqlstm.sqhstl[5] = (unsigned long )sizeof(double);
                sqlstm.sqhsts[5] = (         int  )0;
                sqlstm.sqindv[5] = (         short *)0;
                sqlstm.sqinds[5] = (         int  )0;
                sqlstm.sqharm[5] = (unsigned long )0;
                sqlstm.sqadto[5] = (unsigned short )0;
                sqlstm.sqtdso[5] = (unsigned short )0;
                sqlstm.sqhstv[6] = (unsigned char  *)&o1_rx_freq_lo;
                sqlstm.sqhstl[6] = (unsigned long )sizeof(double);
                sqlstm.sqhsts[6] = (         int  )0;
                sqlstm.sqindv[6] = (         short *)0;
                sqlstm.sqinds[6] = (         int  )0;
                sqlstm.sqharm[6] = (unsigned long )0;
                sqlstm.sqadto[6] = (unsigned short )0;
                sqlstm.sqtdso[6] = (unsigned short )0;
                sqlstm.sqhstv[7] = (unsigned char  *)&o1_rx_freq_hi;
                sqlstm.sqhstl[7] = (unsigned long )sizeof(double);
                sqlstm.sqhsts[7] = (         int  )0;
                sqlstm.sqindv[7] = (         short *)0;
                sqlstm.sqinds[7] = (         int  )0;
                sqlstm.sqharm[7] = (unsigned long )0;
                sqlstm.sqadto[7] = (unsigned short )0;
                sqlstm.sqtdso[7] = (unsigned short )0;
                sqlstm.sqhstv[8] = (unsigned char  *)&o1_tx_freq_lo;
                sqlstm.sqhstl[8] = (unsigned long )sizeof(double);
                sqlstm.sqhsts[8] = (         int  )0;
                sqlstm.sqindv[8] = (         short *)0;
                sqlstm.sqinds[8] = (         int  )0;
                sqlstm.sqharm[8] = (unsigned long )0;
                sqlstm.sqadto[8] = (unsigned short )0;
                sqlstm.sqtdso[8] = (unsigned short )0;
                sqlstm.sqhstv[9] = (unsigned char  *)&o1_tx_freq_hi;
                sqlstm.sqhstl[9] = (unsigned long )sizeof(double);
                sqlstm.sqhsts[9] = (         int  )0;
                sqlstm.sqindv[9] = (         short *)0;
                sqlstm.sqinds[9] = (         int  )0;
                sqlstm.sqharm[9] = (unsigned long )0;
                sqlstm.sqadto[9] = (unsigned short )0;
                sqlstm.sqtdso[9] = (unsigned short )0;
                sqlstm.sqhstv[10] = (unsigned char  *)&o_sys_category;
                sqlstm.sqhstl[10] = (unsigned long )1;
                sqlstm.sqhsts[10] = (         int  )0;
                sqlstm.sqindv[10] = (         short *)0;
                sqlstm.sqinds[10] = (         int  )0;
                sqlstm.sqharm[10] = (unsigned long )0;
                sqlstm.sqadto[10] = (unsigned short )0;
                sqlstm.sqtdso[10] = (unsigned short )0;
                sqlstm.sqhstv[11] = (unsigned char  *)&o_sys_type;
                sqlstm.sqhstl[11] = (unsigned long )5;
                sqlstm.sqhsts[11] = (         int  )0;
                sqlstm.sqindv[11] = (         short *)0;
                sqlstm.sqinds[11] = (         int  )0;
                sqlstm.sqharm[11] = (unsigned long )0;
                sqlstm.sqadto[11] = (unsigned short )0;
                sqlstm.sqtdso[11] = (unsigned short )0;
                sqlstm.sqhstv[12] = (unsigned char  *)&o_sys_no;
                sqlstm.sqhstl[12] = (unsigned long )10;
                sqlstm.sqhsts[12] = (         int  )0;
                sqlstm.sqindv[12] = (         short *)0;
                sqlstm.sqinds[12] = (         int  )0;
                sqlstm.sqharm[12] = (unsigned long )0;
                sqlstm.sqadto[12] = (unsigned short )0;
                sqlstm.sqtdso[12] = (unsigned short )0;
                sqlstm.sqhstv[13] = (unsigned char  *)&o_sys_suffix;
                sqlstm.sqhstl[13] = (unsigned long )6;
                sqlstm.sqhsts[13] = (         int  )0;
                sqlstm.sqindv[13] = (         short *)0;
                sqlstm.sqinds[13] = (         int  )0;
                sqlstm.sqharm[13] = (unsigned long )0;
                sqlstm.sqadto[13] = (unsigned short )0;
                sqlstm.sqtdso[13] = (unsigned short )0;
                sqlstm.sqhstv[14] = (unsigned char  *)&o1_rx_freq_lo;
                sqlstm.sqhstl[14] = (unsigned long )sizeof(double);
                sqlstm.sqhsts[14] = (         int  )0;
                sqlstm.sqindv[14] = (         short *)0;
                sqlstm.sqinds[14] = (         int  )0;
                sqlstm.sqharm[14] = (unsigned long )0;
                sqlstm.sqadto[14] = (unsigned short )0;
                sqlstm.sqtdso[14] = (unsigned short )0;
                sqlstm.sqhstv[15] = (unsigned char  *)&o1_rx_freq_hi;
                sqlstm.sqhstl[15] = (unsigned long )sizeof(double);
                sqlstm.sqhsts[15] = (         int  )0;
                sqlstm.sqindv[15] = (         short *)0;
                sqlstm.sqinds[15] = (         int  )0;
                sqlstm.sqharm[15] = (unsigned long )0;
                sqlstm.sqadto[15] = (unsigned short )0;
                sqlstm.sqtdso[15] = (unsigned short )0;
                sqlstm.sqphsv = sqlstm.sqhstv;
                sqlstm.sqphsl = sqlstm.sqhstl;
                sqlstm.sqphss = sqlstm.sqhsts;
                sqlstm.sqpind = sqlstm.sqindv;
                sqlstm.sqpins = sqlstm.sqinds;
                sqlstm.sqparm = sqlstm.sqharm;
                sqlstm.sqparc = sqlstm.sqharc;
                sqlstm.sqpadto = sqlstm.sqadto;
                sqlstm.sqptdso = sqlstm.sqtdso;
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


                /* EXEC SQL FETCH C061 
                     INTO   :o_base_no; */ 

{
                struct sqlexd sqlstm;
                sqlstm.sqlvsn = 13;
                sqlstm.arrsiz = 16;
                sqlstm.sqladtp = &sqladt;
                sqlstm.sqltdsp = &sqltds;
                sqlstm.iters = (unsigned int  )1;
                sqlstm.offset = (unsigned int  )911;
                sqlstm.selerr = (unsigned short)1;
                sqlstm.sqlpfmem = (unsigned int  )0;
                sqlstm.cud = sqlcud0;
                sqlstm.sqlest = (unsigned char  *)&sqlca;
                sqlstm.sqlety = (unsigned short)4352;
                sqlstm.occurs = (unsigned int  )0;
                sqlstm.sqfoff = (         int )0;
                sqlstm.sqfmod = (unsigned int )2;
                sqlstm.sqhstv[0] = (unsigned char  *)&o_base_no;
                sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
                sqlstm.sqhsts[0] = (         int  )0;
                sqlstm.sqindv[0] = (         short *)0;
                sqlstm.sqinds[0] = (         int  )0;
                sqlstm.sqharm[0] = (unsigned long )0;
                sqlstm.sqadto[0] = (unsigned short )0;
                sqlstm.sqtdso[0] = (unsigned short )0;
                sqlstm.sqphsv = sqlstm.sqhstv;
                sqlstm.sqphsl = sqlstm.sqhstl;
                sqlstm.sqphss = sqlstm.sqhsts;
                sqlstm.sqpind = sqlstm.sqindv;
                sqlstm.sqpins = sqlstm.sqinds;
                sqlstm.sqparm = sqlstm.sqharm;
                sqlstm.sqparc = sqlstm.sqharc;
                sqlstm.sqpadto = sqlstm.sqadto;
                sqlstm.sqptdso = sqlstm.sqtdso;
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


            }

            if (sqlca.sqlcode == NOT_FOUND)
            {
                /* EXEC SQL
                     FETCH C05
                     INTO  :o_sys_category, :o_sys_type, :o_sys_no,
                           :o_sys_suffix, :o_tx_freq, :o_rx_freq,
                           :o_assign_date, :o_ac_cancel_date; */ 

{
                struct sqlexd sqlstm;
                sqlstm.sqlvsn = 13;
                sqlstm.arrsiz = 16;
                sqlstm.sqladtp = &sqladt;
                sqlstm.sqltdsp = &sqltds;
                sqlstm.iters = (unsigned int  )1;
                sqlstm.offset = (unsigned int  )930;
                sqlstm.selerr = (unsigned short)1;
                sqlstm.sqlpfmem = (unsigned int  )0;
                sqlstm.cud = sqlcud0;
                sqlstm.sqlest = (unsigned char  *)&sqlca;
                sqlstm.sqlety = (unsigned short)4352;
                sqlstm.occurs = (unsigned int  )0;
                sqlstm.sqfoff = (         int )0;
                sqlstm.sqfmod = (unsigned int )2;
                sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
                sqlstm.sqhstl[0] = (unsigned long )1;
                sqlstm.sqhsts[0] = (         int  )0;
                sqlstm.sqindv[0] = (         short *)0;
                sqlstm.sqinds[0] = (         int  )0;
                sqlstm.sqharm[0] = (unsigned long )0;
                sqlstm.sqadto[0] = (unsigned short )0;
                sqlstm.sqtdso[0] = (unsigned short )0;
                sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
                sqlstm.sqhstl[1] = (unsigned long )5;
                sqlstm.sqhsts[1] = (         int  )0;
                sqlstm.sqindv[1] = (         short *)0;
                sqlstm.sqinds[1] = (         int  )0;
                sqlstm.sqharm[1] = (unsigned long )0;
                sqlstm.sqadto[1] = (unsigned short )0;
                sqlstm.sqtdso[1] = (unsigned short )0;
                sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
                sqlstm.sqhstl[2] = (unsigned long )10;
                sqlstm.sqhsts[2] = (         int  )0;
                sqlstm.sqindv[2] = (         short *)0;
                sqlstm.sqinds[2] = (         int  )0;
                sqlstm.sqharm[2] = (unsigned long )0;
                sqlstm.sqadto[2] = (unsigned short )0;
                sqlstm.sqtdso[2] = (unsigned short )0;
                sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
                sqlstm.sqhstl[3] = (unsigned long )6;
                sqlstm.sqhsts[3] = (         int  )0;
                sqlstm.sqindv[3] = (         short *)0;
                sqlstm.sqinds[3] = (         int  )0;
                sqlstm.sqharm[3] = (unsigned long )0;
                sqlstm.sqadto[3] = (unsigned short )0;
                sqlstm.sqtdso[3] = (unsigned short )0;
                sqlstm.sqhstv[4] = (unsigned char  *)&o_tx_freq;
                sqlstm.sqhstl[4] = (unsigned long )sizeof(double);
                sqlstm.sqhsts[4] = (         int  )0;
                sqlstm.sqindv[4] = (         short *)0;
                sqlstm.sqinds[4] = (         int  )0;
                sqlstm.sqharm[4] = (unsigned long )0;
                sqlstm.sqadto[4] = (unsigned short )0;
                sqlstm.sqtdso[4] = (unsigned short )0;
                sqlstm.sqhstv[5] = (unsigned char  *)&o_rx_freq;
                sqlstm.sqhstl[5] = (unsigned long )sizeof(double);
                sqlstm.sqhsts[5] = (         int  )0;
                sqlstm.sqindv[5] = (         short *)0;
                sqlstm.sqinds[5] = (         int  )0;
                sqlstm.sqharm[5] = (unsigned long )0;
                sqlstm.sqadto[5] = (unsigned short )0;
                sqlstm.sqtdso[5] = (unsigned short )0;
                sqlstm.sqhstv[6] = (unsigned char  *)&o_assign_date;
                sqlstm.sqhstl[6] = (unsigned long )11;
                sqlstm.sqhsts[6] = (         int  )0;
                sqlstm.sqindv[6] = (         short *)0;
                sqlstm.sqinds[6] = (         int  )0;
                sqlstm.sqharm[6] = (unsigned long )0;
                sqlstm.sqadto[6] = (unsigned short )0;
                sqlstm.sqtdso[6] = (unsigned short )0;
                sqlstm.sqhstv[7] = (unsigned char  *)&o_ac_cancel_date;
                sqlstm.sqhstl[7] = (unsigned long )12;
                sqlstm.sqhsts[7] = (         int  )0;
                sqlstm.sqindv[7] = (         short *)0;
                sqlstm.sqinds[7] = (         int  )0;
                sqlstm.sqharm[7] = (unsigned long )0;
                sqlstm.sqadto[7] = (unsigned short )0;
                sqlstm.sqtdso[7] = (unsigned short )0;
                sqlstm.sqphsv = sqlstm.sqhstv;
                sqlstm.sqphsl = sqlstm.sqhstl;
                sqlstm.sqphss = sqlstm.sqhsts;
                sqlstm.sqpind = sqlstm.sqindv;
                sqlstm.sqpins = sqlstm.sqinds;
                sqlstm.sqparm = sqlstm.sqharm;
                sqlstm.sqparc = sqlstm.sqharc;
                sqlstm.sqpadto = sqlstm.sqadto;
                sqlstm.sqptdso = sqlstm.sqtdso;
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



                if (sqlca.sqlcode == NOT_FOUND)
                {
                    if (t_rec[i] != (FREQ_PRESEL *)NULL)
                    {
                        t_rec[i]->sharer_cnt = j;
                        t_rec[i]->same_buss_cnt = k;
                        max_sharer_cnt = max(max_sharer_cnt, t_rec[i]->sharer_cnt);
                        max_buss_cnt = max(max_buss_cnt, t_rec[i]->same_buss_cnt);
                        i++;
                    }

                    /* EXEC SQL CLOSE C05; */ 

{
                    struct sqlexd sqlstm;
                    sqlstm.sqlvsn = 13;
                    sqlstm.arrsiz = 16;
                    sqlstm.sqladtp = &sqladt;
                    sqlstm.sqltdsp = &sqltds;
                    sqlstm.iters = (unsigned int  )1;
                    sqlstm.offset = (unsigned int  )977;
                    sqlstm.cud = sqlcud0;
                    sqlstm.sqlest = (unsigned char  *)&sqlca;
                    sqlstm.sqlety = (unsigned short)4352;
                    sqlstm.occurs = (unsigned int  )0;
                    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


                    break;
                }

                continue;
            }

            t_rec[i]->subdistrict_line2[j][0] = '\0'; 

            for (l = 0; l < MAX_SUBDIST_NO_2; )
            {
/*
sprintf(s,"base no: %d", o_base_no);
mvaddstr(23,0,s);refresh();getch();
*/

/*
system("echo \"esemsc0f 7\" >> /tmp/debug");
sprintf(msg, "echo \"%c %s %s %s %d\" >> /tmp/debug",
o_sys_category, o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr, o_base_no );
system(msg);
*/

                /* EXEC SQL
                     SELECT SUBDISTRICT
                     INTO   :o_subdistrict
                     FROM   STATION
                     WHERE  SYS_CATEGORY = :o_sys_category
                     AND    SYS_TYPE = :o_sys_type
                     AND    SYS_NO = :o_sys_no
                     AND    SYS_SUFFIX = :o_sys_suffix
                     AND    BASE_NO = :o_base_no; */ 

{
                struct sqlexd sqlstm;
                sqlstm.sqlvsn = 13;
                sqlstm.arrsiz = 16;
                sqlstm.sqladtp = &sqladt;
                sqlstm.sqltdsp = &sqltds;
                sqlstm.stmt = "select SUBDISTRICT into :b0  from STATION whe\
re ((((SYS_CATEGORY=:b1 and SYS_TYPE=:b2) and SYS_NO=:b3) and SYS_SUFFIX=:b4) \
and BASE_NO=:b5)";
                sqlstm.iters = (unsigned int  )1;
                sqlstm.offset = (unsigned int  )992;
                sqlstm.selerr = (unsigned short)1;
                sqlstm.sqlpfmem = (unsigned int  )0;
                sqlstm.cud = sqlcud0;
                sqlstm.sqlest = (unsigned char  *)&sqlca;
                sqlstm.sqlety = (unsigned short)4352;
                sqlstm.occurs = (unsigned int  )0;
                sqlstm.sqhstv[0] = (unsigned char  *)&o_subdistrict;
                sqlstm.sqhstl[0] = (unsigned long )6;
                sqlstm.sqhsts[0] = (         int  )0;
                sqlstm.sqindv[0] = (         short *)0;
                sqlstm.sqinds[0] = (         int  )0;
                sqlstm.sqharm[0] = (unsigned long )0;
                sqlstm.sqadto[0] = (unsigned short )0;
                sqlstm.sqtdso[0] = (unsigned short )0;
                sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_category;
                sqlstm.sqhstl[1] = (unsigned long )1;
                sqlstm.sqhsts[1] = (         int  )0;
                sqlstm.sqindv[1] = (         short *)0;
                sqlstm.sqinds[1] = (         int  )0;
                sqlstm.sqharm[1] = (unsigned long )0;
                sqlstm.sqadto[1] = (unsigned short )0;
                sqlstm.sqtdso[1] = (unsigned short )0;
                sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_type;
                sqlstm.sqhstl[2] = (unsigned long )5;
                sqlstm.sqhsts[2] = (         int  )0;
                sqlstm.sqindv[2] = (         short *)0;
                sqlstm.sqinds[2] = (         int  )0;
                sqlstm.sqharm[2] = (unsigned long )0;
                sqlstm.sqadto[2] = (unsigned short )0;
                sqlstm.sqtdso[2] = (unsigned short )0;
                sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_no;
                sqlstm.sqhstl[3] = (unsigned long )10;
                sqlstm.sqhsts[3] = (         int  )0;
                sqlstm.sqindv[3] = (         short *)0;
                sqlstm.sqinds[3] = (         int  )0;
                sqlstm.sqharm[3] = (unsigned long )0;
                sqlstm.sqadto[3] = (unsigned short )0;
                sqlstm.sqtdso[3] = (unsigned short )0;
                sqlstm.sqhstv[4] = (unsigned char  *)&o_sys_suffix;
                sqlstm.sqhstl[4] = (unsigned long )6;
                sqlstm.sqhsts[4] = (         int  )0;
                sqlstm.sqindv[4] = (         short *)0;
                sqlstm.sqinds[4] = (         int  )0;
                sqlstm.sqharm[4] = (unsigned long )0;
                sqlstm.sqadto[4] = (unsigned short )0;
                sqlstm.sqtdso[4] = (unsigned short )0;
                sqlstm.sqhstv[5] = (unsigned char  *)&o_base_no;
                sqlstm.sqhstl[5] = (unsigned long )sizeof(int);
                sqlstm.sqhsts[5] = (         int  )0;
                sqlstm.sqindv[5] = (         short *)0;
                sqlstm.sqinds[5] = (         int  )0;
                sqlstm.sqharm[5] = (unsigned long )0;
                sqlstm.sqadto[5] = (unsigned short )0;
                sqlstm.sqtdso[5] = (unsigned short )0;
                sqlstm.sqphsv = sqlstm.sqhstv;
                sqlstm.sqphsl = sqlstm.sqhstl;
                sqlstm.sqphss = sqlstm.sqhsts;
                sqlstm.sqpind = sqlstm.sqindv;
                sqlstm.sqpins = sqlstm.sqinds;
                sqlstm.sqparm = sqlstm.sqharm;
                sqlstm.sqparc = sqlstm.sqharc;
                sqlstm.sqpadto = sqlstm.sqadto;
                sqlstm.sqptdso = sqlstm.sqtdso;
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



                if (sqlca.sqlcode == NOT_FOUND)
                { 
                    sprintf(err_msg, "Station not found for channel (%lf/%-lf) of system: %c-%s-%s-%s", 
                            o_tx_freq, o_rx_freq, o_sys_category,
                            o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr);
                    fclose(efp); fclose(sfp);
                    goto force_exit;
                }

                o_subdistrict.arr[o_subdistrict.len] = '\0';
                if (l < MAX_SUBDIST_NO)
                {
/*                    strcat(t_rec[i]->subdistrict_line1[j], 
                           o_subdistrict.arr); */
                    strcat(t_rec[i]->subdistrict_line1[j], (char *)o_subdistrict.arr);
                    pad_n_space(t_rec[i]->subdistrict_line1[j], 1);
                }
                else
                {
/*                    strcat(t_rec[i]->subdistrict_line2[j], 
                           o_subdistrict.arr); */
                    strcat(t_rec[i]->subdistrict_line2[j], (char *)o_subdistrict.arr);
                    pad_n_space(t_rec[i]->subdistrict_line2[j], 1);
                }

                l++;

                if (abs(o_rx_freq) < FREQ_EPSILON)
                {
                    /* EXEC SQL FETCH C060 
                         INTO   :o_base_no; */ 

{
                    struct sqlexd sqlstm;
                    sqlstm.sqlvsn = 13;
                    sqlstm.arrsiz = 16;
                    sqlstm.sqladtp = &sqladt;
                    sqlstm.sqltdsp = &sqltds;
                    sqlstm.iters = (unsigned int  )1;
                    sqlstm.offset = (unsigned int  )1031;
                    sqlstm.selerr = (unsigned short)1;
                    sqlstm.sqlpfmem = (unsigned int  )0;
                    sqlstm.cud = sqlcud0;
                    sqlstm.sqlest = (unsigned char  *)&sqlca;
                    sqlstm.sqlety = (unsigned short)4352;
                    sqlstm.occurs = (unsigned int  )0;
                    sqlstm.sqfoff = (         int )0;
                    sqlstm.sqfmod = (unsigned int )2;
                    sqlstm.sqhstv[0] = (unsigned char  *)&o_base_no;
                    sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
                    sqlstm.sqhsts[0] = (         int  )0;
                    sqlstm.sqindv[0] = (         short *)0;
                    sqlstm.sqinds[0] = (         int  )0;
                    sqlstm.sqharm[0] = (unsigned long )0;
                    sqlstm.sqadto[0] = (unsigned short )0;
                    sqlstm.sqtdso[0] = (unsigned short )0;
                    sqlstm.sqphsv = sqlstm.sqhstv;
                    sqlstm.sqphsl = sqlstm.sqhstl;
                    sqlstm.sqphss = sqlstm.sqhsts;
                    sqlstm.sqpind = sqlstm.sqindv;
                    sqlstm.sqpins = sqlstm.sqinds;
                    sqlstm.sqparm = sqlstm.sqharm;
                    sqlstm.sqparc = sqlstm.sqharc;
                    sqlstm.sqpadto = sqlstm.sqadto;
                    sqlstm.sqptdso = sqlstm.sqtdso;
                    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


                }
                else
                {
                    /* EXEC SQL FETCH C061
                         INTO   :o_base_no; */ 

{
                    struct sqlexd sqlstm;
                    sqlstm.sqlvsn = 13;
                    sqlstm.arrsiz = 16;
                    sqlstm.sqladtp = &sqladt;
                    sqlstm.sqltdsp = &sqltds;
                    sqlstm.iters = (unsigned int  )1;
                    sqlstm.offset = (unsigned int  )1050;
                    sqlstm.selerr = (unsigned short)1;
                    sqlstm.sqlpfmem = (unsigned int  )0;
                    sqlstm.cud = sqlcud0;
                    sqlstm.sqlest = (unsigned char  *)&sqlca;
                    sqlstm.sqlety = (unsigned short)4352;
                    sqlstm.occurs = (unsigned int  )0;
                    sqlstm.sqfoff = (         int )0;
                    sqlstm.sqfmod = (unsigned int )2;
                    sqlstm.sqhstv[0] = (unsigned char  *)&o_base_no;
                    sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
                    sqlstm.sqhsts[0] = (         int  )0;
                    sqlstm.sqindv[0] = (         short *)0;
                    sqlstm.sqinds[0] = (         int  )0;
                    sqlstm.sqharm[0] = (unsigned long )0;
                    sqlstm.sqadto[0] = (unsigned short )0;
                    sqlstm.sqtdso[0] = (unsigned short )0;
                    sqlstm.sqphsv = sqlstm.sqhstv;
                    sqlstm.sqphsl = sqlstm.sqhstl;
                    sqlstm.sqphss = sqlstm.sqhsts;
                    sqlstm.sqpind = sqlstm.sqindv;
                    sqlstm.sqpins = sqlstm.sqinds;
                    sqlstm.sqparm = sqlstm.sqharm;
                    sqlstm.sqparc = sqlstm.sqharc;
                    sqlstm.sqpadto = sqlstm.sqadto;
                    sqlstm.sqptdso = sqlstm.sqtdso;
                    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


                }

                if (sqlca.sqlcode == NOT_FOUND)
                    break;
            }

            if (abs(o_rx_freq) < FREQ_EPSILON)
            {
                /* EXEC SQL CLOSE C060; */ 

{
                struct sqlexd sqlstm;
                sqlstm.sqlvsn = 13;
                sqlstm.arrsiz = 16;
                sqlstm.sqladtp = &sqladt;
                sqlstm.sqltdsp = &sqltds;
                sqlstm.iters = (unsigned int  )1;
                sqlstm.offset = (unsigned int  )1069;
                sqlstm.cud = sqlcud0;
                sqlstm.sqlest = (unsigned char  *)&sqlca;
                sqlstm.sqlety = (unsigned short)4352;
                sqlstm.occurs = (unsigned int  )0;
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


            }
            else
            {
                /* EXEC SQL CLOSE C061; */ 

{
                struct sqlexd sqlstm;
                sqlstm.sqlvsn = 13;
                sqlstm.arrsiz = 16;
                sqlstm.sqladtp = &sqladt;
                sqlstm.sqltdsp = &sqltds;
                sqlstm.iters = (unsigned int  )1;
                sqlstm.offset = (unsigned int  )1084;
                sqlstm.cud = sqlcud0;
                sqlstm.sqlest = (unsigned char  *)&sqlca;
                sqlstm.sqlety = (unsigned short)4352;
                sqlstm.occurs = (unsigned int  )0;
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


            }

            if (l == MAX_SUBDIST_NO_2)
            {
                strcat(t_rec[i]->subdistrict_line1[j], "#");
/*
                sprintf(err_msg, "Too many stations for system %c-%s-%s-%s with channel %lf/%lf", 
                        o_sys_category, o_sys_type.arr, o_sys_no.arr, 
                        o_sys_suffix.arr, o_tx_freq, o_rx_freq);
                fclose(efp); fclose(sfp);
                goto force_exit;
*/
            }

/*
system("echo \"esemsc0f 8\" >> /tmp/debug");
sprintf(msg, "echo \"%c %s %s %s\" >> /tmp/debug",
o_sys_category, o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr );
system(msg);
*/

            /* EXEC SQL 
                 SELECT BUSS_CODE
                 INTO   :o_buss_code
                 FROM   SYSTEM
                 WHERE  SYS_CATEGORY = :o_sys_category
                 AND    SYS_TYPE = :o_sys_type
                 AND    SYS_NO = :o_sys_no
                 AND    SYS_SUFFIX = :o_sys_suffix; */ 

{
            struct sqlexd sqlstm;
            sqlstm.sqlvsn = 13;
            sqlstm.arrsiz = 16;
            sqlstm.sqladtp = &sqladt;
            sqlstm.sqltdsp = &sqltds;
            sqlstm.stmt = "select BUSS_CODE into :b0  from SYSTEM where (((S\
YS_CATEGORY=:b1 and SYS_TYPE=:b2) and SYS_NO=:b3) and SYS_SUFFIX=:b4)";
            sqlstm.iters = (unsigned int  )1;
            sqlstm.offset = (unsigned int  )1099;
            sqlstm.selerr = (unsigned short)1;
            sqlstm.sqlpfmem = (unsigned int  )0;
            sqlstm.cud = sqlcud0;
            sqlstm.sqlest = (unsigned char  *)&sqlca;
            sqlstm.sqlety = (unsigned short)4352;
            sqlstm.occurs = (unsigned int  )0;
            sqlstm.sqhstv[0] = (unsigned char  *)&o_buss_code;
            sqlstm.sqhstl[0] = (unsigned long )7;
            sqlstm.sqhsts[0] = (         int  )0;
            sqlstm.sqindv[0] = (         short *)0;
            sqlstm.sqinds[0] = (         int  )0;
            sqlstm.sqharm[0] = (unsigned long )0;
            sqlstm.sqadto[0] = (unsigned short )0;
            sqlstm.sqtdso[0] = (unsigned short )0;
            sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_category;
            sqlstm.sqhstl[1] = (unsigned long )1;
            sqlstm.sqhsts[1] = (         int  )0;
            sqlstm.sqindv[1] = (         short *)0;
            sqlstm.sqinds[1] = (         int  )0;
            sqlstm.sqharm[1] = (unsigned long )0;
            sqlstm.sqadto[1] = (unsigned short )0;
            sqlstm.sqtdso[1] = (unsigned short )0;
            sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_type;
            sqlstm.sqhstl[2] = (unsigned long )5;
            sqlstm.sqhsts[2] = (         int  )0;
            sqlstm.sqindv[2] = (         short *)0;
            sqlstm.sqinds[2] = (         int  )0;
            sqlstm.sqharm[2] = (unsigned long )0;
            sqlstm.sqadto[2] = (unsigned short )0;
            sqlstm.sqtdso[2] = (unsigned short )0;
            sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_no;
            sqlstm.sqhstl[3] = (unsigned long )10;
            sqlstm.sqhsts[3] = (         int  )0;
            sqlstm.sqindv[3] = (         short *)0;
            sqlstm.sqinds[3] = (         int  )0;
            sqlstm.sqharm[3] = (unsigned long )0;
            sqlstm.sqadto[3] = (unsigned short )0;
            sqlstm.sqtdso[3] = (unsigned short )0;
            sqlstm.sqhstv[4] = (unsigned char  *)&o_sys_suffix;
            sqlstm.sqhstl[4] = (unsigned long )6;
            sqlstm.sqhsts[4] = (         int  )0;
            sqlstm.sqindv[4] = (         short *)0;
            sqlstm.sqinds[4] = (         int  )0;
            sqlstm.sqharm[4] = (unsigned long )0;
            sqlstm.sqadto[4] = (unsigned short )0;
            sqlstm.sqtdso[4] = (unsigned short )0;
            sqlstm.sqphsv = sqlstm.sqhstv;
            sqlstm.sqphsl = sqlstm.sqhstl;
            sqlstm.sqphss = sqlstm.sqhsts;
            sqlstm.sqpind = sqlstm.sqindv;
            sqlstm.sqpins = sqlstm.sqinds;
            sqlstm.sqparm = sqlstm.sqharm;
            sqlstm.sqparc = sqlstm.sqharc;
            sqlstm.sqpadto = sqlstm.sqadto;
            sqlstm.sqptdso = sqlstm.sqtdso;
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



            if (sqlca.sqlcode == NOT_FOUND)
            { 
               sprintf(err_msg, "System not found: %c-%s-%s-%s", 
                       o_sys_category, o_sys_type.arr, o_sys_no.arr, 
                       o_sys_suffix.arr);
               fclose(efp); fclose(sfp);
               goto force_exit;
            }

            o_buss_code.arr[o_buss_code.len] = '\0';
 
            sprintf(t_rec[i]->system_id[j], "%c%s%s-%s", o_sys_category, 
                    o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr);
/*            strcpy(t_rec[i]->buss_code[j], o_buss_code.arr); */
            strcpy(t_rec[i]->buss_code[j], (char *)o_buss_code.arr);
            
            o_assign_date.arr[o_assign_date.len] = '\0';
/*            strcpy(t_rec[i]->assign_date[j], o_assign_date.arr); */
            strcpy(t_rec[i]->assign_date[j], (char *)o_assign_date.arr);
            
/*
sprintf(s,"i j system id: %d %d %s", i, j, t_rec[i]->system_id[j]);
mvaddstr(17,0,s);refresh();getch();
*/

/*            if (!strcmp(buss_code, o_buss_code.arr)) */
            if (!strcmp(buss_code, (char *)o_buss_code.arr))
                k++; 

            j++;
        }

        if (j == MAX_SHARER_NO)
        {
            for (prev_tx_freq = o_tx_freq; prev_tx_freq == o_tx_freq; )
            {
                prev_sys_id[0] = o_sys_category; prev_sys_id[1] = '\0';
/*                strcat(prev_sys_id, o_sys_type.arr);
                strcat(prev_sys_id, o_sys_no.arr);
                strcat(prev_sys_id, o_sys_suffix.arr); */
                strcat(prev_sys_id, (char *)o_sys_type.arr);
                strcat(prev_sys_id, (char *)o_sys_no.arr);
                strcat(prev_sys_id, (char *)o_sys_suffix.arr);

                /* EXEC SQL
                     FETCH C05
                     INTO  :o_sys_category, :o_sys_type, :o_sys_no,
                           :o_sys_suffix, :o_tx_freq, :o_rx_freq,
                           :o_assign_date, :o_ac_cancel_date; */ 

{
                struct sqlexd sqlstm;
                sqlstm.sqlvsn = 13;
                sqlstm.arrsiz = 16;
                sqlstm.sqladtp = &sqladt;
                sqlstm.sqltdsp = &sqltds;
                sqlstm.iters = (unsigned int  )1;
                sqlstm.offset = (unsigned int  )1134;
                sqlstm.selerr = (unsigned short)1;
                sqlstm.sqlpfmem = (unsigned int  )0;
                sqlstm.cud = sqlcud0;
                sqlstm.sqlest = (unsigned char  *)&sqlca;
                sqlstm.sqlety = (unsigned short)4352;
                sqlstm.occurs = (unsigned int  )0;
                sqlstm.sqfoff = (         int )0;
                sqlstm.sqfmod = (unsigned int )2;
                sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
                sqlstm.sqhstl[0] = (unsigned long )1;
                sqlstm.sqhsts[0] = (         int  )0;
                sqlstm.sqindv[0] = (         short *)0;
                sqlstm.sqinds[0] = (         int  )0;
                sqlstm.sqharm[0] = (unsigned long )0;
                sqlstm.sqadto[0] = (unsigned short )0;
                sqlstm.sqtdso[0] = (unsigned short )0;
                sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
                sqlstm.sqhstl[1] = (unsigned long )5;
                sqlstm.sqhsts[1] = (         int  )0;
                sqlstm.sqindv[1] = (         short *)0;
                sqlstm.sqinds[1] = (         int  )0;
                sqlstm.sqharm[1] = (unsigned long )0;
                sqlstm.sqadto[1] = (unsigned short )0;
                sqlstm.sqtdso[1] = (unsigned short )0;
                sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
                sqlstm.sqhstl[2] = (unsigned long )10;
                sqlstm.sqhsts[2] = (         int  )0;
                sqlstm.sqindv[2] = (         short *)0;
                sqlstm.sqinds[2] = (         int  )0;
                sqlstm.sqharm[2] = (unsigned long )0;
                sqlstm.sqadto[2] = (unsigned short )0;
                sqlstm.sqtdso[2] = (unsigned short )0;
                sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
                sqlstm.sqhstl[3] = (unsigned long )6;
                sqlstm.sqhsts[3] = (         int  )0;
                sqlstm.sqindv[3] = (         short *)0;
                sqlstm.sqinds[3] = (         int  )0;
                sqlstm.sqharm[3] = (unsigned long )0;
                sqlstm.sqadto[3] = (unsigned short )0;
                sqlstm.sqtdso[3] = (unsigned short )0;
                sqlstm.sqhstv[4] = (unsigned char  *)&o_tx_freq;
                sqlstm.sqhstl[4] = (unsigned long )sizeof(double);
                sqlstm.sqhsts[4] = (         int  )0;
                sqlstm.sqindv[4] = (         short *)0;
                sqlstm.sqinds[4] = (         int  )0;
                sqlstm.sqharm[4] = (unsigned long )0;
                sqlstm.sqadto[4] = (unsigned short )0;
                sqlstm.sqtdso[4] = (unsigned short )0;
                sqlstm.sqhstv[5] = (unsigned char  *)&o_rx_freq;
                sqlstm.sqhstl[5] = (unsigned long )sizeof(double);
                sqlstm.sqhsts[5] = (         int  )0;
                sqlstm.sqindv[5] = (         short *)0;
                sqlstm.sqinds[5] = (         int  )0;
                sqlstm.sqharm[5] = (unsigned long )0;
                sqlstm.sqadto[5] = (unsigned short )0;
                sqlstm.sqtdso[5] = (unsigned short )0;
                sqlstm.sqhstv[6] = (unsigned char  *)&o_assign_date;
                sqlstm.sqhstl[6] = (unsigned long )11;
                sqlstm.sqhsts[6] = (         int  )0;
                sqlstm.sqindv[6] = (         short *)0;
                sqlstm.sqinds[6] = (         int  )0;
                sqlstm.sqharm[6] = (unsigned long )0;
                sqlstm.sqadto[6] = (unsigned short )0;
                sqlstm.sqtdso[6] = (unsigned short )0;
                sqlstm.sqhstv[7] = (unsigned char  *)&o_ac_cancel_date;
                sqlstm.sqhstl[7] = (unsigned long )12;
                sqlstm.sqhsts[7] = (         int  )0;
                sqlstm.sqindv[7] = (         short *)0;
                sqlstm.sqinds[7] = (         int  )0;
                sqlstm.sqharm[7] = (unsigned long )0;
                sqlstm.sqadto[7] = (unsigned short )0;
                sqlstm.sqtdso[7] = (unsigned short )0;
                sqlstm.sqphsv = sqlstm.sqhstv;
                sqlstm.sqphsl = sqlstm.sqhstl;
                sqlstm.sqphss = sqlstm.sqhsts;
                sqlstm.sqpind = sqlstm.sqindv;
                sqlstm.sqpins = sqlstm.sqinds;
                sqlstm.sqparm = sqlstm.sqharm;
                sqlstm.sqparc = sqlstm.sqharc;
                sqlstm.sqpadto = sqlstm.sqadto;
                sqlstm.sqptdso = sqlstm.sqtdso;
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



                if (sqlca.sqlcode == NOT_FOUND)
                {
                    if (t_rec[i] != (FREQ_PRESEL *)NULL)
                    {
                        t_rec[i]->sharer_cnt = j;
                        t_rec[i]->same_buss_cnt = k;
                        max_sharer_cnt = max(max_sharer_cnt, t_rec[i]->sharer_cnt);
                        max_buss_cnt = max(max_buss_cnt, t_rec[i]->same_buss_cnt);
                        i++;
                    }

                    /* EXEC SQL CLOSE C05; */ 

{
                    struct sqlexd sqlstm;
                    sqlstm.sqlvsn = 13;
                    sqlstm.arrsiz = 16;
                    sqlstm.sqladtp = &sqladt;
                    sqlstm.sqltdsp = &sqltds;
                    sqlstm.iters = (unsigned int  )1;
                    sqlstm.offset = (unsigned int  )1181;
                    sqlstm.cud = sqlcud0;
                    sqlstm.sqlest = (unsigned char  *)&sqlca;
                    sqlstm.sqlety = (unsigned short)4352;
                    sqlstm.occurs = (unsigned int  )0;
                    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


                    break;
                }
            }
        }
        else
        {
            prev_sys_id[0] = o_sys_category; prev_sys_id[1] = '\0';
/*            strcat(prev_sys_id, o_sys_type.arr);
            strcat(prev_sys_id, o_sys_no.arr);
            strcat(prev_sys_id, o_sys_suffix.arr); */
            strcat(prev_sys_id, (char *)o_sys_type.arr);
            strcat(prev_sys_id, (char *)o_sys_no.arr);
            strcat(prev_sys_id, (char *)o_sys_suffix.arr);

            /* EXEC SQL
                 FETCH C05
                 INTO  :o_sys_category, :o_sys_type, :o_sys_no, :o_sys_suffix,
                       :o_tx_freq, :o_rx_freq, :o_assign_date, :o_ac_cancel_date; */ 

{
            struct sqlexd sqlstm;
            sqlstm.sqlvsn = 13;
            sqlstm.arrsiz = 16;
            sqlstm.sqladtp = &sqladt;
            sqlstm.sqltdsp = &sqltds;
            sqlstm.iters = (unsigned int  )1;
            sqlstm.offset = (unsigned int  )1196;
            sqlstm.selerr = (unsigned short)1;
            sqlstm.sqlpfmem = (unsigned int  )0;
            sqlstm.cud = sqlcud0;
            sqlstm.sqlest = (unsigned char  *)&sqlca;
            sqlstm.sqlety = (unsigned short)4352;
            sqlstm.occurs = (unsigned int  )0;
            sqlstm.sqfoff = (         int )0;
            sqlstm.sqfmod = (unsigned int )2;
            sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
            sqlstm.sqhstl[0] = (unsigned long )1;
            sqlstm.sqhsts[0] = (         int  )0;
            sqlstm.sqindv[0] = (         short *)0;
            sqlstm.sqinds[0] = (         int  )0;
            sqlstm.sqharm[0] = (unsigned long )0;
            sqlstm.sqadto[0] = (unsigned short )0;
            sqlstm.sqtdso[0] = (unsigned short )0;
            sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
            sqlstm.sqhstl[1] = (unsigned long )5;
            sqlstm.sqhsts[1] = (         int  )0;
            sqlstm.sqindv[1] = (         short *)0;
            sqlstm.sqinds[1] = (         int  )0;
            sqlstm.sqharm[1] = (unsigned long )0;
            sqlstm.sqadto[1] = (unsigned short )0;
            sqlstm.sqtdso[1] = (unsigned short )0;
            sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
            sqlstm.sqhstl[2] = (unsigned long )10;
            sqlstm.sqhsts[2] = (         int  )0;
            sqlstm.sqindv[2] = (         short *)0;
            sqlstm.sqinds[2] = (         int  )0;
            sqlstm.sqharm[2] = (unsigned long )0;
            sqlstm.sqadto[2] = (unsigned short )0;
            sqlstm.sqtdso[2] = (unsigned short )0;
            sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
            sqlstm.sqhstl[3] = (unsigned long )6;
            sqlstm.sqhsts[3] = (         int  )0;
            sqlstm.sqindv[3] = (         short *)0;
            sqlstm.sqinds[3] = (         int  )0;
            sqlstm.sqharm[3] = (unsigned long )0;
            sqlstm.sqadto[3] = (unsigned short )0;
            sqlstm.sqtdso[3] = (unsigned short )0;
            sqlstm.sqhstv[4] = (unsigned char  *)&o_tx_freq;
            sqlstm.sqhstl[4] = (unsigned long )sizeof(double);
            sqlstm.sqhsts[4] = (         int  )0;
            sqlstm.sqindv[4] = (         short *)0;
            sqlstm.sqinds[4] = (         int  )0;
            sqlstm.sqharm[4] = (unsigned long )0;
            sqlstm.sqadto[4] = (unsigned short )0;
            sqlstm.sqtdso[4] = (unsigned short )0;
            sqlstm.sqhstv[5] = (unsigned char  *)&o_rx_freq;
            sqlstm.sqhstl[5] = (unsigned long )sizeof(double);
            sqlstm.sqhsts[5] = (         int  )0;
            sqlstm.sqindv[5] = (         short *)0;
            sqlstm.sqinds[5] = (         int  )0;
            sqlstm.sqharm[5] = (unsigned long )0;
            sqlstm.sqadto[5] = (unsigned short )0;
            sqlstm.sqtdso[5] = (unsigned short )0;
            sqlstm.sqhstv[6] = (unsigned char  *)&o_assign_date;
            sqlstm.sqhstl[6] = (unsigned long )11;
            sqlstm.sqhsts[6] = (         int  )0;
            sqlstm.sqindv[6] = (         short *)0;
            sqlstm.sqinds[6] = (         int  )0;
            sqlstm.sqharm[6] = (unsigned long )0;
            sqlstm.sqadto[6] = (unsigned short )0;
            sqlstm.sqtdso[6] = (unsigned short )0;
            sqlstm.sqhstv[7] = (unsigned char  *)&o_ac_cancel_date;
            sqlstm.sqhstl[7] = (unsigned long )12;
            sqlstm.sqhsts[7] = (         int  )0;
            sqlstm.sqindv[7] = (         short *)0;
            sqlstm.sqinds[7] = (         int  )0;
            sqlstm.sqharm[7] = (unsigned long )0;
            sqlstm.sqadto[7] = (unsigned short )0;
            sqlstm.sqtdso[7] = (unsigned short )0;
            sqlstm.sqphsv = sqlstm.sqhstv;
            sqlstm.sqphsl = sqlstm.sqhstl;
            sqlstm.sqphss = sqlstm.sqhsts;
            sqlstm.sqpind = sqlstm.sqindv;
            sqlstm.sqpins = sqlstm.sqinds;
            sqlstm.sqparm = sqlstm.sqharm;
            sqlstm.sqparc = sqlstm.sqharc;
            sqlstm.sqpadto = sqlstm.sqadto;
            sqlstm.sqptdso = sqlstm.sqtdso;
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



            if (sqlca.sqlcode == NOT_FOUND)
            {
                if (t_rec[i] != (FREQ_PRESEL *)NULL)
                {
                    t_rec[i]->sharer_cnt = j;
                    t_rec[i]->same_buss_cnt = k;
                    max_sharer_cnt = max(max_sharer_cnt, t_rec[i]->sharer_cnt);
                    max_buss_cnt = max(max_buss_cnt, t_rec[i]->same_buss_cnt);
                    i++;
                }

                /* EXEC SQL CLOSE C05; */ 

{
                struct sqlexd sqlstm;
                sqlstm.sqlvsn = 13;
                sqlstm.arrsiz = 16;
                sqlstm.sqladtp = &sqladt;
                sqlstm.sqltdsp = &sqltds;
                sqlstm.iters = (unsigned int  )1;
                sqlstm.offset = (unsigned int  )1243;
                sqlstm.cud = sqlcud0;
                sqlstm.sqlest = (unsigned char  *)&sqlca;
                sqlstm.sqlety = (unsigned short)4352;
                sqlstm.occurs = (unsigned int  )0;
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


                break;
            }
        }

        if (   ((abs(o_tx_freq - prev_tx_freq) >= FREQ_EPSILON) 
        ||  (abs(o_rx_freq - prev_rx_freq) >= FREQ_EPSILON))
        &&  (t_rec[i] != (FREQ_PRESEL *)NULL))
        {
            t_rec[i]->sharer_cnt = j;
            t_rec[i]->same_buss_cnt = k;
            max_sharer_cnt = max(max_sharer_cnt, t_rec[i]->sharer_cnt);
            max_buss_cnt = max(max_buss_cnt, t_rec[i]->same_buss_cnt);
/*
sprintf(s,"%lf %lf %f %d %d press ...", t_rec[i]->tx_freq, t_rec[i]->rx_freq,
        t_rec[i]->traffic, t_rec[i]->sharer_cnt, t_rec[i]->same_buss_cnt);
mvaddstr(22,0,s);refresh();getch();
*/
            j = k = 0; i++;

            if (i == MAX_CHANNEL_NO)
                break;
        }
        else
        {
            if ((abs(o_tx_freq - prev_tx_freq) < FREQ_EPSILON) 
            &&  (abs(o_rx_freq - prev_rx_freq) < FREQ_EPSILON))
            {
                char   curr_sys_id[14];
                int    break_this_loop = 0;
                int    break_whole_loop = 0;

               
                for (; (!break_this_loop) && (!break_whole_loop);)
                {
                    o_sys_type.arr[o_sys_type.len] = '\0';
                    o_sys_no.arr[o_sys_no.len] = '\0';
                    o_sys_suffix.arr[o_sys_suffix.len] = '\0';

                    curr_sys_id[0] = o_sys_category; curr_sys_id[1] = '\0';
/*                    strcat(curr_sys_id, o_sys_type.arr);
                    strcat(curr_sys_id, o_sys_no.arr);
                    strcat(curr_sys_id, o_sys_suffix.arr); */
                    strcat(curr_sys_id, (char *)o_sys_type.arr);
                    strcat(curr_sys_id, (char *)o_sys_no.arr);
                    strcat(curr_sys_id, (char *)o_sys_suffix.arr);

/*
sprintf(s, " %d BF c: %s %lf/%-lf  p: %s %lf/%-lf   ", i, curr_sys_id, o_tx_freq, o_rx_freq,
prev_sys_id, prev_tx_freq, prev_rx_freq);
mvaddstr(22, 0, s); refresh(); getch();
*/

                    /*****************************************************/
                    /* skip the assigned channels that are of the same   */
                    /* tx/rx freq. and system id. as the previous one    */
                    /*                                                   */
                    /* a system is allowed to have 2 channels with the   */
                    /* same tx/rx freq. as long as they have different   */
                    /* tone freq.                                        */
                    /*****************************************************/

                    if ((strcmp(prev_sys_id, curr_sys_id) == 0)
                    && ((abs(o_tx_freq - prev_tx_freq) < FREQ_EPSILON) 
                    &&  (abs(o_rx_freq - prev_rx_freq) < FREQ_EPSILON)))
                    {
                        /* EXEC SQL
                             FETCH C05
                             INTO  :o_sys_category, :o_sys_type, :o_sys_no,
                                   :o_sys_suffix, :o_tx_freq, :o_rx_freq, 
                                   :o_assign_date, :o_ac_cancel_date; */ 

{
                        struct sqlexd sqlstm;
                        sqlstm.sqlvsn = 13;
                        sqlstm.arrsiz = 16;
                        sqlstm.sqladtp = &sqladt;
                        sqlstm.sqltdsp = &sqltds;
                        sqlstm.iters = (unsigned int  )1;
                        sqlstm.offset = (unsigned int  )1258;
                        sqlstm.selerr = (unsigned short)1;
                        sqlstm.sqlpfmem = (unsigned int  )0;
                        sqlstm.cud = sqlcud0;
                        sqlstm.sqlest = (unsigned char  *)&sqlca;
                        sqlstm.sqlety = (unsigned short)4352;
                        sqlstm.occurs = (unsigned int  )0;
                        sqlstm.sqfoff = (         int )0;
                        sqlstm.sqfmod = (unsigned int )2;
                        sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
                        sqlstm.sqhstl[0] = (unsigned long )1;
                        sqlstm.sqhsts[0] = (         int  )0;
                        sqlstm.sqindv[0] = (         short *)0;
                        sqlstm.sqinds[0] = (         int  )0;
                        sqlstm.sqharm[0] = (unsigned long )0;
                        sqlstm.sqadto[0] = (unsigned short )0;
                        sqlstm.sqtdso[0] = (unsigned short )0;
                        sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
                        sqlstm.sqhstl[1] = (unsigned long )5;
                        sqlstm.sqhsts[1] = (         int  )0;
                        sqlstm.sqindv[1] = (         short *)0;
                        sqlstm.sqinds[1] = (         int  )0;
                        sqlstm.sqharm[1] = (unsigned long )0;
                        sqlstm.sqadto[1] = (unsigned short )0;
                        sqlstm.sqtdso[1] = (unsigned short )0;
                        sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
                        sqlstm.sqhstl[2] = (unsigned long )10;
                        sqlstm.sqhsts[2] = (         int  )0;
                        sqlstm.sqindv[2] = (         short *)0;
                        sqlstm.sqinds[2] = (         int  )0;
                        sqlstm.sqharm[2] = (unsigned long )0;
                        sqlstm.sqadto[2] = (unsigned short )0;
                        sqlstm.sqtdso[2] = (unsigned short )0;
                        sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
                        sqlstm.sqhstl[3] = (unsigned long )6;
                        sqlstm.sqhsts[3] = (         int  )0;
                        sqlstm.sqindv[3] = (         short *)0;
                        sqlstm.sqinds[3] = (         int  )0;
                        sqlstm.sqharm[3] = (unsigned long )0;
                        sqlstm.sqadto[3] = (unsigned short )0;
                        sqlstm.sqtdso[3] = (unsigned short )0;
                        sqlstm.sqhstv[4] = (unsigned char  *)&o_tx_freq;
                        sqlstm.sqhstl[4] = (unsigned long )sizeof(double);
                        sqlstm.sqhsts[4] = (         int  )0;
                        sqlstm.sqindv[4] = (         short *)0;
                        sqlstm.sqinds[4] = (         int  )0;
                        sqlstm.sqharm[4] = (unsigned long )0;
                        sqlstm.sqadto[4] = (unsigned short )0;
                        sqlstm.sqtdso[4] = (unsigned short )0;
                        sqlstm.sqhstv[5] = (unsigned char  *)&o_rx_freq;
                        sqlstm.sqhstl[5] = (unsigned long )sizeof(double);
                        sqlstm.sqhsts[5] = (         int  )0;
                        sqlstm.sqindv[5] = (         short *)0;
                        sqlstm.sqinds[5] = (         int  )0;
                        sqlstm.sqharm[5] = (unsigned long )0;
                        sqlstm.sqadto[5] = (unsigned short )0;
                        sqlstm.sqtdso[5] = (unsigned short )0;
                        sqlstm.sqhstv[6] = (unsigned char  *)&o_assign_date;
                        sqlstm.sqhstl[6] = (unsigned long )11;
                        sqlstm.sqhsts[6] = (         int  )0;
                        sqlstm.sqindv[6] = (         short *)0;
                        sqlstm.sqinds[6] = (         int  )0;
                        sqlstm.sqharm[6] = (unsigned long )0;
                        sqlstm.sqadto[6] = (unsigned short )0;
                        sqlstm.sqtdso[6] = (unsigned short )0;
                        sqlstm.sqhstv[7] = (unsigned char  *)&o_ac_cancel_date;
                        sqlstm.sqhstl[7] = (unsigned long )12;
                        sqlstm.sqhsts[7] = (         int  )0;
                        sqlstm.sqindv[7] = (         short *)0;
                        sqlstm.sqinds[7] = (         int  )0;
                        sqlstm.sqharm[7] = (unsigned long )0;
                        sqlstm.sqadto[7] = (unsigned short )0;
                        sqlstm.sqtdso[7] = (unsigned short )0;
                        sqlstm.sqphsv = sqlstm.sqhstv;
                        sqlstm.sqphsl = sqlstm.sqhstl;
                        sqlstm.sqphss = sqlstm.sqhsts;
                        sqlstm.sqpind = sqlstm.sqindv;
                        sqlstm.sqpins = sqlstm.sqinds;
                        sqlstm.sqparm = sqlstm.sqharm;
                        sqlstm.sqparc = sqlstm.sqharc;
                        sqlstm.sqpadto = sqlstm.sqadto;
                        sqlstm.sqptdso = sqlstm.sqtdso;
                        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



                        if (sqlca.sqlcode == NOT_FOUND)
                        {
                            if (t_rec[i] != (FREQ_PRESEL *)NULL)
                            {
                                t_rec[i]->sharer_cnt = j;
                                t_rec[i]->same_buss_cnt = k;
                                max_sharer_cnt = max(max_sharer_cnt, t_rec[i]->sharer_cnt);
                                max_buss_cnt = max(max_buss_cnt, t_rec[i]->same_buss_cnt);
                                i++;
                            }

                            /* EXEC SQL CLOSE C05; */ 

{
                            struct sqlexd sqlstm;
                            sqlstm.sqlvsn = 13;
                            sqlstm.arrsiz = 16;
                            sqlstm.sqladtp = &sqladt;
                            sqlstm.sqltdsp = &sqltds;
                            sqlstm.iters = (unsigned int  )1;
                            sqlstm.offset = (unsigned int  )1305;
                            sqlstm.cud = sqlcud0;
                            sqlstm.sqlest = (unsigned char  *)&sqlca;
                            sqlstm.sqlety = (unsigned short)4352;
                            sqlstm.occurs = (unsigned int  )0;
                            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


                            break_whole_loop = 1;
                        }
                    }
                    else
                        break_this_loop = 1;
                }

                if (    ((abs(o_tx_freq - prev_tx_freq) >= FREQ_EPSILON) 
                    ||   (abs(o_rx_freq - prev_rx_freq) >= FREQ_EPSILON))
                && (t_rec[i] != (FREQ_PRESEL *)NULL))
                {
                    t_rec[i]->sharer_cnt = j;
                    t_rec[i]->same_buss_cnt = k;
                    max_sharer_cnt = max(max_sharer_cnt, t_rec[i]->sharer_cnt);
                    max_buss_cnt = max(max_buss_cnt, t_rec[i]->same_buss_cnt);
                    j = k = 0; i++;
                }

/*
sprintf(s, "%d AF c: %s %lf/%-lf  p: %s %lf/%-lf   ", i, curr_sys_id, o_tx_freq, o_rx_freq,
prev_sys_id, prev_tx_freq, prev_rx_freq);
mvaddstr(23, 0, s); refresh(); getch();
*/
                if (break_whole_loop)
                    break;
            }

        }

    }

clear_msg(17);attrset(A_REVERSE);

/*
sprintf(s,"cal traffic %d %d ...press", max_sharer_cnt, max_buss_cnt);
mvaddstr(17,0, s);refresh();getch();
sprintf(s,"sharer weight %f %f ...press", o_weight_sharer, o_weight_buss);
mvaddstr(17,0, s);refresh();getch();
*/

    /***********************************************************/
    /* Calculates figures of sharings according to information */
    /* stored in t_rec and the 'max_' values                   */
    /***********************************************************/
    for (j = 0; j < i; j++)
    {
        t_rec[j]->share_figure = t_rec[j]->traffic * o_weight_traffic;
        if (max_sharer_cnt != 0)
            t_rec[j]->share_figure = t_rec[j]->share_figure
                + t_rec[j]->sharer_cnt * o_weight_sharer / max_sharer_cnt;
        if (max_buss_cnt != 0)
            t_rec[j]->share_figure = t_rec[j]->share_figure
                + t_rec[j]->same_buss_cnt * o_weight_buss / max_buss_cnt;
/*
sprintf(s," %lf %f %f %f ...press", t_rec[j]->tx_freq, f1, f2, f3);
mvaddstr(22,0,s);refresh();getch();
*/
    }

    /**********************************************************************/
    /*  sort all channels in ascending order using shell sort             */
    /**********************************************************************/

/*
mvaddstr(17,0,"sort array ...press ");refresh();getch();
*/
    for (gap = i / 2; gap > 0; gap /= 2)
        for (j = gap; j < i; j++)
            for (k = j - gap; 
                    (k >= 0)
                 && (t_rec[k]->share_figure > t_rec[k+gap]->share_figure);
                 k -= gap)
            {
                tmp_rec      = t_rec[k];
                t_rec[k]     = t_rec[k+gap];
                t_rec[k+gap] = tmp_rec;
            }

/*
mvaddstr(17,0,"print result ...press");refresh();getch();
*/
    if (i > 0)
    {
        if (i > no_of_channels)
            i = no_of_channels;

        for (j = 0; j < i; j++)
            print_schannel_lines(sfp, t_rec[j], &line_cnt, &page_cnt);
/*
            print_schannel_lines(sfp, t_rec[j], &sys_cnt, &line_cnt, &page_cnt);
*/

        print_schannel_rpt_head(sfp, &page_cnt);
        fprintf(sfp, "\n\n\n\n\n\n");
        fprintf(sfp, "%38sNO. OF SHARED CHANNELS PRINTED                : %4d\n", "", i);
        fprintf(sfp, "%38sMAX. NO. OF SHARERS IN BAND               (M2): %4d\n", "", max_sharer_cnt);
        fprintf(sfp, "%38sMAX. NO. OF SAME BUSINESS SHARERS IN BAND (M3): %4d",
                "", max_buss_cnt);
        fprintf(sfp, "\n\n\n%5sNOTE\n", "");
        fprintf(sfp, "%5s====\n\n", "");
        fprintf(sfp, "%5sFIGURE OF SHARING = (A/1)*W1 + (B/M2)*W2 + (C/M3)*W3\n\n", "");
        fprintf(sfp, "%5sWHERE\n\n", "");
        fprintf(sfp, "%5sA = TRAFFIC LOADING OF A CHANNEL ", "");
        fprintf(sfp, "(MEASURED LOADING * NO. OF MOBILES, DEFAULT MEASURED LOADING = .005)\n");
        fprintf(sfp, "%5sB = NO. OF SHARERS IN A CHANNEL,\n", "");
        fprintf(sfp, "%5sC = NO. OF SHARERS IN A CHANNEL WITH ", "");
        fprintf(sfp, "SAME BUSINESS AS THE PROPOSED STATION,\n\n");
        fprintf(sfp, "%5sW1, W2 AND W3 ARE WEIGHTING FACTORS FOR ", "");
        fprintf(sfp, "A, B AND C RESPECTIVELY\n");
        fprintf(sfp, "%5sWHERE W1 = %5.2f, W2 = %5.2f, W3 = %5.2f",
                "", o_weight_traffic, o_weight_sharer, o_weight_buss);
    }

    fclose(efp); fclose(sfp);

    sprintf(cmdline, "lp -dprinter1 %s > /dev/null", sc_fname);
    system(cmdline);

    if (err_flag)
    {
        sprintf(err_msg,
                "Assigned channels with no mobiles found, see error log");
        sprintf(cmdline, "lp -dprinter1 %s > /dev/null", err_fname);
        system(cmdline);
        return NOT_FOUND;
    }

    for (j = 0; j < i; j++)
        free(t_rec[j]);

    return OK;

sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);

force_exit:
    disp_err(err_msg); getch();
    clear(); show_cursor(); refresh();
    endwin();
    /* EXEC SQL ROLLBACK RELEASE; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 16;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )1320;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


    exit(1);
}
             

/**********************************************************************/
/*  print shared channels report lines                                */
/**********************************************************************/

/*
print_schannel_lines(sfp, ptr, sys_cnt, line_cnt, page_cnt)
*/
print_schannel_lines(sfp, ptr, line_cnt, page_cnt)
FILE   *sfp;
FREQ_PRESEL *ptr;
int    *line_cnt, *page_cnt;
/*
int    *sys_cnt, *line_cnt, *page_cnt;
*/
{
    int    i;

    
/*
    if (*sys_cnt == 0)
*/
    if ((*line_cnt == 0) || (*line_cnt >= LINES_PER_PAGE))
    {
        print_schannel_rpt_head(sfp, page_cnt);
        print_schannel_col_head(sfp);
        (*page_cnt)++;
        *line_cnt = 17;
    }

    fprintf(sfp, "%11.5lf%2s%11.5lf%4s", ptr->tx_freq, "", ptr->rx_freq, "");

    (ptr->sharer_cnt == MAX_SHARER_NO) ?
        fprintf(sfp, "%5.2f%6s%3d*%3s%6.3f/%-3d%2s%s%2s%-14s %s %4s%2s%s\n", 
                ptr->share_figure, "", ptr->sharer_cnt, "", ptr->traffic, 
                ptr->no_of_mobiles, "", ptr->measure_date, "", 
                ptr->system_id[0], ptr->assign_date[0], ptr->buss_code[0], "",
                ptr->subdistrict_line1[0]):
        fprintf(sfp, "%5.2f%6s%3d%4s%6.3f/%-3d%2s%s%2s%-14s %s %4s%2s%s\n", 
                ptr->share_figure, "", ptr->sharer_cnt, "", ptr->traffic, 
                ptr->no_of_mobiles, "", ptr->measure_date, "", 
                ptr->system_id[0], ptr->assign_date[0], ptr->buss_code[0], "",
                ptr->subdistrict_line1[0]);

    if (ptr->subdistrict_line2[0][0] != '\0')
    {
        fprintf(sfp, "%98s%s\n", "", ptr->subdistrict_line2[0]);
        *line_cnt += 2;
    }
    else
        (*line_cnt)++;

/*
    *sys_cnt = (*sys_cnt + 1) % SYSTEM_PER_PAGE;
*/

    for (i = 1; i < ptr->sharer_cnt; i++)
    {
/*
        if (*sys_cnt == 0)
*/
        if (*line_cnt >= LINES_PER_PAGE)
        {
            print_schannel_rpt_head(sfp, page_cnt);
            print_schannel_col_head(sfp);
            (*page_cnt)++;

            fprintf(sfp, "%11.5lf%2s%11.5lf%4s", 
                    ptr->tx_freq, "", ptr->rx_freq, "");

            (ptr->sharer_cnt == MAX_SHARER_NO) ?
                fprintf(sfp, "%5.2f%6s%3d*%3s%6.3f/%-3d%2s%s%2s%-14s %s %4s%2s%s\n", 
                        ptr->share_figure, "", ptr->sharer_cnt, "",
                        ptr->traffic, ptr->no_of_mobiles, "",
                        ptr->measure_date, "", ptr->system_id[0], 
                        ptr->assign_date[0], ptr->buss_code[0], "",
                        ptr->subdistrict_line1[0]):
                fprintf(sfp, "%5.2f%6s%3d%4s%6.3f/%-3d%2s%s%2s%-14s %s %4s%2s%s\n", 
                        ptr->share_figure, "", ptr->sharer_cnt, "",
                        ptr->traffic, ptr->no_of_mobiles, "",
                        ptr->measure_date, "", ptr->system_id[0],
                        ptr->assign_date[0], ptr->buss_code[0], "",
                        ptr->subdistrict_line1[0]);

            if (ptr->subdistrict_line2[i][0] != '\0')
            {
                fprintf(sfp, "%98s%s\n", "", ptr->subdistrict_line2[i]);
                *line_cnt = 19;
            }
            else
                *line_cnt = 18;
        }
        else
        {
            fprintf(sfp, "%68s%-14s %s %4s%2s%s\n", "",
                    ptr->system_id[i], ptr->assign_date[i], 
                    ptr->buss_code[i], "", ptr->subdistrict_line1[i]);
            if (ptr->subdistrict_line2[i][0] != '\0')
            {
                fprintf(sfp, "%98s%s\n", "", ptr->subdistrict_line2[i]);
                *line_cnt += 2;
            }
            else
                (*line_cnt)++;
        }

/*
        *sys_cnt = (*sys_cnt + 1) % SYSTEM_PER_PAGE;
*/
    }

    fprintf(sfp, "\n");
    (*line_cnt)++;
    fflush(sfp);
}


/**********************************************************************/
/*  print shared channels report heading                              */
/**********************************************************************/

print_schannel_rpt_head(sfp, page_cnt)
FILE    *sfp;
int     *page_cnt;
{

#ifdef DEBUG
    printf("print_schannel_head\n");
#endif

   if (*page_cnt > 1)
       fprintf(sfp, "\f");

   fprintf(sfp, "RUN DATE: %s%16s", sys_date, "");
   fprintf(sfp, "*****************************************************************");
   fprintf(sfp, "%16sPAGE   : %-d\n", "", *page_cnt);
   fprintf(sfp, "RUN TIME: %s%16s", sys_time, "");
   fprintf(sfp, "*%63s*", "");
   fprintf(sfp, "%16sPROGRAM: esemsc0f\n", "");
   fprintf(sfp, "USER ID : %-19s%5s", emc_uid, "");
   fprintf(sfp, "*%20sFREQUENCY PRE-SELECTION%20s*\n", "", "");
   fprintf(sfp, "%34s", "");
   fprintf(sfp, "*%17sSHARED CHANNEL TRAFFIC REPORT%17s*\n", "", "");
   fprintf(sfp, "%34s", "");
   fprintf(sfp, "*%63s*\n", "");
   fprintf(sfp, "%34s", "");
   fprintf(sfp, "*   BUSS CODE : %-4s%18sFREQ BAND  : %-9s%4s*\n", buss_code,
           "", band_code, "");
   fprintf(sfp, "%34s", "");
   fprintf(sfp, "*   BASE TX LOW (MHz) : %11.5lf   HIGH (MHz) : %11.5lf  *\n",
           o_tx_freq_lo, o_tx_freq_hi);
   fprintf(sfp, "%34s", "");
   fprintf(sfp, "*   BASE RX LOW (MHz) : %11.5lf   HIGH (MHz) : %11.5lf  *\n",
           o_rx_freq_lo, o_rx_freq_hi);
   fprintf(sfp, "%34s", "");
   fprintf(sfp, "*   NO. OF CHANNELS TO BE PRINTED : %3d%25s*\n", 
	   no_of_channels, "");
   fprintf(sfp, "%34s", "");
   fprintf(sfp, "*%63s*\n", "");
   fprintf(sfp, "%34s", "");
   fprintf(sfp, "*****************************************************************");
   fprintf(sfp, "\n\n\n");
}


/**********************************************************************/
/*  print shared channels report column heading                       */
/**********************************************************************/

print_schannel_col_head(sfp)
FILE    *sfp;
{
   fprintf(sfp, " TX   FREQ%4sRX   FREQ%3sFIGURE OF%2sNO. OF%3sTRAF.LOAD/%2s",
		"", "", "", "", "");
   fprintf(sfp, "MEASURED%17sASSIGN%3sBUSS\n", "", "");
   fprintf(sfp, "   (MHz)%8s(MHz)%5sSHARING%4sSHARERS%2s", "", "", "", "");
   fprintf(sfp, "NO. OF MOB%4sDATE%4sSYSTEM ID.%5sDATE%5s", "", "", "", "");
   fprintf(sfp, "CODE%2sBASE STATION SUB-DISTRICT CODES\n", "");
   fprintf(sfp, "===========  ===========  =========  =======  ");
   fprintf(sfp, "==========  ========  ============== ======== ====  ");
   fprintf(sfp, "===============================\n\n"); 
   fflush(sfp);
}
