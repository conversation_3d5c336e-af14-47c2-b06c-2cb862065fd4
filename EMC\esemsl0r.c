
/* Result Sets Interface */
#ifndef SQL_CRSR
#  define SQL_CRSR
  struct sql_cursor
  {
    unsigned int curocn;
    void *ptr1;
    void *ptr2;
    unsigned int magic;
  };
  typedef struct sql_cursor sql_cursor;
  typedef struct sql_cursor SQL_CURSOR;
#endif /* SQL_CRSR */

/* Thread Safety */
typedef void * sql_context;
typedef void * SQL_CONTEXT;

/* Object support */
struct sqltvn
{
  unsigned char *tvnvsn; 
  unsigned short tvnvsnl; 
  unsigned char *tvnnm;
  unsigned short tvnnml; 
  unsigned char *tvnsnm;
  unsigned short tvnsnml;
};
typedef struct sqltvn sqltvn;

struct sqladts
{
  unsigned int adtvsn; 
  unsigned short adtmode; 
  unsigned short adtnum;  
  sqltvn adttvn[1];       
};
typedef struct sqladts sqladts;

static struct sqladts sqladt = {
  1,0,0,
};

/* Binding to PL/SQL Records */
struct sqltdss
{
  unsigned int tdsvsn; 
  unsigned short tdsnum; 
  unsigned char *tdsval[1]; 
};
typedef struct sqltdss sqltdss;
static struct sqltdss sqltds =
{
  1,
  0,
};

/* File name & Package Name */
struct sqlcxp
{
  unsigned short fillen;
           char  filnam[12];
};
static const struct sqlcxp sqlfpn =
{
    11,
    "esemsl0r.pc"
};


static unsigned int sqlctx = 150251;


static struct sqlexd {
   unsigned long  sqlvsn;
   unsigned int   arrsiz;
   unsigned int   iters;
   unsigned int   offset;
   unsigned short selerr;
   unsigned short sqlety;
   unsigned int   occurs;
      const short *cud;
   unsigned char  *sqlest;
      const char  *stmt;
   sqladts *sqladtp;
   sqltdss *sqltdsp;
   unsigned char  **sqphsv;
   unsigned long  *sqphsl;
            int   *sqphss;
            short **sqpind;
            int   *sqpins;
   unsigned long  *sqparm;
   unsigned long  **sqparc;
   unsigned short  *sqpadto;
   unsigned short  *sqptdso;
   unsigned int   sqlcmax;
   unsigned int   sqlcmin;
   unsigned int   sqlcincr;
   unsigned int   sqlctimeout;
   unsigned int   sqlcnowait;
            int   sqfoff;
   unsigned int   sqcmod;
   unsigned int   sqfmod;
   unsigned int   sqlpfmem;
   unsigned char  *sqhstv[9];
   unsigned long  sqhstl[9];
            int   sqhsts[9];
            short *sqindv[9];
            int   sqinds[9];
   unsigned long  sqharm[9];
   unsigned long  *sqharc[9];
   unsigned short  sqadto[9];
   unsigned short  sqtdso[9];
} sqlstm = {13,9};

/* SQLLIB Prototypes */
extern void sqlcxt (void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlcx2t(void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlbuft(void **, char *);
extern void sqlgs2t(void **, char *);
extern void sqlorat(void **, unsigned int *, void *);

/* Forms Interface */
static const int IAPSUCC = 0;
static const int IAPFAIL = 1403;
static const int IAPFTL  = 535;
extern void sqliem(unsigned char *, signed int *);

typedef struct { unsigned short len; unsigned char arr[1]; } VARCHAR;
typedef struct { unsigned short len; unsigned char arr[1]; } varchar;

/* cud (compilation unit data) array */
static const short sqlcud0[] =
{13,4242,1,0,0,
5,0,0,1,115,0,4,121,0,0,5,4,0,1,0,2,9,0,0,1,1,0,0,1,9,0,0,1,9,0,0,1,9,0,0,
40,0,0,2,219,0,4,147,0,0,9,8,0,1,0,2,3,0,0,1,1,0,0,1,9,0,0,1,9,0,0,1,9,0,0,1,4,
0,0,1,4,0,0,1,4,0,0,1,4,0,0,
};


/**********************************************************************/
/*                                                                    */
/*    Module Name   :  sub_district_stn (esemsl0r.pc)                 */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. H. Mak                                      */
/*                                                                    */
/*    Callers       :  main (esemcs0f.pc)                             */  
/*                                                                    */
/*    Parameters    :  error message                                  */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  Accept user input EMC analysis data, validate  */
/*                     the data the input data, then write to EMC     */
/*                     analysis batch file.                           */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <curses.h>
#include "../include/define.h"
#include "../include/emcext.h"
#include "../include/existext.h"

#ifndef ERROR
#define ERROR    1
#endif

#ifndef OK
#define OK       0
#endif

#define  MAX_COSITE_LINES    38

/*   strip blank mode   */
#define  TRAIL          0
#define  LEADING        1

extern char   sub_district[];
extern double prop_tx_freq, prop_rx_freq;
extern char   emc_uid[];

extern char msg[];

/* EXEC SQL BEGIN DECLARE SECTION; */ 


    double   o_tx_freq;
    double   o_rx_freq;
    double   o_tx_freq_lo;
    double   o_tx_freq_hi;
    double   o_rx_freq_lo;
    double   o_rx_freq_hi;
    int      o_cnt;
    char     o_sys_category;
    /* VARCHAR  o_sys_type[3]; */ 
struct { unsigned short len; unsigned char arr[3]; } o_sys_type;

    /* VARCHAR  o_sys_no[8]; */ 
struct { unsigned short len; unsigned char arr[8]; } o_sys_no;

    /* VARCHAR  o_sys_suffix[4]; */ 
struct { unsigned short len; unsigned char arr[4]; } o_sys_suffix;

    /* VARCHAR  o_e_name[49]; */ 
struct { unsigned short len; unsigned char arr[49]; } o_e_name;


/* EXEC SQL END DECLARE SECTION; */ 


/* EXEC SQL INCLUDE SQLCA;
 */ 
/*
 * $Header: sqlca.h 24-apr-2003.12:50:58 mkandarp Exp $ sqlca.h 
 */

/* Copyright (c) 1985, 2003, Oracle Corporation.  All rights reserved.  */
 
/*
NAME
  SQLCA : SQL Communications Area.
FUNCTION
  Contains no code. Oracle fills in the SQLCA with status info
  during the execution of a SQL stmt.
NOTES
  **************************************************************
  ***                                                        ***
  *** This file is SOSD.  Porters must change the data types ***
  *** appropriately on their platform.  See notes/pcport.doc ***
  *** for more information.                                  ***
  ***                                                        ***
  **************************************************************

  If the symbol SQLCA_STORAGE_CLASS is defined, then the SQLCA
  will be defined to have this storage class. For example:
 
    #define SQLCA_STORAGE_CLASS extern
 
  will define the SQLCA as an extern.
 
  If the symbol SQLCA_INIT is defined, then the SQLCA will be
  statically initialized. Although this is not necessary in order
  to use the SQLCA, it is a good pgming practice not to have
  unitialized variables. However, some C compilers/OS's don't
  allow automatic variables to be init'd in this manner. Therefore,
  if you are INCLUDE'ing the SQLCA in a place where it would be
  an automatic AND your C compiler/OS doesn't allow this style
  of initialization, then SQLCA_INIT should be left undefined --
  all others can define SQLCA_INIT if they wish.

  If the symbol SQLCA_NONE is defined, then the SQLCA variable will
  not be defined at all.  The symbol SQLCA_NONE should not be defined
  in source modules that have embedded SQL.  However, source modules
  that have no embedded SQL, but need to manipulate a sqlca struct
  passed in as a parameter, can set the SQLCA_NONE symbol to avoid
  creation of an extraneous sqlca variable.
 
MODIFIED
    lvbcheng   07/31/98 -  long to int
    jbasu      12/12/94 -  Bug 217878: note this is an SOSD file
    losborne   08/11/92 -  No sqlca var if SQLCA_NONE macro set 
  Clare      12/06/84 - Ch SQLCA to not be an extern.
  Clare      10/21/85 - Add initialization.
  Bradbury   01/05/86 - Only initialize when SQLCA_INIT set
  Clare      06/12/86 - Add SQLCA_STORAGE_CLASS option.
*/
 
#ifndef SQLCA
#define SQLCA 1
 
struct   sqlca
         {
         /* ub1 */ char    sqlcaid[8];
         /* b4  */ int     sqlabc;
         /* b4  */ int     sqlcode;
         struct
           {
           /* ub2 */ unsigned short sqlerrml;
           /* ub1 */ char           sqlerrmc[70];
           } sqlerrm;
         /* ub1 */ char    sqlerrp[8];
         /* b4  */ int     sqlerrd[6];
         /* ub1 */ char    sqlwarn[8];
         /* ub1 */ char    sqlext[8];
         };

#ifndef SQLCA_NONE 
#ifdef   SQLCA_STORAGE_CLASS
SQLCA_STORAGE_CLASS struct sqlca sqlca
#else
         struct sqlca sqlca
#endif
 
#ifdef  SQLCA_INIT
         = {
         {'S', 'Q', 'L', 'C', 'A', ' ', ' ', ' '},
         sizeof(struct sqlca),
         0,
         { 0, {0}},
         {'N', 'O', 'T', ' ', 'S', 'E', 'T', ' '},
         {0, 0, 0, 0, 0, 0},
         {0, 0, 0, 0, 0, 0, 0, 0},
         {0, 0, 0, 0, 0, 0, 0, 0}
         }
#endif
         ;
#endif
 
#endif
 
/* end SQLCA */



sub_district_stn(err_msg)
char   *err_msg;
{
    char   cs_dir[100];
    char   cs_fname[50];
    char   tmp_fname[50];
    char   cmdline[100];
    char   instr[133];
    FILE   *cfp, *tfp;
    int    line_cnt = 0;
    int    page_cnt = 1;
    int    skip_lines;
    register int    i, j;


    sprintf(cs_dir, "%s/cosite/substn", emc_dir);
    chdir(cs_dir);
    sprintf(tmp_fname, "%s.%s.tmp", emc_uid, sub_district);
    if((tfp = fopen(tmp_fname, "w")) == (FILE *) NULL)
    {
        sprintf(err_msg, "Fail to open %s, press any to exit", tmp_fname);
        return ERROR;
    }

    for (i = 0; i < fq_cnt; i++)
    {
        j = fq_list[i]->stn_node;

        o_sys_category = exist[j]->sys_category;
/*        strcpy(o_sys_type.arr, exist[j]->sys_type);
        strcpy(o_sys_no.arr, exist[j]->sys_no);
        strcpy(o_sys_suffix.arr, exist[j]->sys_suffix);
        o_sys_type.len = strlen(o_sys_type.arr);
        o_sys_no.len = strlen(o_sys_no.arr);
        o_sys_suffix.len = strlen(o_sys_suffix.arr); */
        strcpy((char *)o_sys_type.arr, exist[j]->sys_type);
        strcpy((char *)o_sys_no.arr, exist[j]->sys_no);
        strcpy((char *)o_sys_suffix.arr, exist[j]->sys_suffix);
        o_sys_type.len = strlen((char *)o_sys_type.arr);
        o_sys_no.len = strlen((char *)o_sys_no.arr);
        o_sys_suffix.len = strlen((char *)o_sys_suffix.arr);

/*
system("echo \"esemsl0r 1\" >> /tmp/debug");
sprintf(msg, "echo \"%c %s %s %s\" >> /tmp/debug",
o_sys_category, o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr );
system(msg);
*/

        /* EXEC SQL
             SELECT E_NAME
             INTO   :o_e_name
             FROM   SYSTEM
             WHERE  SYS_CATEGORY = :o_sys_category
             AND    SYS_TYPE = :o_sys_type
             AND    SYS_NO = :o_sys_no
             AND    SYS_SUFFIX = :o_sys_suffix; */ 

{
        struct sqlexd sqlstm;
        sqlstm.sqlvsn = 13;
        sqlstm.arrsiz = 5;
        sqlstm.sqladtp = &sqladt;
        sqlstm.sqltdsp = &sqltds;
        sqlstm.stmt = "select E_NAME into :b0  from SYSTEM where (((SYS_CATE\
GORY=:b1 and SYS_TYPE=:b2) and SYS_NO=:b3) and SYS_SUFFIX=:b4)";
        sqlstm.iters = (unsigned int  )1;
        sqlstm.offset = (unsigned int  )5;
        sqlstm.selerr = (unsigned short)1;
        sqlstm.sqlpfmem = (unsigned int  )0;
        sqlstm.cud = sqlcud0;
        sqlstm.sqlest = (unsigned char  *)&sqlca;
        sqlstm.sqlety = (unsigned short)4352;
        sqlstm.occurs = (unsigned int  )0;
        sqlstm.sqhstv[0] = (unsigned char  *)&o_e_name;
        sqlstm.sqhstl[0] = (unsigned long )51;
        sqlstm.sqhsts[0] = (         int  )0;
        sqlstm.sqindv[0] = (         short *)0;
        sqlstm.sqinds[0] = (         int  )0;
        sqlstm.sqharm[0] = (unsigned long )0;
        sqlstm.sqadto[0] = (unsigned short )0;
        sqlstm.sqtdso[0] = (unsigned short )0;
        sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_category;
        sqlstm.sqhstl[1] = (unsigned long )1;
        sqlstm.sqhsts[1] = (         int  )0;
        sqlstm.sqindv[1] = (         short *)0;
        sqlstm.sqinds[1] = (         int  )0;
        sqlstm.sqharm[1] = (unsigned long )0;
        sqlstm.sqadto[1] = (unsigned short )0;
        sqlstm.sqtdso[1] = (unsigned short )0;
        sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_type;
        sqlstm.sqhstl[2] = (unsigned long )5;
        sqlstm.sqhsts[2] = (         int  )0;
        sqlstm.sqindv[2] = (         short *)0;
        sqlstm.sqinds[2] = (         int  )0;
        sqlstm.sqharm[2] = (unsigned long )0;
        sqlstm.sqadto[2] = (unsigned short )0;
        sqlstm.sqtdso[2] = (unsigned short )0;
        sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_no;
        sqlstm.sqhstl[3] = (unsigned long )10;
        sqlstm.sqhsts[3] = (         int  )0;
        sqlstm.sqindv[3] = (         short *)0;
        sqlstm.sqinds[3] = (         int  )0;
        sqlstm.sqharm[3] = (unsigned long )0;
        sqlstm.sqadto[3] = (unsigned short )0;
        sqlstm.sqtdso[3] = (unsigned short )0;
        sqlstm.sqhstv[4] = (unsigned char  *)&o_sys_suffix;
        sqlstm.sqhstl[4] = (unsigned long )6;
        sqlstm.sqhsts[4] = (         int  )0;
        sqlstm.sqindv[4] = (         short *)0;
        sqlstm.sqinds[4] = (         int  )0;
        sqlstm.sqharm[4] = (unsigned long )0;
        sqlstm.sqadto[4] = (unsigned short )0;
        sqlstm.sqtdso[4] = (unsigned short )0;
        sqlstm.sqphsv = sqlstm.sqhstv;
        sqlstm.sqphsl = sqlstm.sqhstl;
        sqlstm.sqphss = sqlstm.sqhsts;
        sqlstm.sqpind = sqlstm.sqindv;
        sqlstm.sqpins = sqlstm.sqinds;
        sqlstm.sqparm = sqlstm.sqharm;
        sqlstm.sqparc = sqlstm.sqharc;
        sqlstm.sqpadto = sqlstm.sqadto;
        sqlstm.sqptdso = sqlstm.sqtdso;
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



        if (sqlca.sqlcode == NOT_FOUND)
        {
            sprintf(err_msg, "System (%c%s%s-%s) not found", o_sys_category,
                    o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr);
            sprintf(err_msg, "%s, press any to exit", err_msg);
            fclose(tfp);
            return ERROR;
        }

        o_tx_freq = fq_list[i]->tx_freq;
        o_rx_freq = fq_list[i]->rx_freq;

        o_tx_freq_lo = o_tx_freq - FREQ_EPSILON;
        o_tx_freq_hi = o_tx_freq + FREQ_EPSILON;
        o_rx_freq_lo = o_rx_freq - FREQ_EPSILON;
        o_rx_freq_hi = o_rx_freq + FREQ_EPSILON;

        /* EXEC SQL
             SELECT COUNT(ROWID)
             INTO   :o_cnt
             FROM   MOBILE_CH
             WHERE  SYS_CATEGORY = :o_sys_category
             AND    SYS_TYPE = :o_sys_type
             AND    SYS_NO = :o_sys_no
             AND    SYS_SUFFIX = :o_sys_suffix
             AND    TX_FREQ between :o_tx_freq_lo and :o_tx_freq_hi
             AND    RX_FREQ between :o_rx_freq_lo and :o_rx_freq_hi
             AND    CANCEL_DATE IS NULL; */ 

{
        struct sqlexd sqlstm;
        sqlstm.sqlvsn = 13;
        sqlstm.arrsiz = 9;
        sqlstm.sqladtp = &sqladt;
        sqlstm.sqltdsp = &sqltds;
        sqlstm.stmt = "select count(ROWID) into :b0  from MOBILE_CH where ((\
((((SYS_CATEGORY=:b1 and SYS_TYPE=:b2) and SYS_NO=:b3) and SYS_SUFFIX=:b4) and\
 TX_FREQ between :b5 and :b6) and RX_FREQ between :b7 and :b8) and CANCEL_DATE\
 is null )";
        sqlstm.iters = (unsigned int  )1;
        sqlstm.offset = (unsigned int  )40;
        sqlstm.selerr = (unsigned short)1;
        sqlstm.sqlpfmem = (unsigned int  )0;
        sqlstm.cud = sqlcud0;
        sqlstm.sqlest = (unsigned char  *)&sqlca;
        sqlstm.sqlety = (unsigned short)4352;
        sqlstm.occurs = (unsigned int  )0;
        sqlstm.sqhstv[0] = (unsigned char  *)&o_cnt;
        sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
        sqlstm.sqhsts[0] = (         int  )0;
        sqlstm.sqindv[0] = (         short *)0;
        sqlstm.sqinds[0] = (         int  )0;
        sqlstm.sqharm[0] = (unsigned long )0;
        sqlstm.sqadto[0] = (unsigned short )0;
        sqlstm.sqtdso[0] = (unsigned short )0;
        sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_category;
        sqlstm.sqhstl[1] = (unsigned long )1;
        sqlstm.sqhsts[1] = (         int  )0;
        sqlstm.sqindv[1] = (         short *)0;
        sqlstm.sqinds[1] = (         int  )0;
        sqlstm.sqharm[1] = (unsigned long )0;
        sqlstm.sqadto[1] = (unsigned short )0;
        sqlstm.sqtdso[1] = (unsigned short )0;
        sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_type;
        sqlstm.sqhstl[2] = (unsigned long )5;
        sqlstm.sqhsts[2] = (         int  )0;
        sqlstm.sqindv[2] = (         short *)0;
        sqlstm.sqinds[2] = (         int  )0;
        sqlstm.sqharm[2] = (unsigned long )0;
        sqlstm.sqadto[2] = (unsigned short )0;
        sqlstm.sqtdso[2] = (unsigned short )0;
        sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_no;
        sqlstm.sqhstl[3] = (unsigned long )10;
        sqlstm.sqhsts[3] = (         int  )0;
        sqlstm.sqindv[3] = (         short *)0;
        sqlstm.sqinds[3] = (         int  )0;
        sqlstm.sqharm[3] = (unsigned long )0;
        sqlstm.sqadto[3] = (unsigned short )0;
        sqlstm.sqtdso[3] = (unsigned short )0;
        sqlstm.sqhstv[4] = (unsigned char  *)&o_sys_suffix;
        sqlstm.sqhstl[4] = (unsigned long )6;
        sqlstm.sqhsts[4] = (         int  )0;
        sqlstm.sqindv[4] = (         short *)0;
        sqlstm.sqinds[4] = (         int  )0;
        sqlstm.sqharm[4] = (unsigned long )0;
        sqlstm.sqadto[4] = (unsigned short )0;
        sqlstm.sqtdso[4] = (unsigned short )0;
        sqlstm.sqhstv[5] = (unsigned char  *)&o_tx_freq_lo;
        sqlstm.sqhstl[5] = (unsigned long )sizeof(double);
        sqlstm.sqhsts[5] = (         int  )0;
        sqlstm.sqindv[5] = (         short *)0;
        sqlstm.sqinds[5] = (         int  )0;
        sqlstm.sqharm[5] = (unsigned long )0;
        sqlstm.sqadto[5] = (unsigned short )0;
        sqlstm.sqtdso[5] = (unsigned short )0;
        sqlstm.sqhstv[6] = (unsigned char  *)&o_tx_freq_hi;
        sqlstm.sqhstl[6] = (unsigned long )sizeof(double);
        sqlstm.sqhsts[6] = (         int  )0;
        sqlstm.sqindv[6] = (         short *)0;
        sqlstm.sqinds[6] = (         int  )0;
        sqlstm.sqharm[6] = (unsigned long )0;
        sqlstm.sqadto[6] = (unsigned short )0;
        sqlstm.sqtdso[6] = (unsigned short )0;
        sqlstm.sqhstv[7] = (unsigned char  *)&o_rx_freq_lo;
        sqlstm.sqhstl[7] = (unsigned long )sizeof(double);
        sqlstm.sqhsts[7] = (         int  )0;
        sqlstm.sqindv[7] = (         short *)0;
        sqlstm.sqinds[7] = (         int  )0;
        sqlstm.sqharm[7] = (unsigned long )0;
        sqlstm.sqadto[7] = (unsigned short )0;
        sqlstm.sqtdso[7] = (unsigned short )0;
        sqlstm.sqhstv[8] = (unsigned char  *)&o_rx_freq_hi;
        sqlstm.sqhstl[8] = (unsigned long )sizeof(double);
        sqlstm.sqhsts[8] = (         int  )0;
        sqlstm.sqindv[8] = (         short *)0;
        sqlstm.sqinds[8] = (         int  )0;
        sqlstm.sqharm[8] = (unsigned long )0;
        sqlstm.sqadto[8] = (unsigned short )0;
        sqlstm.sqtdso[8] = (unsigned short )0;
        sqlstm.sqphsv = sqlstm.sqhstv;
        sqlstm.sqphsl = sqlstm.sqhstl;
        sqlstm.sqphss = sqlstm.sqhsts;
        sqlstm.sqpind = sqlstm.sqindv;
        sqlstm.sqpins = sqlstm.sqinds;
        sqlstm.sqparm = sqlstm.sqharm;
        sqlstm.sqparc = sqlstm.sqharc;
        sqlstm.sqpadto = sqlstm.sqadto;
        sqlstm.sqptdso = sqlstm.sqtdso;
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}


/*
             AND    TX_FREQ = :o_rx_freq
             AND    RX_FREQ = :o_tx_freq
             AND    CANCEL_DATE IS NULL;
*/

        o_e_name.arr[o_e_name.len] = '\0';

/*
        fprintf(tfp, "%2s%-48s%4s%5d%4s%5d%4s%11.5lf%4s%11.5lf%5s%6.1f%7s%3d%8s%c\n",
                "", o_e_name.arr, "", exist[j]->east_grid, "",
                exist[j]->north_grid, "", fq_list[i]->tx_freq, "",
                fq_list[i]->rx_freq, "", exist[j]->pw_dbw, "", o_cnt,
                "", fq_list[i]->act_flag);
*/
        fprintf(tfp, "%2s%-48s%4s%5d%4s%5d%4s%11.5lf%4s%11.5lf%5s%6.1f%5s%4d%6s%3d\n",
                "", o_e_name.arr, "", exist[j]->east_grid, "",
                exist[j]->north_grid, "", fq_list[i]->tx_freq, "",
                fq_list[i]->rx_freq, "", exist[j]->pw_dbw, "",
                exist[j]->ant_height, "", exist[j]->ant_gain);

        if (sqlca.sqlcode == NOT_FOUND)
            break;
    }
    
    fclose(tfp);

    if (fq_cnt == 0)
    {
        unlink(tmp_fname);
        sprintf(err_msg, "No station found, press any key to continue");
        return NOT_FOUND;
    }

    sprintf(cmdline, "sort -o %s.%s.srt %s", 
            emc_uid, sub_district, tmp_fname);
    system(cmdline);

    unlink(tmp_fname);     /* remove 'emc_uid.sub_district.tmp' */
    sprintf(tmp_fname, "%s.%s.srt", emc_uid, sub_district);

    if((tfp = fopen(tmp_fname, "r")) == (FILE *) NULL)
    {
        sprintf(err_msg, "Fail to open %s, press any key to exit", tmp_fname);
        return ERROR;
    }

    sprintf(cs_fname, "%s.%s", emc_uid, sub_district);
    if((cfp = fopen(cs_fname, "w")) == (FILE *) NULL)
    {
        sprintf(err_msg, "Fail to open %s, press any key to exit", cs_fname);
        return ERROR;
    }

    for (; fgets(instr, sizeof(instr), tfp) != (char *) NULL; )
    {
        if (line_cnt == 0)
        {
            print_substn_head(cfp, page_cnt);
            page_cnt++;
        }

        line_cnt = (++line_cnt) % MAX_COSITE_LINES;
        fputs(instr, cfp);
    }

    skip_lines = 3 + (MAX_COSITE_LINES - line_cnt);
    for (i = 0; i < skip_lines; i++)
        fprintf(cfp, "\n");
    fprintf(cfp, "%50sNO. OF STATIONS PRINTED : %d", "", fq_cnt);

    fclose(cfp); fclose(tfp);
    unlink(tmp_fname);     /* remove 'emc_uid.sub_district.srt' */
    sprintf(cmdline, "lp -dprinter1 %s > /dev/null", cs_fname);
    system(cmdline);
    return OK;


sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    refresh(); beep();
    sleep(1); clear(); show_cursor(); refresh();
    endwin();
    exit(1);
}


/**********************************************************************/
/*  print co-site sub-district station report heading                 */
/**********************************************************************/

print_substn_head(cfp, page_cnt)
FILE    *cfp;
int     page_cnt;
{

/*
#ifdef DEBUG
    printf("print_substn_head\n");
#endif
*/

   if (page_cnt > 1)
       fprintf(cfp, "\f");

   fprintf(cfp, "RUN DATE: %s%22s", sys_date, "");
   fprintf(cfp, "***************************************************");
   fprintf(cfp, "%21sPAGE   : %-d\n", "", page_cnt);
   fprintf(cfp, "RUN TIME: %s%22s", sys_time, "");
   fprintf(cfp, "*                                                 *");
   fprintf(cfp, "%21sPROGRAM: esemsl0r\n", "");
   fprintf(cfp, "%40s", "");
   fprintf(cfp, "*                CO-SITE  ANALYSIS                *\n");
   fprintf(cfp, "USER ID : %-19s%11s", emc_uid, "");
   fprintf(cfp, "*           SUB-DISTRICT STATION REPORT           *\n");
   fprintf(cfp, "%40s", "");
   fprintf(cfp, "*                                                 *\n");
   fprintf(cfp, "%40s", "");
   fprintf(cfp, "*             SUB-DISTRICT CODE : %3s             *\n",
           sub_district);
   fprintf(cfp, "%40s", "");
   fprintf(cfp, "*                                                 *\n");
   fprintf(cfp, "%40s", "");
   fprintf(cfp, "***************************************************");
   fprintf(cfp, "\n\n\n\n");
/*
   fprintf(cfp, "%58sSTATION%9sTX FREQ%8sRX FREQ%6sTX POWER%4sNO. OF%5sACT\n",
           "", "", "", "", "", "");
*/
   fprintf(cfp, "%58sSTATION%9sTX FREQ%8sRX FREQ%6sTX POWER%12sANTENNA\n",
           "", "", "", "", "");
   fprintf(cfp, "%2sSYSTEM NAME%41sEAST%5sNORTH%8s(MHz)%11s(MHz)",
           "", "", "", "", "");
   fprintf(cfp, "%6s(dBW)%6sHALT%4sGAIN\n", "", "", "");
/*
   fprintf(cfp, "%6s(dBW)%6sMOBILES%4sFLAG\n", "", "", "");
*/
   fprintf(cfp, "  ================================================    ");
   fprintf(cfp, "=====    =====    ===========    ===========    ");
   fprintf(cfp, "========    ====    =======\n\n");
/*
   fprintf(cfp, "========    =======    ====\n\n");
*/
}
