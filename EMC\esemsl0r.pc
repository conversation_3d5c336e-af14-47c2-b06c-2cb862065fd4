/**********************************************************************/
/*                                                                    */
/*    Module Name   :  sub_district_stn (esemsl0r.pc)                 */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. <PERSON><PERSON> Mak                                      */
/*                                                                    */
/*    Callers       :  main (esemcs0f.pc)                             */  
/*                                                                    */
/*    Parameters    :  error message                                  */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  Accept user input EMC analysis data, validate  */
/*                     the data the input data, then write to EMC     */
/*                     analysis batch file.                           */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <curses.h>
#include "../include/define.h"
#include "../include/emcext.h"
#include "../include/existext.h"

#ifndef ERROR
#define ERROR    1
#endif

#ifndef OK
#define OK       0
#endif

#define  MAX_COSITE_LINES    38

/*   strip blank mode   */
#define  TRAIL          0
#define  LEADING        1

extern char   sub_district[];
extern double prop_tx_freq, prop_rx_freq;
extern char   emc_uid[];

extern char msg[];

EXEC SQL BEGIN DECLARE SECTION;

    double   o_tx_freq;
    double   o_rx_freq;
    double   o_tx_freq_lo;
    double   o_tx_freq_hi;
    double   o_rx_freq_lo;
    double   o_rx_freq_hi;
    int      o_cnt;
    char     o_sys_category;
    VARCHAR  o_sys_type[3];
    VARCHAR  o_sys_no[8];
    VARCHAR  o_sys_suffix[4];
    VARCHAR  o_e_name[49];

EXEC SQL END DECLARE SECTION;

EXEC SQL INCLUDE SQLCA;


sub_district_stn(err_msg)
char   *err_msg;
{
    char   cs_dir[100];
    char   cs_fname[50];
    char   tmp_fname[50];
    char   cmdline[100];
    char   instr[133];
    FILE   *cfp, *tfp;
    int    line_cnt = 0;
    int    page_cnt = 1;
    int    skip_lines;
    register int    i, j;


    sprintf(cs_dir, "%s/cosite/substn", emc_dir);
    chdir(cs_dir);
    sprintf(tmp_fname, "%s.%s.tmp", emc_uid, sub_district);
    if((tfp = fopen(tmp_fname, "w")) == (FILE *) NULL)
    {
        sprintf(err_msg, "Fail to open %s, press any to exit", tmp_fname);
        return ERROR;
    }

    for (i = 0; i < fq_cnt; i++)
    {
        j = fq_list[i]->stn_node;

        o_sys_category = exist[j]->sys_category;
/*        strcpy(o_sys_type.arr, exist[j]->sys_type);
        strcpy(o_sys_no.arr, exist[j]->sys_no);
        strcpy(o_sys_suffix.arr, exist[j]->sys_suffix);
        o_sys_type.len = strlen(o_sys_type.arr);
        o_sys_no.len = strlen(o_sys_no.arr);
        o_sys_suffix.len = strlen(o_sys_suffix.arr); */
        strcpy((char *)o_sys_type.arr, exist[j]->sys_type);
        strcpy((char *)o_sys_no.arr, exist[j]->sys_no);
        strcpy((char *)o_sys_suffix.arr, exist[j]->sys_suffix);
        o_sys_type.len = strlen((char *)o_sys_type.arr);
        o_sys_no.len = strlen((char *)o_sys_no.arr);
        o_sys_suffix.len = strlen((char *)o_sys_suffix.arr);

/*
system("echo \"esemsl0r 1\" >> /tmp/debug");
sprintf(msg, "echo \"%c %s %s %s\" >> /tmp/debug",
o_sys_category, o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr );
system(msg);
*/

        EXEC SQL
             SELECT E_NAME
             INTO   :o_e_name
             FROM   SYSTEM
             WHERE  SYS_CATEGORY = :o_sys_category
             AND    SYS_TYPE = :o_sys_type
             AND    SYS_NO = :o_sys_no
             AND    SYS_SUFFIX = :o_sys_suffix;

        if (sqlca.sqlcode == NOT_FOUND)
        {
            sprintf(err_msg, "System (%c%s%s-%s) not found", o_sys_category,
                    o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr);
            sprintf(err_msg, "%s, press any to exit", err_msg);
            fclose(tfp);
            return ERROR;
        }

        o_tx_freq = fq_list[i]->tx_freq;
        o_rx_freq = fq_list[i]->rx_freq;

        o_tx_freq_lo = o_tx_freq - FREQ_EPSILON;
        o_tx_freq_hi = o_tx_freq + FREQ_EPSILON;
        o_rx_freq_lo = o_rx_freq - FREQ_EPSILON;
        o_rx_freq_hi = o_rx_freq + FREQ_EPSILON;

        EXEC SQL
             SELECT COUNT(ROWID)
             INTO   :o_cnt
             FROM   MOBILE_CH
             WHERE  SYS_CATEGORY = :o_sys_category
             AND    SYS_TYPE = :o_sys_type
             AND    SYS_NO = :o_sys_no
             AND    SYS_SUFFIX = :o_sys_suffix
             AND    TX_FREQ between :o_tx_freq_lo and :o_tx_freq_hi
             AND    RX_FREQ between :o_rx_freq_lo and :o_rx_freq_hi
             AND    CANCEL_DATE IS NULL;
/*
             AND    TX_FREQ = :o_rx_freq
             AND    RX_FREQ = :o_tx_freq
             AND    CANCEL_DATE IS NULL;
*/

        o_e_name.arr[o_e_name.len] = '\0';

/*
        fprintf(tfp, "%2s%-48s%4s%5d%4s%5d%4s%11.5lf%4s%11.5lf%5s%6.1f%7s%3d%8s%c\n",
                "", o_e_name.arr, "", exist[j]->east_grid, "",
                exist[j]->north_grid, "", fq_list[i]->tx_freq, "",
                fq_list[i]->rx_freq, "", exist[j]->pw_dbw, "", o_cnt,
                "", fq_list[i]->act_flag);
*/
        fprintf(tfp, "%2s%-48s%4s%5d%4s%5d%4s%11.5lf%4s%11.5lf%5s%6.1f%5s%4d%6s%3d\n",
                "", o_e_name.arr, "", exist[j]->east_grid, "",
                exist[j]->north_grid, "", fq_list[i]->tx_freq, "",
                fq_list[i]->rx_freq, "", exist[j]->pw_dbw, "",
                exist[j]->ant_height, "", exist[j]->ant_gain);

        if (sqlca.sqlcode == NOT_FOUND)
            break;
    }
    
    fclose(tfp);

    if (fq_cnt == 0)
    {
        unlink(tmp_fname);
        sprintf(err_msg, "No station found, press any key to continue");
        return NOT_FOUND;
    }

    sprintf(cmdline, "sort -o %s.%s.srt %s", 
            emc_uid, sub_district, tmp_fname);
    system(cmdline);

    unlink(tmp_fname);     /* remove 'emc_uid.sub_district.tmp' */
    sprintf(tmp_fname, "%s.%s.srt", emc_uid, sub_district);

    if((tfp = fopen(tmp_fname, "r")) == (FILE *) NULL)
    {
        sprintf(err_msg, "Fail to open %s, press any key to exit", tmp_fname);
        return ERROR;
    }

    sprintf(cs_fname, "%s.%s", emc_uid, sub_district);
    if((cfp = fopen(cs_fname, "w")) == (FILE *) NULL)
    {
        sprintf(err_msg, "Fail to open %s, press any key to exit", cs_fname);
        return ERROR;
    }

    for (; fgets(instr, sizeof(instr), tfp) != (char *) NULL; )
    {
        if (line_cnt == 0)
        {
            print_substn_head(cfp, page_cnt);
            page_cnt++;
        }

        line_cnt = (++line_cnt) % MAX_COSITE_LINES;
        fputs(instr, cfp);
    }

    skip_lines = 3 + (MAX_COSITE_LINES - line_cnt);
    for (i = 0; i < skip_lines; i++)
        fprintf(cfp, "\n");
    fprintf(cfp, "%50sNO. OF STATIONS PRINTED : %d", "", fq_cnt);

    fclose(cfp); fclose(tfp);
    unlink(tmp_fname);     /* remove 'emc_uid.sub_district.srt' */
    sprintf(cmdline, "lp -dprinter1 %s > /dev/null", cs_fname);
    system(cmdline);
    return OK;


sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    refresh(); beep();
    sleep(1); clear(); show_cursor(); refresh();
    endwin();
    exit(1);
}


/**********************************************************************/
/*  print co-site sub-district station report heading                 */
/**********************************************************************/

print_substn_head(cfp, page_cnt)
FILE    *cfp;
int     page_cnt;
{

/*
#ifdef DEBUG
    printf("print_substn_head\n");
#endif
*/

   if (page_cnt > 1)
       fprintf(cfp, "\f");

   fprintf(cfp, "RUN DATE: %s%22s", sys_date, "");
   fprintf(cfp, "***************************************************");
   fprintf(cfp, "%21sPAGE   : %-d\n", "", page_cnt);
   fprintf(cfp, "RUN TIME: %s%22s", sys_time, "");
   fprintf(cfp, "*                                                 *");
   fprintf(cfp, "%21sPROGRAM: esemsl0r\n", "");
   fprintf(cfp, "%40s", "");
   fprintf(cfp, "*                CO-SITE  ANALYSIS                *\n");
   fprintf(cfp, "USER ID : %-19s%11s", emc_uid, "");
   fprintf(cfp, "*           SUB-DISTRICT STATION REPORT           *\n");
   fprintf(cfp, "%40s", "");
   fprintf(cfp, "*                                                 *\n");
   fprintf(cfp, "%40s", "");
   fprintf(cfp, "*             SUB-DISTRICT CODE : %3s             *\n",
           sub_district);
   fprintf(cfp, "%40s", "");
   fprintf(cfp, "*                                                 *\n");
   fprintf(cfp, "%40s", "");
   fprintf(cfp, "***************************************************");
   fprintf(cfp, "\n\n\n\n");
/*
   fprintf(cfp, "%58sSTATION%9sTX FREQ%8sRX FREQ%6sTX POWER%4sNO. OF%5sACT\n",
           "", "", "", "", "", "");
*/
   fprintf(cfp, "%58sSTATION%9sTX FREQ%8sRX FREQ%6sTX POWER%12sANTENNA\n",
           "", "", "", "", "");
   fprintf(cfp, "%2sSYSTEM NAME%41sEAST%5sNORTH%8s(MHz)%11s(MHz)",
           "", "", "", "", "");
   fprintf(cfp, "%6s(dBW)%6sHALT%4sGAIN\n", "", "", "");
/*
   fprintf(cfp, "%6s(dBW)%6sMOBILES%4sFLAG\n", "", "", "");
*/
   fprintf(cfp, "  ================================================    ");
   fprintf(cfp, "=====    =====    ===========    ===========    ");
   fprintf(cfp, "========    ====    =======\n\n");
/*
   fprintf(cfp, "========    =======    ====\n\n");
*/
}
