/**********************************************************************/
/*                                                                    */
/*    Module Name   :  select_station (esemss0x.pc)                   */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. <PERSON>. Mak                                      */
/*                                                                    */
/*    Callers       :  main (esemba0x.c)                              */  
/*                                                                    */
/*    Parameters    :                                                 */
/*                                                                    */
/*    Called Modules:  get_local_height (esemtn0x.pc)                 */
/*                     get_district (esemgd0x.c)                      */
/*                                                                    */
/*    Purpose       :  Select stations that are within culling        */
/*                     distance with respect to proposed station.     */
/*                     Then put station details into array 'exist'    */
/*                     put station channels into array 'fq_list'.     */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <math.h>
/* #include "../include/math.h" */
#include "../include/macros.h"
#include "../include/define.h"
#include "../include/emcext.h"
#include "../include/refext.h"
#include "../include/propext.h"
#include "../include/existext.h"

float    get_local_height();

#ifndef ERROR
#define ERROR    1
#endif

#ifndef OK
#define OK       0
#endif

#ifndef M_PI
#define M_PI            3.14159265358979323846
#endif


int select_station();     /*20170613 Cyrus [Add] */

int get_district  (char *,char *,int *,int *); /*20170613 Cyrus [Add] */



extern char msg[];




int select_station()
{
    float       actual_cull_freq;
    int         cull_grid;
    int         status;
    int         BASE_CH_not_found = FALSE;

    register int    i, j, gap;

/*int m = 0;     20170613 Cyrus [Remove unused variable] */

    EXIST_FREQ  *tmp_list;

    EXEC SQL BEGIN DECLARE SECTION;

        char    o_station_type;
        char    o_pw_sign;
        float   o_pw_dbw;
        double  o_tx_freq;
        double  o_rx_freq;
        int     o_base_no;
        int     o_feeder_loss;
        int     o_ant_height;
        int     o_az_max_rad;
        int     o_grid_east;
        int     o_grid_north;
        int     o_cull_east;
        int     o_cull_west;
        int     o_cull_north;
        int     o_cull_south;
        VARCHAR o_lic_type[3];
        VARCHAR o_lic_no[8];
        VARCHAR o_ant_type[3];
        char    o_sys_category;
        VARCHAR o_sys_type[3];
        VARCHAR o_sys_no[8];
        VARCHAR o_sys_suffix[4];
        VARCHAR o_eq_type[11];
        VARCHAR o_sub_district[4];
        VARCHAR o_cancel_date[10];

    EXEC SQL END DECLARE SECTION;

    EXEC SQL INCLUDE SQLCA;


    EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn;
    EXEC SQL WHENEVER NOT FOUND CONTINUE;


/*
#ifdef DEBUG
    printf("select_station\n");
#endif
*/

    cull_grid = subdist[prop->dist_index].cull_dist[prop->band][0];
/*
    printf("cull_grid: %d\n", cull_grid);
*/

    o_cull_east  = prop->east_grid + cull_grid;
    o_cull_west  = prop->east_grid - cull_grid;
    o_cull_north = prop->north_grid + cull_grid;
    o_cull_south = prop->north_grid - cull_grid;

    EXEC SQL DECLARE C21 CURSOR FOR
         SELECT SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO,
                EAST, NORTH, LIC_TYPE, LIC_NO, STATION_TYPE, ANT_TYPE, 
                HALT, AZ_MAX_RAD, PW_SIGN, PW_DBW, SUBDISTRICT,
                NVL(TO_CHAR(CANCEL_DATE), '-')
         FROM   STATION
         WHERE  (NORTH BETWEEN :o_cull_south AND :o_cull_north)
         AND    (EAST BETWEEN :o_cull_west AND :o_cull_east)
         ORDER BY SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO;
/*
         ORDER BY LIC_TYPE, LIC_NO;
*/

    EXEC SQL OPEN C21;

    EXEC SQL
         FETCH C21
         INTO  :o_sys_category, :o_sys_type, :o_sys_no, :o_sys_suffix,
               :o_base_no, :o_grid_east, :o_grid_north, :o_lic_type,
               :o_lic_no, :o_station_type, :o_ant_type, :o_ant_height,
               :o_az_max_rad, :o_pw_sign, :o_pw_dbw, :o_sub_district,
               :o_cancel_date;

    if (sqlca.sqlcode == NOT_FOUND)
    {
        EXEC SQL CLOSE C21;
        return ERROR;
    }

    actual_cull_freq = desensit_cull_freq + FREQ_EPSILON;

    for (i = 0; i < prop_fq_cnt; i++)
    {
        s_fq_list[i]->tx_freq = fq_list[i]->tx_freq;
        s_fq_list[i]->rx_freq = fq_list[i]->rx_freq;
        s_fq_list[i]->tx_channel = fq_list[i]->tx_channel;
        s_fq_list[i]->rx_channel = fq_list[i]->rx_channel;
        s_fq_list[i]->stn_node = fq_list[i]->stn_node;
    }

    fq_cnt = prop_fq_cnt;

    for (i = prop_stn_cnt; ; )
    {
        o_sys_type.arr[o_sys_type.len] = '\0';
        o_sys_no.arr[o_sys_no.len] = '\0';
        o_sys_suffix.arr[o_sys_suffix.len] = '\0';

        o_lic_type.arr[o_lic_type.len] = '\0';
        o_lic_no.arr[o_lic_no.len] = '\0';

/*
printf("%s%-s: %c-%s-%s-%s\n", 
o_lic_type.arr,o_lic_no.arr,o_sys_category,o_sys_type.arr,o_sys_no.arr,o_sys_suffix.arr);
*/

/*
        if (o_sys_category == 'P')
            if ((strcmp(o_sys_type.arr, "06") == 0) 
            ||  (strcmp(o_sys_type.arr, "40") == 0))
*/
        if ((strcmp((char *)o_sys_type.arr, "06") == 0) 
            ||  (strcmp((char *)o_sys_type.arr, "40") == 0))

            {
                EXEC SQL
                     FETCH C21
                     INTO  :o_sys_category, :o_sys_type, :o_sys_no, :o_sys_suffix,
                           :o_base_no, :o_grid_east, :o_grid_north, :o_lic_type, 
                           :o_lic_no, :o_station_type, :o_ant_type, :o_ant_height,
                           :o_az_max_rad, :o_pw_sign, :o_pw_dbw, :o_sub_district,
                           :o_cancel_date;

                if (sqlca.sqlcode == NOT_FOUND)
                {
                    EXEC SQL CLOSE C21;
                    break;
                }

                continue;
            }

        /*************************************************************/
        /*  if this is a cancelled station (CANCEL_DATE is not NULL) */
        /*  or east/north grid is unavailable (east=0 or north=0),   */
        /*  skip it.                                                 */
        /*************************************************************/


/*
        if ((o_grid_east == 0) || (o_grid_north == 0))
        if (o_cancel_date.arr[0] != '-')
*/
        if ((o_grid_east == 0) || (o_grid_north == 0)
        ||  (o_cancel_date.arr[0] != '-'))
        {
            EXEC SQL
                 FETCH C21
                 INTO  :o_sys_category, :o_sys_type, :o_sys_no, :o_sys_suffix,
                       :o_base_no, :o_grid_east, :o_grid_north, :o_lic_type, 
                       :o_lic_no, :o_station_type, :o_ant_type, :o_ant_height,
                       :o_az_max_rad, :o_pw_sign, :o_pw_dbw, :o_sub_district,
                       :o_cancel_date;

            if (sqlca.sqlcode == NOT_FOUND)
            {
                EXEC SQL CLOSE C21;
                break;
            }

            continue;
        }


        if (BASE_CH_not_found)
            BASE_CH_not_found = FALSE;
        else
            if (exist[i] == (EXIST *) NULL)
                if ((exist[i] = (EXIST *) malloc(sizeof(EXIST))) == (EXIST *) NULL)
                {
                    printf("Fatal: cannot allocate space for 'exist' array\n");
                    fflush(afp);
                    fclose(afp);
                    exit(1);
                }

/*
        o_sys_type.arr[o_sys_type.len] = '\0';
        o_sys_no.arr[o_sys_no.len] = '\0';
        o_sys_suffix.arr[o_sys_suffix.len] = '\0';
*/

        EXEC SQL DECLARE C22 CURSOR FOR
             SELECT NVL(TX_FREQ, 0.0), NVL(RX_FREQ, 0.0)
             FROM   BASE_CH
             WHERE  SYS_CATEGORY = :o_sys_category
             AND    SYS_TYPE = :o_sys_type
             AND    SYS_NO = :o_sys_no
             AND    SYS_SUFFIX = :o_sys_suffix
             AND    BASE_NO = :o_base_no
             ORDER BY TX_FREQ;

        EXEC SQL OPEN C22;

        EXEC SQL
             FETCH C22
             INTO  :o_tx_freq, :o_rx_freq;

        if (sqlca.sqlcode == NOT_FOUND)
        {
            fprintf(afp, "No BASE_CH record for %c-%s-%s-%s base no. %d\n",
                    o_sys_category, o_sys_type.arr, o_sys_no.arr,
                    o_sys_suffix.arr, o_base_no);
            EXEC SQL CLOSE C22;

            EXEC SQL
                 FETCH C21
                 INTO  :o_sys_category, :o_sys_type, :o_sys_no, :o_sys_suffix,
                       :o_base_no, :o_grid_east, :o_grid_north, :o_lic_type, 
                       :o_lic_no, :o_station_type, :o_ant_type, :o_ant_height,
                       :o_az_max_rad, :o_pw_sign, :o_pw_dbw, :o_sub_district,
                       :o_cancel_date;

            if (sqlca.sqlcode == NOT_FOUND)
            {
                EXEC SQL CLOSE C21;
                break;
            }

            BASE_CH_not_found = TRUE;
            continue;

/*
            fflush(afp);
            fclose(afp);
            exit(1);
*/
        }

        for (; ; )
        {
/*
system("echo \"esemss0x o_tx_freq o_rx_freq\" >> /tmp/debug");
sprintf(msg, "echo \"%lf %lf\" >> /tmp/debug",
o_tx_freq, o_rx_freq);
system(msg);
*/


//printf("esemss0x o_tx_freq o_rx_freq: %lf %lf %f\n", o_tx_freq,o_rx_freq, actual_cull_freq);

            if ((abs(prop_tx_freq - o_rx_freq) > actual_cull_freq)
            &&  (abs(prop_rx_freq - o_tx_freq) > actual_cull_freq)
            &&  (abs(prop_tx_freq - o_tx_freq) > actual_cull_freq))
            {
                EXEC SQL
                     FETCH C22
                     INTO  :o_tx_freq, :o_rx_freq;

                if (sqlca.sqlcode == NOT_FOUND)
                {
                    EXEC SQL CLOSE C22;
                    break;
                }

                continue;
            }

            if (fq_list[fq_cnt] == (EXIST_FREQ *) NULL)
            {
                fq_list[fq_cnt] = (EXIST_FREQ *) malloc(sizeof(EXIST_FREQ));
                s_fq_list[fq_cnt] = (EXIST_FREQ *) malloc(sizeof(EXIST_FREQ));
            }

            if ((fq_list[fq_cnt] == (EXIST_FREQ *) NULL)
            ||  (s_fq_list[fq_cnt] == (EXIST_FREQ *) NULL))
            {
                printf("Fatal error: cannot allocate space for channels ");
                printf("of existing station\n");
                fflush(afp);
                fclose(afp);
                exit(1);
            }
            fq_list[fq_cnt]->tx_freq = s_fq_list[fq_cnt]->tx_freq = o_tx_freq;
            fq_list[fq_cnt]->rx_freq = s_fq_list[fq_cnt]->rx_freq = o_rx_freq;
            fq_list[fq_cnt]->tx_channel = s_fq_list[fq_cnt]->tx_channel
                                        = (int)(o_tx_freq/MIN_CHANNEL_SEP + .5);
            fq_list[fq_cnt]->rx_channel = s_fq_list[fq_cnt]->rx_channel
                                        = (int)(o_rx_freq/MIN_CHANNEL_SEP + .5);
            fq_list[fq_cnt]->stn_node = s_fq_list[fq_cnt]->stn_node = i;
/*
printf("cnt tx rx txc rxc node: %d %lf %lf %d %d %d\n", fq_cnt,
            fq_list[fq_cnt]->tx_freq,
            fq_list[fq_cnt]->rx_freq,
            fq_list[fq_cnt]->tx_channel,
            fq_list[fq_cnt]->rx_channel,
            fq_list[fq_cnt]->stn_node);
*/
            fq_cnt++;

            EXEC SQL
                 FETCH C22
                 INTO  :o_tx_freq, :o_rx_freq;

            if (sqlca.sqlcode == NOT_FOUND)
            {
                EXEC SQL CLOSE C22;
                break;
            }

            if (fq_cnt == (MAX_EXIST*2 + 500))
            {
                fprintf(afp, "No. of channels exceeds %d\n", (MAX_EXIST*2 + 500));
                EXEC SQL CLOSE C22;
                EXEC SQL CLOSE C21;
                return ERROR;
            }
        }

/*
        exist[i]->no_channels = j;
*/

/*
system("echo \"esemss0x 1\" >> /tmp/debug");
sprintf(msg, "echo \"%c %s %s %s %d\" >> /tmp/debug",
o_sys_category, o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr, o_base_no );
system(msg);
*/

        EXEC SQL
             SELECT NVL(LOSS, 0)
             INTO   :o_feeder_loss
             FROM   ADD_EQ
             WHERE  SYS_CATEGORY = :o_sys_category
             AND    SYS_TYPE = :o_sys_type
             AND    SYS_NO = :o_sys_no
             AND    SYS_SUFFIX = :o_sys_suffix
             AND    BASE_NO = :o_base_no
             AND    ADD_EQ_TYPE = 'L';

        if (sqlca.sqlcode == NOT_FOUND)
	    o_feeder_loss = 0;


        /********************************************************/
        /* if tx mode is SFX, get filter attenuation from table */
        /********************************************************/
	exist[i]->sfx_filter[0] = '\0';
        if (abs(o_tx_freq - o_rx_freq) <= FREQ_EPSILON)
        {

/*
system("echo \"esemss0x 2\" >> /tmp/debug");
sprintf(msg, "echo \"%c %s %s %s %d\" >> /tmp/debug",
o_sys_category, o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr, o_base_no );
system(msg);
*/

/* since all eq_type is null in the table and not used in the program */

/*

            EXEC SQL
                 SELECT EQ_TYPE
                 INTO   :o_eq_type
                 FROM   ADD_EQ
                 WHERE  SYS_CATEGORY = :o_sys_category
                 AND    SYS_TYPE = :o_sys_type
                 AND    SYS_NO = :o_sys_no
                 AND    SYS_SUFFIX = :o_sys_suffix
                 AND    BASE_NO = :o_base_no
                 AND    ADD_EQ_TYPE = 'F';

Alex Yeung 1999-08-06 */

            if (sqlca.sqlcode == NOT_FOUND)
            {
                fprintf(afp, "No SFX filter for %c-%s-%s-%s base no. %d\n",
                        o_sys_category, o_sys_type.arr, o_sys_no.arr,
                        o_sys_suffix.arr, o_base_no);
/*
fprintf(afp, "suffix: %d %d %d %d %d %d %d\n",
o_sys_no.arr[0], o_sys_no.arr[1], o_sys_no.arr[2], o_sys_no.arr[3],
o_sys_no.arr[4], o_sys_no.arr[5], o_sys_no.arr[6], o_sys_no.arr[7]);
fprintf(afp, "suffix: %d %d %d\n", o_sys_suffix.arr[0],
o_sys_suffix.arr[1], o_sys_suffix.arr[2]);
*/
            }
            else
            {
                o_eq_type.arr[o_eq_type.len] = '\0';
/*	        strcpy(exist[i]->sfx_filter, o_eq_type.arr); */
	        strcpy(exist[i]->sfx_filter, (char *)o_eq_type.arr);
            }
        }

        o_sub_district.arr[o_sub_district.len] = '\0';
/*        strcpy(exist[i]->sub_district, o_sub_district.arr); */
        strcpy(exist[i]->sub_district, (char *)o_sub_district.arr);
        
        status = get_district(exist[i]->sub_district,
                              &exist[i]->dist_type,
                              &exist[i]->noise_code,
                              &exist[i]->dist_index);

        if (status == ERROR)
        {
            fprintf(afp, "Error: sub-district code (%s) not found ",
                    exist[i]->sub_district);
            fprintf(afp, "for %c-%s-%s-%s\n", o_sys_category, o_sys_type.arr,
                    o_sys_no.arr, o_sys_suffix.arr);
        }
        else
        {
            exist[i]->sys_category = o_sys_category;
/*
            strcpy(exist[i]->sys_type,  o_sys_type.arr);
            strcpy(exist[i]->sys_no,  o_sys_no.arr);
            strcpy(exist[i]->sys_suffix,  o_sys_suffix.arr);
*/
            strcpy(exist[i]->sys_type,  (char *)o_sys_type.arr);
            strcpy(exist[i]->sys_no,  (char *)o_sys_no.arr);
            strcpy(exist[i]->sys_suffix,  (char *)o_sys_suffix.arr);

            o_lic_type.arr[o_lic_type.len] = '\0';
/*            strcpy(exist[i]->lic_type, o_lic_type.arr); */
            strcpy(exist[i]->lic_type, (char *)o_lic_type.arr);
            o_lic_no.arr[o_lic_no.len] = '\0';
/*            strcpy(exist[i]->lic_no, o_lic_no.arr); */
            strcpy(exist[i]->lic_no, (char *)o_lic_no.arr);
            
            exist[i]->station_type = o_station_type;
            exist[i]->base_no = o_base_no;

            exist[i]->east_grid  = o_grid_east;
            exist[i]->north_grid = o_grid_north;

            exist[i]->pw_dbw = (o_pw_sign == '-')? (-1 * o_pw_dbw):o_pw_dbw;

            o_ant_type.arr[o_ant_type.len] = '\0';
/*	    strcpy(exist[i]->antenna, o_ant_type.arr); */
        strcpy(exist[i]->antenna, (char *)o_ant_type.arr);
            exist[i]->ant_height = o_ant_height;
	    exist[i]->az_max_rad = o_az_max_rad;
	    exist[i]->az_max_rad_r = (float)exist[i]->az_max_rad / 180 * M_PI;
	    exist[i]->feed_loss  = o_feeder_loss;
            exist[i]->height_asl = 
                      get_local_height((float)exist[i]->east_grid/10,
                                       (float)exist[i]->north_grid/10);
/*
printf("ht: %f\n", exist[i]->height_asl);
*/
            i++;
        }

        EXEC SQL
             FETCH C21
             INTO  :o_sys_category, :o_sys_type, :o_sys_no, :o_sys_suffix,
                   :o_base_no, :o_grid_east, :o_grid_north, :o_lic_type, 
                   :o_lic_no, :o_station_type, :o_ant_type, :o_ant_height,
                   :o_az_max_rad, :o_pw_sign, :o_pw_dbw, :o_sub_district,
                   :o_cancel_date;


        if (sqlca.sqlcode == NOT_FOUND)
        {
            EXEC SQL CLOSE C21;
            break;
        }

        if (i == MAX_EXIST)
        {
            fprintf(afp, "No. of stations exceeds %d\n", MAX_EXIST);
            EXEC SQL CLOSE C21;
            return ERROR;
        }
		
    }

    cull_stn_cnt = i;

/**********************************************************************/
/*  sort all channels in ascending order using shell sort             */
/**********************************************************************/

    for (gap = fq_cnt / 2; gap > 0; gap /= 2)
        for (i = gap; i < fq_cnt; i++)
            for (j = i - gap; 
                (j >= 0) && (s_fq_list[j]->rx_freq > s_fq_list[j+gap]->rx_freq);
                 j -= gap)
            {
                tmp_list         = s_fq_list[j];
                s_fq_list[j]     = s_fq_list[j+gap];
                s_fq_list[j+gap] = tmp_list;
            }

/*
printf("EXIT select_station\n");
*/

/*
if (interactive == FALSE)
    printf("cull_stn_cnt fq_cnt: %d %d\n", cull_stn_cnt, fq_cnt);
*/

/* commented out by Chen Yung
    return OK;
*/

/* changed by Chen Yung */
/* original: */
/*    return OK; */
    return OK;


sqlerr_rtn:
/* changed by Chen Yung */
/* original: */
/*    printf("%s\c", sqlca.sqlerrm.sqlerrmc); */
    printf("%s\n", sqlca.sqlerrm.sqlerrmc);
    fflush(afp);
    fclose(afp);
    exit(1);

}
