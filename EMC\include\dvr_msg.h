/* @(#)dvr_msg.h	6.1	(ULTRIX)	11/19/91	*/
/********************************************************************************************************************************/
/* Created  7-JUL-1990 05:58:34 by VAX SDL V3.2-12     Source:  7-JUL-1990 05:58:31 DDIF$DEVELOP_ROOT:[DDIFBUILD.DVR.SRC]DVR$MSG. */
/********************************************************************************************************************************/
 
/*** MODULE $DVRDEF ***/
/*                                                                          */
/* This SDL File Generated by VAX-11 Message V04-00 on  7-JUL-1990 05:58:31.33 */
/*                                                                          */
/*++                                                                        */
/*                                                                          */
/* COPYRIGHT (C) 1988, 1990                                                 */
/* DIGITAL EQUIPMENT CORPORATION, MAYNARD, MASSACHUSETTS 01754              */
/*  ALL RIGHTS RESERVED.                                                    */
/*                                                                          */
/*  THIS SOFTWARE IS FURNISHED UNDER A LICENSE AND MAY BE USED AND  COPIED  */
/*  ONLY  IN  ACCORDANCE  WITH  THE  TERMS  OF  SUCH  LICENSE AND WITH THE  */
/*  INCLUSION OF THE ABOVE COPYRIGHT NOTICE.  THIS SOFTWARE OR  ANY  OTHER  */
/*  COPIES  THEREOF MAY NOT BE PROVIDED OR OTHERWISE MADE AVAILABLE TO ANY  */
/*  OTHER PERSON.  NO TITLE TO AND OWNERSHIP OF  THE  SOFTWARE  IS  HEREBY  */
/*  TRANSFERRED.                                                            */
/*                                                                          */
/*  THE INFORMATION IN THIS SOFTWARE IS SUBJECT TO CHANGE  WITHOUT  NOTICE  */
/*  AND  SHOULD  NOT  BE  CONSTRUED  AS  A COMMITMENT BY DIGITAL EQUIPMENT  */
/*  CORPORATION.                                                            */
/*                                                                          */
/*  DIGITAL ASSUMES NO RESPONSIBILITY FOR THE USE OR  RELIABILITY  OF  ITS  */
/*  SOFTWARE ON EQUIPMENT WHICH IS NOT SUPPLIED BY DIGITAL.                 */
/*                                                                          */
/*                                                                          */
/* ABSTRACT:                                                                */
/*	THIS IS THE MESSAGE FILE CONTAINING MESSAGES AND STATUS CODES USED  */
/*	BY THE CDA VIEWER APPLICATION.					    */
/*                                                                          */
/*************************************************************************** */
/*--                                                                        */
#define DVR$_FACILITY 859
#define DVR$_NORMAL 56328201
#define DVR$_FILENOTFOUND 56328211
#define DVR$_NOFONT 56328219
#define DVR$_TOPOFDOC 56328227
#define DVR$_INVALREQ 56328235
#define DVR$_EOD 56328243
#define DVR$_EOC 56328251
#define DVR$_SCRFULL 56328259
#define DVR$_DEFAULTFONT 56328267
#define DVR$_ALREADYWIDGET 56328275
#define DVR$_NODISPCONT 56328283
#define DVR$_FILENOTOPEN 56328291
#define DVR$_OPENFAIL 56328300
#define DVR$_INVADDR 56328308
#define DVR$_INVTEXTUNIT 56328316
#define DVR$_INVFILETYPE 56328324
#define DVR$_NOCONVERTER 56328332
#define DVR$_MEMALLOFAIL 56328340
#define DVR$_MEMDEALLOFAIL 56328348
#define DVR$_INTERNALERROR 56328356
#define DVR$_BADFRAMETYPE 56328364
#define DVR$_DDIFERR 56328372
#define DVR$_DDIFDELERR 56328380
#define DVR$_NOTDDIFDOC 56328388
#define DVR$_BADINFILENAME 56328396
#define DVR$_BADOUTFILENAME 56328404
#define DVR$_OUTFILEOPEN 56328412
#define DVR$_OUTFILECLOSE 56328420
#define DVR$_OUTFILEWRITE 56328428
#define DVR$_FATALERROR 56328436
#define DVR$_DRMREGISTERFAIL 56328444
#define DVR$_DRMHIERARCHYFAIL 56328452
#define DVR$_DRMMAINFETCHFAIL 56328460
#define DVR$_DRMSTRINGFETCHFAIL 56328468
#define DVR$_DRMPOPUPFETCHFAIL 56328476
#define DVR$_NOTBITONAL 56328483
#define DVR$_IMAGEFAIL 56328491
#define DVR$_GRAPHICFAIL 56328499
/*                                                                          */
/* NEW V2 ERROR MESSAGES                                                    */
/*                                                                          */
#define DVR$_UNKOBJTYPE 56328507
#define DVR$_PAGENOTFOUND 56328515
#define DVR$_FORMATINFO 56328523
#define DVR$_TEXTFAIL 56328531
#define DVR$_FORMATWARN 56328536
#define DVR$_UNKSTRTYPE 56328548
#define DVR$_BADPARAM 56328556
#define DVR$_FORMATERROR 56328564
#define DVR$_NOPSHEAD 56328572
#define DVR$_NODPSEXT 56328580
#define DVR$_NOPAGE 56328588
#define DVR$_OBSOLETE 56328594
#define DVR$_BADCOMMENTS 56328600
#define DVR$_LOADINPROGRESS 56328610
/* END OF MESSAGE FILE                                                      */
