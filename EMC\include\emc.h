char    user_id[20];        /* user login name       */
char	sys_date[9];        /* system date           */
char	sys_time[9];        /* system time           */
char	yymmdd[7];          /* system date in yymmdd */
char	hhmmss[7];          /* system time in hhmmss */
char	printer_id[15];     /* destinated printer id */
char	print_file[100];    /* destinated print file */
char	*emc_dir;           /* EMC home directory    */
char	emc_uid[20];        /* EMC user id.          */

FILE    *afp;               /* file pointer to audit file    */

int     prop_stn_cnt;       /* no. of proposed stations      */
int     prop_fq_cnt;        /* no. of proposed freq channels */
int     cull_stn_cnt;       /* no. of culling stations       */

int     interactive;        /* 'TRUE' : interactive EMC      */
                            /* 'FALSE': batched EMC          */

/*****************************************************/
/*           Variables for summary totals            */
/*****************************************************/
int     cochan_tot;
int     desen_tot;
int     intmod2_vict_tot;
int     intmod2_tx1_tot;
int     intmod2_tx2_tot;
int     intmod3_vict_tot;
int     intmod3_tx1_tot;
int     intmod3_tx3_tot;
