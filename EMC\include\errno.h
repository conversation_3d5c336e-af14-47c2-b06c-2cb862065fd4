/*	@(#)errno.h	2.4	(ULTRIX)	11/10/89	*/

/************************************************************************
 *									*
 *			Copyright (c) 1984, 1987 by			*
 *		Digital Equipment Corporation, Maynard, MA		*
 *			All rights reserved.				*
 *									*
 *   This software is furnished under a license and may be used and	*
 *   copied  only  in accordance with the terms of such license and	*
 *   with the  inclusion  of  the  above  copyright  notice.   This	*
 *   software  or  any  other copies thereof may not be provided or	*
 *   otherwise made available to any other person.  No title to and	*
 *   ownership of the software is hereby transferred.			*
 *									*
 *   This software is  derived  from  software  received  from  the	*
 *   University    of   California,   Berkeley,   and   from   Bell	*
 *   Laboratories.  Use, duplication, or disclosure is  subject  to	*
 *   restrictions  under  license  agreements  with  University  of	*
 *   California and with AT&T.						*
 *									*
 *   The information in this software is subject to change  without	*
 *   notice  and should not be construed as a commitment by Digital	*
 *   Equipment Corporation.						*
 *									*
 *   Digital assumes no responsibility for the use  or  reliability	*
 *   of its software on equipment which is not supplied by Digital.	*
 *									*
 ************************************************************************/
/*
 *
 *   Modification history:
 *
 * 10 Jul 89 -- jlr
 *	Added ENOSYS for POSIX
 *
 * 13 Jan 88 -- map
 *	Added ENOLCK for POSIX
 *
 * 4 Aug 86 -- chet
 *	Moved NFS error codes to match SUN's.
 *
 * 24 Feb 86 -- depp
 *	Added EALIGN error code
 *
 * 28-Mar-85 -- David L Ballenger
 *	Add mapping of System V error numbers from BRL package.
 *
 * 01 Mar 85 -- depp
 *	Added System V IPC error codes error codes.
 *
 */

/*
 * Error codes
 */

#define	EPERM		1		/* Not owner */
#define	ENOENT		2		/* No such file or directory */
#define	ESRCH		3		/* No such process */
#define	EINTR		4		/* Interrupted system call */
#define	EIO		5		/* I/O error */
#define	ENXIO		6		/* No such device or address */
#define	E2BIG		7		/* Arg list too long */
#define	ENOEXEC		8		/* Exec format error */
#define	EBADF		9		/* Bad file number */
#define	ECHILD		10		/* No children */
#define	EAGAIN		11		/* No more processes */
#define	ENOMEM		12		/* Not enough core */
#define	EACCES		13		/* Permission denied */
#define	EFAULT		14		/* Bad address */
#define	ENOTBLK		15		/* Block device required */
#define	EBUSY		16		/* Mount device busy */
#define	EEXIST		17		/* File exists */
#define	EXDEV		18		/* Cross-device link */
#define	ENODEV		19		/* No such device */
#define	ENOTDIR		20		/* Not a directory*/
#define	EISDIR		21		/* Is a directory */
#define	EINVAL		22		/* Invalid argument */
#define	ENFILE		23		/* File table overflow */
#define	EMFILE		24		/* Too many open files */
#define	ENOTTY		25		/* Not a typewriter */
#define	ETXTBSY		26		/* Text file busy */
#define	EFBIG		27		/* File too large */
#define	ENOSPC		28		/* No space left on device */
#define	ESPIPE		29		/* Illegal seek */
#define	EROFS		30		/* Read-only file system */
#define	EMLINK		31		/* Too many links */
#define	EPIPE		32		/* Broken pipe */

/* math software */
#define	EDOM		33		/* Argument too large */
#define	ERANGE		34		/* Result too large */

/* non-blocking and interrupt i/o */
#define	EWOULDBLOCK	35		/* Operation would block */
#define	EINPROGRESS	36		/* Operation now in progress */
#define	EALREADY	37		/* Operation already in progress */
/* ipc/network software */

	/* argument errors */
#define	ENOTSOCK	38		/* Socket operation on non-socket */
#define	EDESTADDRREQ	39		/* Destination address required */
#define	EMSGSIZE	40		/* Message too long */
#define	EPROTOTYPE	41		/* Protocol wrong type for socket */
#define	ENOPROTOOPT	42		/* Protocol not available */
#define	EPROTONOSUPPORT	43		/* Protocol not supported */
#define	ESOCKTNOSUPPORT	44		/* Socket type not supported */
#define	EOPNOTSUPP	45		/* Operation not supported on socket */
#define	EPFNOSUPPORT	46		/* Protocol family not supported */
#define	EAFNOSUPPORT	47		/* Address family not supported by protocol family */
#define	EADDRINUSE	48		/* Address already in use */
#define	EADDRNOTAVAIL	49		/* Can't assign requested address */

	/* operational errors */
#define	ENETDOWN	50		/* Network is down */
#define	ENETUNREACH	51		/* Network is unreachable */
#define	ENETRESET	52		/* Network dropped connection on reset */
#define	ECONNABORTED	53		/* Software caused connection abort */
#define	ECONNRESET	54		/* Connection reset by peer */
#define	ENOBUFS		55		/* No buffer space available */
#define	EISCONN		56		/* Socket is already connected */
#define	ENOTCONN	57		/* Socket is not connected */
#define	ESHUTDOWN	58		/* Can't send after socket shutdown */
#define	ETOOMANYREFS	59		/* Too many references: can't splice */
#define	ETIMEDOUT	60		/* Connection timed out */
#define	ECONNREFUSED	61		/* Connection refused */

	/* */
#define	ELOOP		62		/* Too many levels of symbolic links */
#define	ENAMETOOLONG	63		/* File name too long */

/* should be rearranged */
#define	EHOSTDOWN	64		/* Host is down */
#define	EHOSTUNREACH	65		/* No route to host */
#define	ENOTEMPTY	66		/* Directory not empty */

/* quotas & mush */
#define	EPROCLIM	67		/* Too many processes */
#define	EUSERS		68		/* Too many users */
#define	EDQUOT		69		/* Disc quota exceeded */

/* NFS error codes */
#define ESTALE		70
#define EREMOTE		71

/* IPC errors
 */
#define ENOMSG		72		/* No message of desired type */
#define EIDRM		73		/* Identifier removed */

/* Alignment error of some type (i.e., cluster, page, block ...) */
#define EALIGN		74		/* alignment error */

/* System V mappings from BRL package
 */
#define EDEADLK		EWOULDBLOCK	/* resource deadlock would occur */

/* POSIX errnos
 */
#define	ENOLCK		75		/* LOCK_MAX exceeded	*/
#define	ENOSYS		76		/* Function not implemented */

/*
 * DUP (Diagnostic/Utilities Protocol) related error numbers.
 */
#define EACTIVE		77		/* Device is active. */
#define ENOACTIVE	78		/* Device is not active. */
#define ENORESOURCES	79		/* No resources available. */
#define ENOSYSTEM	80		/* No system  block found. */
#define ENODUST		81		/* GETDUST failed */
#define EDUPNOCONN	82		/* MSDUP could not connect to device. */
#define EDUPNODISCONN	83		/* MSDUP could not disconnect. */
#define EDUPNOTCNTD	84		/* Server is not connected. */
#define EDUPNOTIDLE	85		/* Server is not idle. */
#define EDUPNOTWAIT	86		/* Server is not waiting for input. */
#define EDUPNOTRUN	87		/* Server is not running program. */
#define EDUPBADOPCODE	88		/* OP Code not valid. */
#define EDUPINTRANSIT	89		/* An state transition event in progress. */
#define	EDUPTOOMANYCPUS	90		/* Only single cpu systems supported.	*/
/* External definition of errno from System V
 */
#ifndef KERNEL
extern int errno;
#endif /* KERNEL */
