#include "../include/existdef.h"

EXIST       *exist[MAX_EXIST];            /* pointer array to station info.   */
EXIST_FREQ  *fq_list[MAX_EXIST*2+500];    /* pointer array to station channel */
EXIST_FREQ  *s_fq_list[MAX_EXIST*2+500];  /* fq_list in ascending order of    */
                                          /* station channel                  */
int          fq_cnt;                      /* count no. of station channels    */

double  exist_tx_freq;                    /* tx freq of current EXIST station */
double  exist_rx_freq;                    /* rx freq of current EXIST station */

int     e_idx;                            /* array index of current station   */
