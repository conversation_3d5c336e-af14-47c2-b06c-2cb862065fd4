struct  exist_tag
{
        char    emc_uid[20];      /* EMC user id.                      */
        char    sys_category;
        char    sys_type[3];
        char    sys_no[8];
        char    sys_suffix[4];
        char    lic_type[3];      /* licence type                        */
        char    lic_no[8];        /* licence no.                         */
        char    station_type;
        char    sub_district[4];  /* sub-district code                   */
        float   desen_att_db;     /* desensit attenuation level          */
        float   intmod_att_db;    /* intermod attenuation level          */
        int     east_grid;
        int     north_grid;
        int     base_no;
        float   pw_dbw;
	char    antenna[3];
        int     az_max_rad;     /* azimuth of max. radiation             */
        float   az_max_rad_r;   /* azimuth of max. radiation in radian   */
        int     ant_height;	/* height above local terrain            */
				/* (height of antenna inclusive)         */
	float   height_asl; 	/* height above see level of local       */
				/* terrain                               */
	int     ant_gain;       /* max. antenna gain?                    */
	int     feed_loss;      /* obtained from table 'add_eq'          */
	char    sfx_filter[11]; /* SFX filter attenuation in DB          */
        int     no_channels;    /* no. of channels owned by base station */
                                /* obtained from table 'base_ch'         */
        char    mode;           /* mode of transmission                  */
        int     band;           /* band category, eg., VHF, UHF          */
        char    dist_type;      /* sub-district type code 'B', 'H', 'O'  */
        int     dist_index;     /* pointer to subdist array              */
        int     noise_code;     /* 1 : busy,  2 : average,  3 : low      */
};

struct  exist_freq_tag
{
        double  tx_freq;        /* transmit freq of current station  */
        double  rx_freq;        /* receive freq of current station   */
        int     tx_channel;     /* transmit freq of current station  */
                                /* in multiple of 0.0125MHz          */
        int     rx_channel;     /* receive freq of current station   */
                                /* in multiple of 0.0125MHz          */
        char    act_flag;       /* whether this frequency is         */
                                /* activated                         */
        int     stn_node;       /* point to index of current station */
};

typedef struct exist_tag       EXIST;
typedef struct exist_freq_tag  EXIST_FREQ;
