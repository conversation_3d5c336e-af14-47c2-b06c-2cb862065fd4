/*
 *	@(#)ndbm.h	6.2     (ultrix)        3/24/92
 */

/*******************************************************************
 *   Copyright (c) Digital Equipment Corporation, 1992             *
 *   All Rights Reserved.  Unpublished rights reserved             *
 *   under the copyright laws of the United States.                *
 *                                                                 *
 *   The software contained on this media is proprietary           *
 *   to and embodies the confidential technology of                *
 *   Digital Equipment Corporation.  Possession, use,              *
 *   duplication or dissemination of the software and              *
 *   media is authorized only pursuant to a valid written          *
 *   license from Digital Equipment Corporation.                   *
 *                                                                 *
 *   RESTRICTED RIGHTS LEGEND   Use, duplication, or               *
 *   disclosure by the U.S. Government is subject to               *
 *   restrictions as set forth in Subparagraph (c)(1)(ii)          *
 *   of DFARS 252.227-7013, or in FAR 52.227-19, as                *
 *   applicable.                                                   *
 *                                                                 *
 *   This software is derived from software received from the      *
 *   University of California, Berkeley, and from Bell             *
 *   Laboratories.  Use, duplication, or disclosure is subject to  *
 *   restrictions under license agreements with University of      *
 *   California and with AT&T.				           *
 *******************************************************************/

/*******************************************************************
 * Modification history
 *
 * 01	28-Feb-92	Jim Morgan
 *			Modified for dynamic pagesize (Thanks to Jim Swist)
 */

/*
 * Hashed key data base library modified for dynamic pagesize
 */
#define PBLKSIZ 1024		/* This is just the default */
#define DBLKSIZ 4096

typedef struct {
	int	dbm_dirf;		/* open directory file */
	int	dbm_pagf;		/* open page file */
	int	dbm_flags;		/* flags, see below */
	long	dbm_maxbno;		/* last ``bit'' in dir file */
	long	dbm_bitno;		/* current bit number */
	long	dbm_hmask;		/* hash mask */
	long	dbm_blkptr;		/* current block for dbm_nextkey */
	int	dbm_keyptr;		/* current key for dbm_nextkey */
	long	dbm_blkno;		/* current page to read/write */
	long	dbm_pagbno;		/* current page in pagbuf */
	char	*dbm_pagbuf;	        /* page file block buffer */
	char	*dbm_ovfbuf;	        /* page file overflow buffer */
	long	dbm_dirbno;		/* current block in dirbuf */
	int     dbm_pblksiz;            /* page file block size */
	char	dbm_dirbuf[DBLKSIZ];	/* directory file block buffer */
} DBM;

#define _DBM_RDONLY	0x1	/* data base open read-only */
#define _DBM_IOERR	0x2	/* data base I/O error */

#define dbm_rdonly(db)	((db)->dbm_flags & _DBM_RDONLY)

#define dbm_error(db)	((db)->dbm_flags & _DBM_IOERR)
	/* use this one at your own risk! */
#define dbm_clearerr(db)	((db)->dbm_flags &= ~_DBM_IOERR)

/* for flock(2) and fstat(2) */
#define dbm_dirfno(db)	((db)->dbm_dirf)
#define dbm_pagfno(db)	((db)->dbm_pagf)

typedef struct {
	char	*dptr;
	int	dsize;
} datum;

/*
 * flags to dbm_store()
 */
#define DBM_INSERT	0
#define DBM_REPLACE	1

DBM	*dbm_open();
void	dbm_close();
datum	dbm_fetch();
datum	dbm_firstkey();
datum	dbm_nextkey();
long	dbm_forder();
int	dbm_delete();
int	dbm_store();
