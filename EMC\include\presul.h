double  delta_freq;         /* freq diff % victim and interfering stations */
/*
double  sign_delta_freq;
*/
float   delta_dist;        /* distance % victim and interfering stations   */
double  power_intr;        /* interfering power from interfering           */
                           /* station to victim station                    */
char    intrfr_type[4];    /* interfering type, currently unused           */
float   attenuation;       /* computed attenuation required                */
char    victim_is[6];      /* either 'PROP' or 'EXIST'                     */

float   prev_diff_loss;    /* previous diffration loss value               */
float   prev_tx_grid[2];   /* grid of interfering station in previous      */
                           /* diffraction loss calculation                 */
float   prev_rx_grid[2];   /* grid of victim station in previous           */
                           /* diffraction loss calculation                 */
