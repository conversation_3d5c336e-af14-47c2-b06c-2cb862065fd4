/* refer to 'existdef.h' or 'propdef.h' for field meanings */

struct victim_tag
{
    float    grid[2];
    char     system_id[18];      /* in the form of X-XXX-XXXXXXX-XXX */
    double   rx_freq;
    int      feed_loss;
    double   mob_rx_freq;        /* rx freq of mobile station */
    char     stn_type;
    int      base_no;
    char     antenna[3];
    float    ant_height;
    int      az_max_rad;
    float    az_max_rad_r;
    float    ant_gain;          /* antenna gain in dB */
    char     sfx_filter[11];
    float    pw_dbw;
    char     mode;
    int      band;
    int      dist_index;
};

typedef     struct victim_tag    VICTIM;
