esemba0x.c:        prop->east_grid  = prop->east_grid * 10;
esemba0x.c:        prop->north_grid = prop->north_grid * 10;
esemba0x.c:        prop->az_max_rad = atoi(tmp_az);
esemba0x.c:        prop->az_max_rad_r = (float)prop->az_max_rad / 180 * M_PI;
esemba0x.c:            prev_stn_node = prop->stn_node = i = fq_list[j]->stn_node;
esemba0x.c:            prop->sys_category = exist[i]->sys_category;
esemba0x.c:            strcpy(prop->sys_type, exist[i]->sys_type);
esemba0x.c:            strcpy(prop->sys_no, exist[i]->sys_no);
esemba0x.c:            strcpy(prop->sys_suffix, exist[i]->sys_suffix);
esemba0x.c:            prop->east_grid = exist[i]->east_grid;
esemba0x.c:            prop->north_grid = exist[i]->north_grid;
esemba0x.c:            strcpy(prop->sub_district, exist[i]->sub_district);
esemba0x.c:            prop->station_type = exist[i]->station_type;
esemba0x.c:            prop->desen_att_db = exist[i]->desen_att_db;
esemba0x.c:            prop->intmod_att_db = exist[i]->intmod_att_db;
esemba0x.c:            strcpy(prop->antenna, exist[i]->antenna);
esemba0x.c:            prop->ant_height = exist[i]->ant_height;
esemba0x.c:            prop->pw_dbw = exist[i]->pw_dbw;
esemba0x.c:            prop->az_max_rad = exist[i]->az_max_rad;
esemba0x.c:            prop->az_max_rad_r = exist[i]->az_max_rad_r;
esemba0x.c:            prop->feed_loss = exist[i]->feed_loss;
esemba0x.c:	    strcpy(prop->sfx_filter, exist[i]->sfx_filter);
esemba0x.c:            prop->height_asl = exist[i]->height_asl;
esemba0x.c:            prop->dist_type = exist[i]->dist_type;
esemba0x.c:            prop->noise_code = exist[i]->noise_code;
esemba0x.c:            prop->dist_index = exist[i]->dist_index;
esemba0x.c:            emc_uid, prop->east_grid, prop->north_grid, prop->sub_district,
esemba0x.c:            prop->station_type, prop->desen_att_db, prop->intmod_att_db,
esemba0x.c:            prop->antenna, prop->ant_height,
esemba0x.c:            prop->pw_dbw,   prop->az_max_rad, prop->feed_loss);
esemba0x.c:	    prop->sfx_filter, prop->height_asl, prop->noise_code, 
esemba0x.c:            prop->dist_type, prop->dist_index);
esemba0x.c:        prop->tx_channel = fq_list[j]->tx_channel;
esemba0x.c:        prop->rx_channel = fq_list[j]->rx_channel;
esemba0x.c:                prop_tx_freq, prop_rx_freq, "", prop->north_grid, 
esemba0x.c:                prop->east_grid, "", sys_time);
esemba0x.c:        prop->east_grid, prop->north_grid, prop->sub_district,
esemba0x.c:        prop->station_type, prop->att_db, prop->antenna, prop->ant_height,
esemba0x.c:        prop->pw_dbw,   prop->az_max_rad, prop->feed_loss);
esemba0x.c:	prop->sfx_filter, prop->tx_freq[i], prop->rx_freq[i],
esemba0x.c:	prop->height_asl, prop->mode, prop->band, prop->noise_code, 
esemba0x.c:        prop->dist_type, prop->dist_index);
esemba0x.c:   fprintf(sfp, "*  PROP. SYSTEM    : %c%s%s-%s%32s*\n", prop->sys_category,
esemba0x.c:           prop->sys_type, prop->sys_no, prop->sys_suffix, "");
esemba0x.c:          prop->east_grid, prop->north_grid);
esemba0x.c:          prop->sub_district, prop->pw_dbw);
esemba0x.c:          prop->ant_height, prop->height_asl);
esemba0x.c:          prop->antenna, prop->az_max_rad);
esemba0x.c:          prop->desen_att_db, prop->intmod_att_db);
esemba0x.c:   if (prop->sfx_filter[0] != '\0')
esemba0x.c:              prop->feed_loss, prop->sfx_filter);
esemba0x.c:              prop->feed_loss, "");
esemba0x.c:         prop->station_type, "");
esemco0r.c:        if ((prop->tx_channel == fq_list[i]->tx_channel)
esemco0r.c:        &&  (prop->rx_channel == fq_list[i]->rx_channel))
esemco0r.c:            if (prop->stn_node != fq_list[i]->stn_node)
esemco0r.c:           prop->sys_category, prop->sys_type, prop->sys_no, 
esemco0r.c:           prop->sys_suffix, "");
esemda0r.c:		subdist[prop->dist_index].cull_dist[prop->band][0]);
esemda0r.c:            if (prop->stn_node != s_fq_list[j]->stn_node)
esemdc0r.c:    if (prop->mode != TX_ONLY)
esemdc0r.c:    strcpy(vict.antenna, prop->antenna);
esemdc0r.c:    vict.grid[0]     = (float)prop->east_grid;
esemdc0r.c:    vict.grid[1]     = (float)prop->north_grid;
esemdc0r.c:    vict.feed_loss   = prop->feed_loss;
esemdc0r.c:    vict.pw_dbw      = prop->pw_dbw;
esemdc0r.c:printf("**ant_ht asl: %d %f\n", prop->ant_height,prop->height_asl);
esemdc0r.c:    vict.ant_height  = max(prop->ant_height+prop->height_asl,
esemdc0r.c:    vict.az_max_rad   = prop->az_max_rad;
esemdc0r.c:    vict.az_max_rad_r = prop->az_max_rad_r;
esemdc0r.c:    vict.stn_type     = prop->station_type;
esemdc0r.c:    vict.mode         = prop->mode;
esemdc0r.c:    vict.band         = prop->band;
esemdc0r.c:    vict.dist_index   = prop->dist_index;
esemdc0r.c:    if (prop->sfx_filter[0] != '\0')
esemdc0r.c:        strcpy(vict.sfx_filter, prop->sfx_filter);
esemdc0r.c:    intr.pw_dbw     = prop->pw_dbw;
esemdc0r.c:    intr.grid[0]    = (float)prop->east_grid;
esemdc0r.c:    intr.grid[1]    = (float)prop->north_grid;
esemdc0r.c:    intr.ant_height = max(prop->ant_height+prop->height_asl, MIN_ANT_HEIGHT);
esemdc0r.c:    intr.stn_type   = prop->station_type;
esemdc0r.c:    intr.mode       = prop->mode;
esemdc0r.c:    intr.dist_index = prop->dist_index;
esemdc0r.c:   fprintf(dfp, "*  PROP. SYSTEM    : %c%s%s-%s%31s*\n", prop->sys_category,
esemdc0r.c:           prop->sys_type, prop->sys_no, prop->sys_suffix, "");
esemdc0r.c:          prop->east_grid, prop->north_grid);
esemdc0r.c:          prop->sub_district, prop->pw_dbw);
esemdc0r.c:          prop->ant_height, prop->height_asl);
esemdc0r.c:          prop->antenna, prop->az_max_rad);
esemdc0r.c:          prop->feed_loss, prop->desen_att_db);
esemdc0r.c:   if (prop->sfx_filter[0] != '\0')
esemdc0r.c:              prop->station_type, prop->sfx_filter);
esemdc0r.c:               prop->station_type, "");
esemdl0x.c:            v = 2.0 * delta_dist * tx_tan * rx_tan / wave_len[prop->band];
esemdl0x.c:                fprintf(afp, "proposed grid: %d(N) %d(E)\n", prop->north_grid, 
esemdl0x.c:                        prop->east_grid);
esemim0r.c:        printf("intmod_att_db = %-6.2f\n", prop->intmod_att_db);
esemim0r.c:    bw3db_channel = (int)(bw3db[prop->band]/MIN_CHANNEL_SEP + .005);
esemim0r.c:    if (prop->mode == TX_ONLY)
esemim0r.c:        if (fq_list[i]->stn_node == prop->stn_node)
esemim0r.c:            if (fq_list[j]->stn_node == prop->stn_node)
esemim0r.c:    if (prop->mode == RX_ONLY)
esemim0r.c:        if (fq_list[i]->stn_node == prop->stn_node)
esemim0r.c:            if (fq_list[j]->stn_node == prop->stn_node)
esemim0r.c:        if (fq_list[i]->stn_node == prop->stn_node)
esemim0r.c:            if (fq_list[j]->stn_node == prop->stn_node)
esemim0r.c:            rx_grid[0]  = (float)prop->east_grid / 10;
esemim0r.c:            rx_grid[1]  = (float)prop->north_grid / 10;
esemim0r.c:            rx_grid[0]  = (float)prop->east_grid / 10;
esemim0r.c:            rx_grid[1]  = (float)prop->north_grid / 10;
esemim0r.c:            tx_grid[0]  = (float)prop->east_grid / 10;
esemim0r.c:            tx_grid[1]  = (float)prop->north_grid / 10;
esemim0r.c:            tx_grid[0]  = (float)prop->east_grid / 10;
esemim0r.c:            tx_grid[1]  = (float)prop->north_grid / 10;
esemim0r.c:    fprintf(mfp, "* PROP. SYSTEM    : %c%s%s-%s%29s*\n", prop->sys_category,
esemim0r.c:           prop->sys_type, prop->sys_no, prop->sys_suffix, "");
esemim0r.c:            prop->east_grid, prop->north_grid);
esemim0r.c:            "", prop->sub_district, "", prop->pw_dbw);
esemim0r.c:            "", prop->ant_height, "", prop->height_asl);
esemim0r.c:            "", prop->antenna, prop->az_max_rad);
esemim0r.c:    (prop->sfx_filter[0] != '\0') ?
esemim0r.c:               prop->feed_loss, prop->sfx_filter):
esemim0r.c:       fprintf(mfp, "* FEED LOSS (DB)  :        %3d%30s*", prop->feed_loss, "");
esemim0r.c:            "", prop->intmod_att_db, "", prop->station_type);
esemim0r.c:    fprintf(mfp, "* PROP. SYSTEM    : %c%s%s-%s%29s*\n", prop->sys_category,
esemim0r.c:           prop->sys_type, prop->sys_no, prop->sys_suffix, "");
esemim0r.c:            prop->east_grid, prop->north_grid);
esemim0r.c:            "", prop->sub_district, "", prop->pw_dbw);
esemim0r.c:            "", prop->ant_height, "", prop->height_asl);
esemim0r.c:            "", prop->antenna, prop->az_max_rad);
esemim0r.c:    (prop->sfx_filter[0] != '\0') ?
esemim0r.c:               prop->feed_loss, prop->sfx_filter):
esemim0r.c:       fprintf(mfp, "* FEED LOSS (DB)  :        %3d%30s*", prop->feed_loss, "");
esemim0r.c:            "", prop->intmod_att_db, "", prop->station_type);
esemim0r.c:    fprintf(mfp, "* PROP. SYSTEM    : %c%s%s-%s%29s*\n", prop->sys_category,
esemim0r.c:           prop->sys_type, prop->sys_no, prop->sys_suffix, "");
esemim0r.c:            prop->east_grid, prop->north_grid);
esemim0r.c:            "", prop->sub_district, "", prop->pw_dbw);
esemim0r.c:            "", prop->ant_height, "", prop->height_asl);
esemim0r.c:            "", prop->antenna, prop->az_max_rad);
esemim0r.c:    (prop->sfx_filter[0] != '\0') ?
esemim0r.c:               prop->feed_loss, prop->sfx_filter):
esemim0r.c:       fprintf(mfp, "* FEED LOSS (DB)  :        %3d%30s*", prop->feed_loss, "");
esemim0r.c:            "", prop->intmod_att_db, "", prop->station_type);
esemim0r.c:        printf("intmod_att_db = %-6.2f\n", prop->intmod_att_db);
esemim0r.c:    bw3db_channel = (int)(bw3db[prop->band]/MIN_CHANNEL_SEP + .005);
esemim0r.c:    if (prop->mode == TX_ONLY)
esemim0r.c:        if (fq_list[i]->stn_node == prop->stn_node)
esemim0r.c:            if (fq_list[j]->stn_node == prop->stn_node)
esemim0r.c:                if (fq_list[k]->stn_node == prop->stn_node)
esemim0r.c:    if (prop->mode == RX_ONLY)
esemim0r.c:        if (fq_list[i]->stn_node == prop->stn_node)
esemim0r.c:            if (fq_list[j]->stn_node == prop->stn_node)
esemim0r.c:                if (fq_list[k]->stn_node == prop->stn_node)
esemim0r.c:        if (fq_list[i]->stn_node == prop->stn_node)
esemim0r.c:            if (fq_list[j]->stn_node == prop->stn_node)
esemim0r.c:                if (fq_list[k]->stn_node == prop->stn_node)
esemim0r.c:            rx_grid[0]  = (float)prop->east_grid / 10;
esemim0r.c:	    rx_grid[1]  = (float)prop->north_grid / 10;
esemim0r.c:            rx_grid[0]  = (float)prop->east_grid / 10;
esemim0r.c:            rx_grid[1]  = (float)prop->north_grid / 10;
esemim0r.c:            rx_grid[0]  = (float)prop->east_grid / 10;
esemim0r.c:            rx_grid[1]  = (float)prop->north_grid / 10;
esemim0r.c:            tx_grid[0]  = (float)prop->east_grid / 10;
esemim0r.c:            tx_grid[1]  = (float)prop->north_grid / 10;
esemim0r.c:            tx_grid[0]  = (float)prop->east_grid / 10;
esemim0r.c:            tx_grid[1]  = (float)prop->north_grid / 10;
esemim0r.c:    fprintf(mfp, "* PROP. SYSTEM    : %c%s%s-%s%29s*\n", prop->sys_category,
esemim0r.c:           prop->sys_type, prop->sys_no, prop->sys_suffix, "");
esemim0r.c:            prop->east_grid, prop->north_grid);
esemim0r.c:            "", prop->sub_district, "", prop->pw_dbw);
esemim0r.c:            "", prop->ant_height, "", prop->height_asl);
esemim0r.c:            "", prop->antenna, prop->az_max_rad);
esemim0r.c:    (prop->sfx_filter[0] != '\0') ?
esemim0r.c:               prop->feed_loss, prop->sfx_filter):
esemim0r.c:       fprintf(mfp, "* FEED LOSS (DB)  :        %3d%30s*", prop->feed_loss, "");
esemim0r.c:            "", prop->intmod_att_db, "", prop->station_type);
esemim0r.c:    fprintf(mfp, "* PROP. SYSTEM    : %c%s%s-%s%29s*\n", prop->sys_category,
esemim0r.c:           prop->sys_type, prop->sys_no, prop->sys_suffix, "");
esemim0r.c:            prop->east_grid, prop->north_grid);
esemim0r.c:            "", prop->sub_district, "", prop->pw_dbw);
esemim0r.c:            "", prop->ant_height, "", prop->height_asl);
esemim0r.c:            "", prop->antenna, prop->az_max_rad);
esemim0r.c:    (prop->sfx_filter[0] != '\0') ?
esemim0r.c:               prop->feed_loss, prop->sfx_filter):
esemim0r.c:       fprintf(mfp, "* FEED LOSS (DB)  :        %3d%30s*", prop->feed_loss, "");
esemim0r.c:            "", prop->intmod_att_db, "", prop->station_type);
esemim0r.c:    fprintf(mfp, "* PROP. SYSTEM    : %c%s%s-%s%29s*\n", prop->sys_category,
esemim0r.c:           prop->sys_type, prop->sys_no, prop->sys_suffix, "");
esemim0r.c:            prop->east_grid, prop->north_grid);
esemim0r.c:            "", prop->sub_district, "", prop->pw_dbw);
esemim0r.c:            "", prop->ant_height, "", prop->height_asl);
esemim0r.c:            "", prop->antenna, prop->az_max_rad);
esemim0r.c:    (prop->sfx_filter[0] != '\0') ?
esemim0r.c:               prop->feed_loss, prop->sfx_filter):
esemim0r.c:       fprintf(mfp, "* FEED LOSS (DB)  :        %3d%30s*", prop->feed_loss, "");
esemim0r.c:            "", prop->intmod_att_db, "", prop->station_type);
esemin0x.c:min_usable_power = min_signal[subdist[prop->dist_index].noise_code][prop->band];
esemin0x.c:prop->dist_index, min_usable_power, subdist[prop->dist_index].noise_code,prop->band, i, delta_freq, power_intr);
esemin0x.c:prop_loss,intr.tx_freq,diffract_loss,vict.feed_loss,vict.ant_gain,off_chan_rej,o_filter_att_db, prop->att_db);
esemin0x.c:                   off_channel[i-1].rej_db[prop->band]:
esemin0x.c:                   off_channel[i].rej_db[prop->band];
esemin0x.c:        if (attenuation >= prop->desen_att_db)
esemin0x.c:        if (power_intr > curve_adj + prop->intmod_att_db)
esemsb0x.c:        set_band_mode2(&(prop->mode), 
esemsb0x.c:                       &(prop->band), 
esemsb0x.c:	prop->mode = 
esemsb0x.c:	    prop->mode = RX_ONLY;
esemsb0x.c:            prop->mode = TX_ONLY;
esemsb0x.c:        prop->band = 0;
esemsb0x.c:            prop->band = 1;
esemsb0x.c:            prop->band = 2;
esemsb0x.c:            prop->band = 3;
esemss0x.c:    cull_grid = subdist[prop->dist_index].cull_dist[prop->band][0];
esemss0x.c:    o_cull_east  = prop->east_grid + cull_grid;
esemss0x.c:    o_cull_west  = prop->east_grid - cull_grid;
esemss0x.c:    o_cull_north = prop->north_grid + cull_grid;
esemss0x.c:    o_cull_south = prop->north_grid - cull_grid;
