#!/bin/sh

# General Terminal Session Settings
TERM=${TERM-}; export TERM
stty ignbrk -brkint

EMC=/export/home/<USER>
export EMC
FORMS_DIR=$EMC/forms
export FORMS_DIR
REPORT_DIR=$EMC/reports
export REPORT_DIR
SRC=$EMC/emc
#SRC=$EMC/alex-debug
export SRC

ORACLE_HOME=/u01/app/oracle/product/10g; export ORACLE_HOME
LD_LIBRARY_PATH=$ORACLE_HOME/lib; export LD_LIBRARY_PATH
ORACLE_PATH=.:$ORACLE_HOME/BIN:$PATH; export ORACLE_PATH
ORA_NLS=$ORACLE_HOME/nls/data; export ORA_NLS
PATH=$ORACLE_HOME/bin:$PATH; export PATH
ORAKITPATH=.; export ORAKITPATH
ORACLE_SERVER=""; export ORACLE_SERVER
ORAENV_ASK=NO; export ORAENV_ASK
ORACLE_LPPROG=/usr/ucb/lpr; export ORACLE_LPPROG
ORACLE_PAGER=/usr/ucb/more; export ORACLE_PAGER
ORACLE_LPSTAT=/usr/ucb/lpq; export ORACLE_LPSTAT
#TWO_TASK=beq_esms; export TWO_TASK

#$src/ESEMCN0F EMC EMC
$SRC/esemcn0f EMC EMC 
#$SRC/esemba0x emc emc -P printer_id batch.out -i -l $1

