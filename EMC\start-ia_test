#!/bin/sh

export LD_LIBRARY_PATH=$ORACLE_HOME/lib
export ORACLE_HOME=/u01/app/oracle/product/12.2.0/dbhome_1

# General Terminal Session Settings
TERM=${TERM-}; export TERM
stty ignbrk -brkint

EMC=/export/home/<USER>
export EMC
FORMS_DIR=$EMC/forms
export FORMS_DIR
REPORT_DIR=$EMC/reports
export REPORT_DIR

SRC=$EMC/emc
#SRC=$EMC/emc_cyrus
#SRC=$EMC/alex-debug
export SRC

ORACLE_SID=ELSO; export ORACLE_SID

LD_LIBRARY_PATH=$ORACLE_HOME/lib; export LD_LIBRARY_PATH
ORACLE_PATH=.:$ORACLE_HOME/BIN:$PATH; export ORACLE_PATH
ORA_NLS=$ORACLE_HOME/nls/data; export ORA_NLS
PATH=$ORACLE_HOME/bin:$PATH; export PATH
ORAKITPATH=.; export ORAKITPATH
ORACLE_SERVER=""; export ORACLE_SERVER
ORAENV_ASK=NO; export ORAENV_ASK
ORACLE_LPPROG=/usr/ucb/lpr; export ORACLE_LPPROG
ORACLE_PAGER=/usr/ucb/more; export ORACLE_PAGER
ORACLE_LPSTAT=/usr/ucb/lpq; export ORACLE_LPSTAT

ORACLE_PATH=.:$ORACLE_HOME/BIN:$PATH; export ORACLE_PATH
ORA_NLS=$ORACLE_HOME/nls/data; export ORA_NLS
PATH=$ORACLE_HOME/bin:$PATH; export PATH
ORAKITPATH=.; export ORAKITPATH
ORACLE_SERVER=""; export ORACLE_SERVER



cp /dev/null /tmp/debug


awk '{ sub("\r$", ""); print }' $EMC/interactive/$1 > $EMC/interactive/$12

rm -f $EMC/interactive/$1

mv -v $EMC/interactive/$12 $EMC/interactive/$1


cp /export/home/<USER>/interactive/ELSO_WEB.ANALYSIS_tmp6 /export/home/<USER>/interactive/ELSO_WEB.ANALYSIS

$SRC/esemba0x emc emc -P printer_id batch.out -i -l $1





