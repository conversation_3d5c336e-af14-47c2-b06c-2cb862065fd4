
/* Result Sets Interface */
#ifndef SQL_CRSR
#  define SQL_CRSR
  struct sql_cursor
  {
    unsigned int curocn;
    void *ptr1;
    void *ptr2;
    unsigned int magic;
  };
  typedef struct sql_cursor sql_cursor;
  typedef struct sql_cursor SQL_CURSOR;
#endif /* SQL_CRSR */

/* Thread Safety */
typedef void * sql_context;
typedef void * SQL_CONTEXT;

/* Object support */
struct sqltvn
{
  unsigned char *tvnvsn; 
  unsigned short tvnvsnl; 
  unsigned char *tvnnm;
  unsigned short tvnnml; 
  unsigned char *tvnsnm;
  unsigned short tvnsnml;
};
typedef struct sqltvn sqltvn;

struct sqladts
{
  unsigned int adtvsn; 
  unsigned short adtmode; 
  unsigned short adtnum;  
  sqltvn adttvn[1];       
};
typedef struct sqladts sqladts;

static struct sqladts sqladt = {
  1,1,0,
};

/* Binding to PL/SQL Records */
struct sqltdss
{
  unsigned int tdsvsn; 
  unsigned short tdsnum; 
  unsigned char *tdsval[1]; 
};
typedef struct sqltdss sqltdss;
static struct sqltdss sqltds =
{
  1,
  0,
};

/* File name & Package Name */
struct sqlcxp
{
  unsigned short fillen;
           char  filnam[11];
};
static const struct sqlcxp sqlfpn =
{
    10,
    "utility.pc"
};


static unsigned int sqlctx = 84035;


static struct sqlexd {
   unsigned long  sqlvsn;
   unsigned int   arrsiz;
   unsigned int   iters;
   unsigned int   offset;
   unsigned short selerr;
   unsigned short sqlety;
   unsigned int   occurs;
      const short *cud;
   unsigned char  *sqlest;
      const char  *stmt;
   sqladts *sqladtp;
   sqltdss *sqltdsp;
   unsigned char  **sqphsv;
   unsigned long  *sqphsl;
            int   *sqphss;
            short **sqpind;
            int   *sqpins;
   unsigned long  *sqparm;
   unsigned long  **sqparc;
   unsigned short  *sqpadto;
   unsigned short  *sqptdso;
   unsigned int   sqlcmax;
   unsigned int   sqlcmin;
   unsigned int   sqlcincr;
   unsigned int   sqlctimeout;
   unsigned int   sqlcnowait;
            int   sqfoff;
   unsigned int   sqcmod;
   unsigned int   sqfmod;
   unsigned int   sqlpfmem;
   unsigned char  *sqhstv[1];
   unsigned long  sqhstl[1];
            int   sqhsts[1];
            short *sqindv[1];
            int   sqinds[1];
   unsigned long  sqharm[1];
   unsigned long  *sqharc[1];
   unsigned short  sqadto[1];
   unsigned short  sqtdso[1];
} sqlstm = {13,1};

/* SQLLIB Prototypes */
extern void sqlcxt (void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlcx2t(void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlbuft(void **, char *);
extern void sqlgs2t(void **, char *);
extern void sqlorat(void **, unsigned int *, void *);

/* Forms Interface */
static const int IAPSUCC = 0;
static const int IAPFAIL = 1403;
static const int IAPFTL  = 535;
extern void sqliem(unsigned char *, signed int *);

typedef struct { unsigned short len; unsigned char arr[1]; } VARCHAR;
typedef struct { unsigned short len; unsigned char arr[1]; } varchar;

/* cud (compilation unit data) array */
static const short sqlcud0[] =
{13,4130,1,0,0,
};


/**********************************************************************/
/*                                                                    */
/*    Module Name   :  utility.c                                      */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. H. Mak                                      */
/*                                                                    */
/*    Callers       :  EMC-related programs                           */  
/*                                                                    */
/*    Parameters    :                                                 */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  This programs contains a number of useful      */
/*                     common functions. The following is a list      */
/*                     of these functions:                            */
/*                         get_sys_date_time                          */
/*                         strip_blank                                */
/*                         pad_space                                  */
/*                         pad_n_space                                */
/*                         cal_dist                                   */
/*                         file_exist                                 */
/*                         str_toupper                                */
/*                         isnumber                                   */
/*                         is_space                                   */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

#include <stdio.h>
#include <time.h>
#include <string.h>
#include <math.h>

#ifndef   TRUE
#define  TRUE    1
#endif

#ifndef   FALSE
#define  FALSE   0
#endif

#define  TRAIL          0
#define  LEADING        1

#define  BLANK          32




void get_sys_date_time();		/*20170613 Cyrus [Add] */
void strip_blank();			/*20170613 Cyrus [Add] */
void pad_space();			/*20170613 Cyrus [Add] */
void pad_n_space();			/*20170613 Cyrus [Add] */
int toupper (int);			/*20170613 Cyrus [Add] */
int isspace (int);			/*20170613 Cyrus [Add] */
int isdigit (int);			/*20170613 Cyrus [Add] */



/**********************************************************************/
/*    get system date (yymmdd) and system time (hhmmss)               */
/**********************************************************************/

void get_sys_date_time(sys_date, sys_time, yymmdd, hhmmss)	/*20170613 Cyrus [Add][void ] */
char *sys_date, *sys_time, *yymmdd, *hhmmss;
{
    time_t        sys_sec;
    struct tm     *tt;

    sys_sec = time((long *)0);
    tt = localtime(&sys_sec);
    sprintf(sys_date, "%02d/%02d/%02d", tt->tm_mday, tt->tm_mon+1, tt->tm_year%100);
    sprintf(sys_time, "%02d:%02d:%02d", tt->tm_hour, tt->tm_min, tt->tm_sec);
    sprintf(yymmdd, "%02d%02d%02d", tt->tm_year, tt->tm_mon+1, tt->tm_mday);
    sprintf(hhmmss, "%02d%02d%02d", tt->tm_hour, tt->tm_min, tt->tm_sec);
}


/**********************************************************************/
/*    remove leading or trailing blanks of 'str' depending on 'mode'  */
/**********************************************************************/

void strip_blank(str, mode)					/*20170613 Cyrus [Add][void ] */
char  *str;
int    mode;
{
    char   tmp_str[512];
    int    i;
    int    slen;

    slen = strlen(str);

    if (mode == TRAIL)
    {
        for (i = slen - 1; (i >= 0) && (str[i] == BLANK); i--)
            ;
        str[i+1] = '\0';
    }

    if (mode == LEADING)
    {
        strcpy(tmp_str, str);
        for (i = 0; (i < slen) && (tmp_str[i] == BLANK); i++)
            ;
        if (i < slen)
            strcpy(str, tmp_str+i); 
    }
}


/*****************************************************************/
/*    pad space to 'str' using 'len' as terminator               */
/*****************************************************************/

void pad_space(str, len)					/*20170613 Cyrus [Add][void ] */
char  *str;
int    len;
{
    int   i; 


    for (i = strlen(str); i < len; i++)
        str[i] = BLANK;

    str[len] = '\0';
}


/*****************************************************************/
/*    pad n spaces to 'str'                                      */
/*****************************************************************/

void pad_n_space(str, n)					/*20170613 Cyrus [Add][void ] */
char  *str;
int    n;
{
    int   i, j; 


    for (i = strlen(str), j = 0; j < n; i++, j++)
        str[i] = BLANK;

    str[i] = '\0';
}


/*****************************************************************/
/*    search 1st/last occurrence of 'cc' in 's', depending on     */
/*    'direction'                                                */
/*****************************************************************/

int chr_in_str(str, c, direction)				/*20170613 Cyrus [Add][int ] */
char   *str;
char   c;
int    direction;
{
    char   s[80];
    int    len;
    register int    i, j;

    strcpy(s, str);
    len = strlen(s);
	
    i=0;							/*20170614 Cyrus [Add][i=0;] */
    j=0;							/*20170614 Cyrus [Add][j=0;] */

    if (direction == TRAIL)
    {
        for (i = len, j = 0; i >= 0; i--, j++)
            if (s[i] == c)
                break;
    }
    else
    {
        for(i = 0; i < len; i++)
            if (s[i] == c)
                break;
    }
     

    return((direction == LEADING)? i : j);
}
    
/*****************************************************************
 *            C a l    D i s t                                   *
 *****************************************************************/
/*
c++
c   This  calculates the distanse in km between two hong kong grid locations.
c   Note that the input is 4-digit grid.
c--
*/
float cal_dist (grida, gridb)
float grida[2],gridb[2];            
{ float f;
  struct {double x,y;} z;
/*  float  cabs(); */

  z.x = (double)(grida[0]-gridb[0]);
  z.y = (double)(grida[1]-gridb[1]);
/*  f = (float)(0.01 * cabs(z)); */
  f = (float)(0.01 * sqrt((z.x * z.x) + (z.y * z.y)));

  return (f < 0.001 ? 0.001 : f); 
/*
  return ((f = 0.01 * cabs(z)) < 0.001 ? 0.001 : f); 
*/
}


/*****************************************************************/
/*    determine whether the file 'fname' exists or not           */
/*****************************************************************/

int file_exist(fname)						/*20170613 Cyrus [Add][int ] */
char   *fname;
{
    FILE   *fp;

    if ((fp = fopen(fname, "r")) == (FILE *)NULL)
        return FALSE;

    fclose(fp);
/*    fclose(fname); */
    return TRUE;
}


/*****************************************************************/
/*    convert the whole string to uppercase                      */
/*****************************************************************/

void str_toupper(str)						/*20170613 Cyrus [Add][void ] */
char    *str;
{
    int    i;

    for (i = 0; str[i] != '\0'; i++)
        str[i] = toupper(str[i]);
}


/*****************************************************************/
/*    determine whether string 'cptr' represents a number        */
/*****************************************************************/

int isnumber(cptr)						/*20170613 Cyrus [Add][int ] */
char *cptr;
{
  int state=0;

  while (*cptr!='\0')
  { switch (state) 
    { case 0:  if (isspace(*cptr))
                 break;
               else
                 state = 1; 
      case 1:  if (*cptr=='.')
                 state = 2;
               else if (isspace(*cptr))
                 state = 3; 
               else if (!isdigit(*cptr))
                 state = 9; 
               break;
      case 2:  if (isdigit(*cptr))
                 state = 2;
               else if (isspace(*cptr))
                 state = 3; 
               else
                 state = 9; 
               break;
      case 3:  if (!isspace(*cptr))
                 state = 9;
               break;
    }
    cptr++;
  }
  return (state!=9);
}


/*****************************************************************/
/*    determine whether string 'cptr' is filled with spaces      */
/*****************************************************************/

int is_space(cptr)						/*20170614 Cyrus [Add][int ] */
char *cptr;
{
    int    len, i;

    len = strlen(cptr);

    for (i = 0; i < len; i++)
        if (cptr[i] != BLANK)
            break;

   return((i == len) ? TRUE : FALSE);
}
