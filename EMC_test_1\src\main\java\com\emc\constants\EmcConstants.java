package com.emc.constants;

/**
 * Constants used in the EMC analysis application.
 * Translated from the original C++ code.
 */
public class EmcConstants {
    // Error codes
    public static final int ERROR = 1;
    public static final int OK = 0;
    
    // Mathematical constants
    public static final double M_PI = Math.PI;
    
    // List markers
    public static final int END_OF_LIST = -1;
    
    // String handling
    public static final int TRAIL = 0;
    public static final int LEADING = 1;
    
    // Login status
    public static final int LOGIN_OK = 0;
    
    // Station types
    public static final char IS_PROPOSED = 'P';
    public static final char IS_EXIST = 'E';
    
    // Maximum array sizes
    public static final int MAX_EXIST = 500;
    
    // Dummy value
    public static final int DUMMY = -1;
    
    // System ID for proposed station
    public static final String PROPSTN_ID = "PROPOSED_STATION";
    
    // Minimum antenna height
    public static final double MIN_ANT_HEIGHT = 1.0;
    
    // Minimum channel separation
    public static final double MIN_CHANNEL_SEP = 0.0125;
}
