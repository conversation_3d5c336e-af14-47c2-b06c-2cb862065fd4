package com.emc.controller;

import com.emc.model.EmcAnalysisRequest;
import com.emc.model.EmcAnalysisResult;
import com.emc.service.EmcAnalysisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for EMC analysis.
 */
@RestController
@RequestMapping("/api/emc")
@RequiredArgsConstructor
@Slf4j
public class EmcAnalysisController {

    private final EmcAnalysisService emcAnalysisService;

    /**
     * Performs EMC analysis.
     *
     * @param request The EMC analysis request
     * @return The result of the EMC analysis
     */
    @PostMapping("/analyze")
    public ResponseEntity<EmcAnalysisResult> analyzeEmc(@RequestBody EmcAnalysisRequest request) {
        log.info("Received EMC analysis request: {}", request);

        // For interactive mode, use the default EMC UID if not provided
        if (request.isInteractive() && (request.getEmcUid() == null || request.getEmcUid().isEmpty())) {
            log.info("Using default EMC UID for interactive mode: ELSO_WEB.ANALYSIS");
        }

        EmcAnalysisResult result = emcAnalysisService.performAnalysis(request);
        log.info("EMC analysis completed with status: {}", result.getStatus());

        return ResponseEntity.ok(result);
    }

    /**
     * Gets the status of an EMC analysis.
     *
     * @param emcUid The EMC UID
     * @return The status of the EMC analysis
     */
    @GetMapping("/status/{emcUid}")
    public ResponseEntity<EmcAnalysisResult> getEmcStatus(@PathVariable String emcUid) {
        log.info("Received status request for EMC UID: {}", emcUid);

        // In a real application, this would retrieve the status from a database or cache
        EmcAnalysisResult result = new EmcAnalysisResult();
        result.setEmcUid(emcUid);
        result.setStatus("UNKNOWN");
        result.setMessage("Status retrieval not implemented");

        log.info("Returning status for EMC UID {}: {}", emcUid, result.getStatus());
        return ResponseEntity.ok(result);
    }
}
