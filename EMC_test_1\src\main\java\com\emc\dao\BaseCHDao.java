package com.emc.dao;

import com.emc.model.BaseCH;

import java.util.List;
import java.util.Optional;

/**
 * DAO interface for BaseCH operations.
 * This matches the original ProC BASE_CH table queries.
 */
public interface BaseCHDao extends BaseDao<BaseCH, String> {
    
    /**
     * Finds base channels by frequency range.
     * This matches the original ProC co-channel analysis queries.
     *
     * @param txFreqLow Lower TX frequency bound
     * @param txFreqHigh Upper TX frequency bound
     * @param rxFreqLow Lower RX frequency bound
     * @param rxFreqHigh Upper RX frequency bound
     * @return List of base channels within the frequency ranges
     */
    List<BaseCH> findByFrequencyRange(double txFreqLow, double txFreqHigh, double rxFreqLow, double rxFreqHigh);
    
    /**
     * Finds base channels by RX frequency range.
     *
     * @param rxFreqLow Lower RX frequency bound
     * @param rxFreqHigh Upper RX frequency bound
     * @return List of base channels within the RX frequency range
     */
    List<BaseCH> findByRxFrequencyRange(double rxFreqLow, double rxFreqHigh);
    
    /**
     * Finds base channels by system identification.
     *
     * @param sysCategory System category
     * @param sysType System type
     * @param sysNo System number
     * @param sysSuffix System suffix
     * @param baseNo Base number
     * @return List of base channels for the specified system
     */
    List<BaseCH> findBySystemId(char sysCategory, String sysType, String sysNo, String sysSuffix, int baseNo);
    
    /**
     * Finds base channels by exact frequency match.
     *
     * @param txFreq TX frequency
     * @param rxFreq RX frequency
     * @return List of base channels with matching frequencies
     */
    List<BaseCH> findByExactFrequency(double txFreq, double rxFreq);
}
