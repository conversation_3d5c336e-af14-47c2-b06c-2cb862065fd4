package com.emc.dao;

import java.util.List;
import java.util.Optional;

/**
 * Base DAO interface providing common CRUD operations.
 * 
 * @param <T> The entity type
 * @param <ID> The primary key type
 */
public interface BaseDao<T, ID> {
    
    /**
     * Save an entity.
     * 
     * @param entity The entity to save
     * @return The saved entity
     */
    T save(T entity);
    
    /**
     * Update an entity.
     * 
     * @param entity The entity to update
     * @return The updated entity
     */
    T update(T entity);
    
    /**
     * Find an entity by its ID.
     * 
     * @param id The entity ID
     * @return Optional containing the entity if found
     */
    Optional<T> findById(ID id);
    
    /**
     * Find all entities.
     * 
     * @return List of all entities
     */
    List<T> findAll();
    
    /**
     * Delete an entity by its ID.
     * 
     * @param id The entity ID
     * @return true if deleted, false if not found
     */
    boolean deleteById(ID id);
    
    /**
     * Check if an entity exists by its ID.
     * 
     * @param id The entity ID
     * @return true if exists, false otherwise
     */
    boolean existsById(ID id);
    
    /**
     * Count all entities.
     * 
     * @return The total count
     */
    long count();
}
