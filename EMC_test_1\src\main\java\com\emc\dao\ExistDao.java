package com.emc.dao;

import com.emc.model.Exist;
import java.util.List;
import java.util.Optional;

/**
 * DAO interface for Exist entity operations.
 */
public interface ExistDao extends BaseDao<Exist, String> {
    
    /**
     * Find existing station by EMC UID.
     * 
     * @param emcUid The EMC UID
     * @return Optional containing the existing station if found
     */
    Optional<Exist> findByEmcUid(String emcUid);
    
    /**
     * Find existing stations by system category.
     * 
     * @param sysCategory The system category
     * @return List of existing stations with the specified system category
     */
    List<Exist> findBySysCategory(char sysCategory);
    
    /**
     * Find existing stations by system type.
     * 
     * @param sysType The system type
     * @return List of existing stations with the specified system type
     */
    List<Exist> findBySysType(String sysType);
    
    /**
     * Find existing stations by sub-district.
     * 
     * @param subDistrict The sub-district code
     * @return List of existing stations in the specified sub-district
     */
    List<Exist> findBySubDistrict(String subDistrict);
    
    /**
     * Find existing stations by station type.
     * 
     * @param stationType The station type
     * @return List of existing stations with the specified station type
     */
    List<Exist> findByStationType(char stationType);
    
    /**
     * Find existing stations within a grid range.
     * 
     * @param minEastGrid Minimum east grid coordinate
     * @param maxEastGrid Maximum east grid coordinate
     * @param minNorthGrid Minimum north grid coordinate
     * @param maxNorthGrid Maximum north grid coordinate
     * @return List of existing stations within the specified range
     */
    List<Exist> findByGridRange(int minEastGrid, int maxEastGrid, 
                               int minNorthGrid, int maxNorthGrid);
    
    /**
     * Find existing stations by district type.
     * 
     * @param distType The district type
     * @return List of existing stations with the specified district type
     */
    List<Exist> findByDistType(char distType);
    
    /**
     * Find existing stations by noise code.
     * 
     * @param noiseCode The noise code
     * @return List of existing stations with the specified noise code
     */
    List<Exist> findByNoiseCode(int noiseCode);
}
