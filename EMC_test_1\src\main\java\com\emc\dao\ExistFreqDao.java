package com.emc.dao;

import com.emc.model.ExistFreq;
import java.util.List;

/**
 * DAO interface for ExistFreq entity operations.
 */
public interface ExistFreqDao extends BaseDao<ExistFreq, Long> {
    
    /**
     * Find frequency records by station node.
     * 
     * @param stnNode The station node identifier
     * @return List of frequency records for the specified station node
     */
    List<ExistFreq> findByStnNode(int stnNode);
    
    /**
     * Find frequency records by TX frequency.
     * 
     * @param txFreq The TX frequency
     * @return List of frequency records with the specified TX frequency
     */
    List<ExistFreq> findByTxFreq(double txFreq);
    
    /**
     * Find frequency records by RX frequency.
     * 
     * @param rxFreq The RX frequency
     * @return List of frequency records with the specified RX frequency
     */
    List<ExistFreq> findByRxFreq(double rxFreq);
    
    /**
     * Find frequency records by TX channel.
     * 
     * @param txChannel The TX channel
     * @return List of frequency records with the specified TX channel
     */
    List<ExistFreq> findByTxChannel(int txChannel);
    
    /**
     * Find frequency records by RX channel.
     * 
     * @param rxChannel The RX channel
     * @return List of frequency records with the specified RX channel
     */
    List<ExistFreq> findByRxChannel(int rxChannel);
    
    /**
     * Find frequency records within a frequency range.
     * 
     * @param minTxFreq Minimum TX frequency
     * @param maxTxFreq Maximum TX frequency
     * @return List of frequency records within the specified TX frequency range
     */
    List<ExistFreq> findByTxFreqRange(double minTxFreq, double maxTxFreq);
    
    /**
     * Find frequency records within an RX frequency range.
     * 
     * @param minRxFreq Minimum RX frequency
     * @param maxRxFreq Maximum RX frequency
     * @return List of frequency records within the specified RX frequency range
     */
    List<ExistFreq> findByRxFreqRange(double minRxFreq, double maxRxFreq);
}
