package com.emc.dao;

import com.emc.model.MinUsableSignal;

/**
 * DAO interface for MinUsableSignal entity operations.
 */
public interface MinUsableSignalDao extends BaseDao<MinUsableSignal, Long> {
    
    /**
     * Get the minimum usable signal configuration.
     * Since there's typically only one configuration record,
     * this method returns the current active configuration.
     * 
     * @return The minimum usable signal configuration
     */
    MinUsableSignal getConfiguration();
    
    /**
     * Update the minimum usable signal configuration.
     * 
     * @param minUsableSignal The new configuration
     * @return The updated configuration
     */
    MinUsableSignal updateConfiguration(MinUsableSignal minUsableSignal);
}
