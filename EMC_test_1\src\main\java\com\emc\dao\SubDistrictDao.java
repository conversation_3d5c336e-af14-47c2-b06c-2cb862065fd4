package com.emc.dao;

import com.emc.model.SubDistrict;
import java.util.List;
import java.util.Optional;

/**
 * DAO interface for SubDistrict entity operations.
 */
public interface SubDistrictDao extends BaseDao<SubDistrict, String> {
    
    /**
     * Find a sub-district by its code.
     * 
     * @param subDistrictCode The sub-district code
     * @return Optional containing the sub-district if found
     */
    Optional<SubDistrict> findBySubDistrictCode(String subDistrictCode);
    
    /**
     * Find sub-districts by district type.
     * 
     * @param distType The district type
     * @return List of sub-districts with the specified district type
     */
    List<SubDistrict> findByDistType(String distType);
    
    /**
     * Find sub-districts by noise code.
     * 
     * @param noiseCode The noise code
     * @return List of sub-districts with the specified noise code
     */
    List<SubDistrict> findByNoiseCode(int noiseCode);
    
    /**
     * Find all sub-districts ordered by sub-district code.
     * 
     * @return List of all sub-districts ordered by code
     */
    List<SubDistrict> findAllOrderBySubDistrictCode();
}
