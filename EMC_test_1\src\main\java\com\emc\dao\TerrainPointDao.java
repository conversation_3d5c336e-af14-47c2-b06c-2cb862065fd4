package com.emc.dao;

import com.emc.model.TerrainPoint;
import java.util.List;
import java.util.Optional;

/**
 * DAO interface for TerrainPoint entity operations.
 */
public interface TerrainPointDao extends BaseDao<TerrainPoint, Long> {
    
    /**
     * Find terrain point by grid coordinates.
     * 
     * @param eastGrid The east grid coordinate
     * @param northGrid The north grid coordinate
     * @return Optional containing the terrain point if found
     */
    Optional<TerrainPoint> findByGridCoordinates(int eastGrid, int northGrid);
    
    /**
     * Find terrain points within a grid range.
     * 
     * @param minEastGrid Minimum east grid coordinate
     * @param maxEastGrid Maximum east grid coordinate
     * @param minNorthGrid Minimum north grid coordinate
     * @param maxNorthGrid Maximum north grid coordinate
     * @return List of terrain points within the specified range
     */
    List<TerrainPoint> findByGridRange(int minEastGrid, int maxEastGrid, 
                                      int minNorthGrid, int maxNorthGrid);
    
    /**
     * Find terrain points by east grid coordinate.
     * 
     * @param eastGrid The east grid coordinate
     * @return List of terrain points with the specified east grid coordinate
     */
    List<TerrainPoint> findByEastGrid(int eastGrid);
    
    /**
     * Find terrain points by north grid coordinate.
     * 
     * @param northGrid The north grid coordinate
     * @return List of terrain points with the specified north grid coordinate
     */
    List<TerrainPoint> findByNorthGrid(int northGrid);
    
    /**
     * Find terrain points with height greater than or equal to the specified value.
     * 
     * @param minHeight The minimum height
     * @return List of terrain points
     */
    List<TerrainPoint> findByHeightGreaterThanEqual(int minHeight);
    
    /**
     * Find terrain points with height less than or equal to the specified value.
     * 
     * @param maxHeight The maximum height
     * @return List of terrain points
     */
    List<TerrainPoint> findByHeightLessThanEqual(int maxHeight);
}
