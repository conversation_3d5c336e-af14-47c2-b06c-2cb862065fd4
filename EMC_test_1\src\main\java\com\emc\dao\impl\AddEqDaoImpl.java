package com.emc.dao.impl;

import com.emc.dao.AddEqDao;
import com.emc.model.AddEq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Optional;

/**
 * JDBC implementation of AddEqDao.
 * This matches the original ProC ADD_EQ table queries.
 */
@Repository
@Slf4j
public class AddEqDaoImpl implements AddEqDao {
    
    private final JdbcTemplate jdbcTemplate;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    private final RowMapper<AddEq> rowMapper;
    
    // SQL queries matching original ProC queries
    private static final String SELECT_ALL = 
        "SELECT SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO, ADD_EQ_TYPE, NVL(LOSS, 0) AS LOSS " +
        "FROM ADD_EQ";
    
    private static final String SELECT_BY_SYSTEM_ID_AND_TYPE = SELECT_ALL +
        " WHERE SYS_CATEGORY = ? AND SYS_TYPE = ? AND SYS_NO = ? AND SYS_SUFFIX = ? AND BASE_NO = ? AND ADD_EQ_TYPE = ?";
    
    private static final String SELECT_FEEDER_LOSS = 
        "SELECT NVL(LOSS, 0) AS LOSS FROM ADD_EQ " +
        "WHERE SYS_CATEGORY = ? AND SYS_TYPE = ? AND SYS_NO = ? AND SYS_SUFFIX = ? AND BASE_NO = ? AND ADD_EQ_TYPE = 'L'";
    
    @Autowired
    public AddEqDaoImpl(JdbcTemplate jdbcTemplate, NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
        this.rowMapper = new AddEqRowMapper();
    }
    
    @Override
    public Optional<AddEq> findBySystemIdAndType(char sysCategory, String sysType, String sysNo, 
                                                String sysSuffix, int baseNo, char addEqType) {
        log.debug("Finding AddEq by system ID: {}{}{}-{} base {} type {}", 
                 sysCategory, sysType, sysNo, sysSuffix, baseNo, addEqType);
        try {
            AddEq addEq = jdbcTemplate.queryForObject(SELECT_BY_SYSTEM_ID_AND_TYPE, rowMapper,
                                                     sysCategory, sysType, sysNo, sysSuffix, baseNo, addEqType);
            return Optional.ofNullable(addEq);
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }
    
    @Override
    public float findFeederLoss(char sysCategory, String sysType, String sysNo, String sysSuffix, int baseNo) {
        log.debug("Finding feeder loss for system ID: {}{}{}-{} base {}", 
                 sysCategory, sysType, sysNo, sysSuffix, baseNo);
        try {
            Float loss = jdbcTemplate.queryForObject(SELECT_FEEDER_LOSS, Float.class,
                                                    sysCategory, sysType, sysNo, sysSuffix, baseNo);
            return loss != null ? loss : 0.0f;
        } catch (EmptyResultDataAccessException e) {
            return 0.0f; // Default value when not found
        }
    }
    
    // BaseDao implementation methods
    @Override
    public AddEq save(AddEq entity) {
        throw new UnsupportedOperationException("AddEq save not implemented");
    }
    
    @Override
    public AddEq update(AddEq entity) {
        throw new UnsupportedOperationException("AddEq update not implemented");
    }
    
    @Override
    public Optional<AddEq> findById(String id) {
        throw new UnsupportedOperationException("AddEq findById not implemented");
    }
    
    @Override
    public List<AddEq> findAll() {
        log.debug("Finding all AddEq records");
        return jdbcTemplate.query(SELECT_ALL + " ORDER BY SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO", rowMapper);
    }
    
    @Override
    public boolean deleteById(String id) {
        throw new UnsupportedOperationException("AddEq delete not implemented");
    }
    
    @Override
    public boolean existsById(String id) {
        throw new UnsupportedOperationException("AddEq existsById not implemented");
    }
    
    @Override
    public long count() {
        Integer count = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM ADD_EQ", Integer.class);
        return count != null ? count : 0;
    }
    
    /**
     * Row mapper for AddEq objects.
     */
    private static class AddEqRowMapper implements RowMapper<AddEq> {
        @Override
        public AddEq mapRow(ResultSet rs, int rowNum) throws SQLException {
            AddEq addEq = new AddEq();
            
            addEq.setSysCategory(rs.getString("SYS_CATEGORY").charAt(0));
            addEq.setSysType(rs.getString("SYS_TYPE"));
            addEq.setSysNo(rs.getString("SYS_NO"));
            addEq.setSysSuffix(rs.getString("SYS_SUFFIX"));
            addEq.setBaseNo(rs.getInt("BASE_NO"));
            addEq.setAddEqType(rs.getString("ADD_EQ_TYPE").charAt(0));
            addEq.setLoss(rs.getFloat("LOSS"));
            
            return addEq;
        }
    }
}
