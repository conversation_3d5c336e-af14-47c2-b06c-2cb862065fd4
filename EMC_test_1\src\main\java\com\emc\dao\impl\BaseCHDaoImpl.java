package com.emc.dao.impl;

import com.emc.dao.BaseCHDao;
import com.emc.dao.mapper.BaseCHRowMapper;
import com.emc.model.BaseCH;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * JDBC implementation of BaseCHDao.
 * This matches the original ProC BASE_CH table queries.
 */
@Repository
@Slf4j
public class BaseCHDaoImpl implements BaseCHDao {
    
    private final JdbcTemplate jdbcTemplate;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    private final BaseCHRowMapper rowMapper;
    
    // SQL queries matching original ProC queries
    private static final String SELECT_ALL = 
        "SELECT SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO, " +
        "TX_FREQ, RX_FREQ, NVL(TONE_FREQ, 0.0) AS TONE_FREQ " +
        "FROM BASE_CH";
    
    private static final String SELECT_BY_FREQUENCY_RANGE = SELECT_ALL +
        " WHERE (TX_FREQ BETWEEN ? AND ? OR RX_FREQ BETWEEN ? AND ?) " +
        "ORDER BY SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, TX_FREQ, RX_FREQ";
    
    private static final String SELECT_BY_RX_FREQUENCY_RANGE = SELECT_ALL +
        " WHERE RX_FREQ BETWEEN ? AND ? " +
        "ORDER BY SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO, CHANNEL_NO";
    
    private static final String SELECT_BY_SYSTEM_ID = SELECT_ALL +
        " WHERE SYS_CATEGORY = ? AND SYS_TYPE = ? AND SYS_NO = ? AND SYS_SUFFIX = ? AND BASE_NO = ? " +
        "ORDER BY TX_FREQ";
    
    private static final String SELECT_BY_EXACT_FREQUENCY = SELECT_ALL +
        " WHERE TX_FREQ = ? AND RX_FREQ = ?";
    
    @Autowired
    public BaseCHDaoImpl(JdbcTemplate jdbcTemplate, NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
        this.rowMapper = new BaseCHRowMapper();
    }
    
    @Override
    public List<BaseCH> findByFrequencyRange(double txFreqLow, double txFreqHigh, double rxFreqLow, double rxFreqHigh) {
        log.debug("Finding base channels by frequency range: TX {}-{}, RX {}-{}", 
                 txFreqLow, txFreqHigh, rxFreqLow, rxFreqHigh);
        return jdbcTemplate.query(SELECT_BY_FREQUENCY_RANGE, rowMapper,
                                 txFreqLow, txFreqHigh, rxFreqLow, rxFreqHigh);
    }
    
    @Override
    public List<BaseCH> findByRxFrequencyRange(double rxFreqLow, double rxFreqHigh) {
        log.debug("Finding base channels by RX frequency range: {}-{}", rxFreqLow, rxFreqHigh);
        return jdbcTemplate.query(SELECT_BY_RX_FREQUENCY_RANGE, rowMapper, rxFreqLow, rxFreqHigh);
    }
    
    @Override
    public List<BaseCH> findBySystemId(char sysCategory, String sysType, String sysNo, String sysSuffix, int baseNo) {
        log.debug("Finding base channels by system ID: {}{}{}-{} base {}", 
                 sysCategory, sysType, sysNo, sysSuffix, baseNo);
        return jdbcTemplate.query(SELECT_BY_SYSTEM_ID, rowMapper,
                                 sysCategory, sysType, sysNo, sysSuffix, baseNo);
    }
    
    @Override
    public List<BaseCH> findByExactFrequency(double txFreq, double rxFreq) {
        log.debug("Finding base channels by exact frequency: TX {}, RX {}", txFreq, rxFreq);
        return jdbcTemplate.query(SELECT_BY_EXACT_FREQUENCY, rowMapper, txFreq, rxFreq);
    }
    
    // BaseDao implementation methods
    @Override
    public BaseCH save(BaseCH entity) {
        throw new UnsupportedOperationException("BaseCH save not implemented");
    }
    
    @Override
    public BaseCH update(BaseCH entity) {
        throw new UnsupportedOperationException("BaseCH update not implemented");
    }
    
    @Override
    public Optional<BaseCH> findById(String id) {
        throw new UnsupportedOperationException("BaseCH findById not implemented");
    }
    
    @Override
    public List<BaseCH> findAll() {
        log.debug("Finding all base channels");
        return jdbcTemplate.query(SELECT_ALL + " ORDER BY SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO", rowMapper);
    }
    
    @Override
    public boolean deleteById(String id) {
        throw new UnsupportedOperationException("BaseCH delete not implemented");
    }
    
    @Override
    public boolean existsById(String id) {
        throw new UnsupportedOperationException("BaseCH existsById not implemented");
    }
    
    @Override
    public long count() {
        Integer count = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM BASE_CH", Integer.class);
        return count != null ? count : 0;
    }
}
