package com.emc.dao.impl;

import com.emc.dao.CullingFrequencyDao;
import com.emc.dao.mapper.CullingFrequencyRowMapper;
import com.emc.model.CullingFrequency;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * JDBC implementation of CullingFrequencyDao.
 */
@Repository
@Slf4j
public class CullingFrequencyDaoImpl implements CullingFrequencyDao {
    
    private final JdbcTemplate jdbcTemplate;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    private final CullingFrequencyRowMapper rowMapper;
    
    // SQL queries
    private static final String SELECT_ALL = 
        "SELECT DESENSITISATION, SIGNAL_2_INTERMOD, SIGNAL_3_INTERMOD FROM CULLING_FREQUENCY";
    
    private static final String SELECT_BY_ID = SELECT_ALL + " WHERE ID = ?";
    private static final String SELECT_CONFIG = SELECT_ALL + " LIMIT 1";
    
    private static final String INSERT = 
        "INSERT INTO CULLING_FREQUENCY (DESENSITISATION, SIGNAL_2_INTERMOD, SIGNAL_3_INTERMOD) VALUES (?, ?, ?)";
    
    private static final String UPDATE = 
        "UPDATE CULLING_FREQUENCY SET DESENSITISATION = ?, SIGNAL_2_INTERMOD = ?, SIGNAL_3_INTERMOD = ? WHERE ID = ?";
    
    private static final String UPDATE_CONFIG = 
        "UPDATE CULLING_FREQUENCY SET DESENSITISATION = ?, SIGNAL_2_INTERMOD = ?, SIGNAL_3_INTERMOD = ?";
    
    private static final String DELETE = "DELETE FROM CULLING_FREQUENCY WHERE ID = ?";
    private static final String EXISTS = "SELECT COUNT(*) FROM CULLING_FREQUENCY WHERE ID = ?";
    private static final String COUNT = "SELECT COUNT(*) FROM CULLING_FREQUENCY";
    
    @Autowired
    public CullingFrequencyDaoImpl(JdbcTemplate jdbcTemplate, NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
        this.rowMapper = new CullingFrequencyRowMapper();
    }
    
    @Override
    public CullingFrequency save(CullingFrequency entity) {
        log.debug("Saving CullingFrequency");
        
        jdbcTemplate.update(INSERT,
            entity.getDesenCull(),
            entity.getIntmod2Cull(),
            entity.getIntmod3Cull()
        );
        
        return entity;
    }
    
    @Override
    public CullingFrequency update(CullingFrequency entity) {
        log.debug("Updating CullingFrequency");
        
        // For configuration tables, we typically update all records
        int rowsAffected = jdbcTemplate.update(UPDATE_CONFIG,
            entity.getDesenCull(),
            entity.getIntmod2Cull(),
            entity.getIntmod3Cull()
        );
        
        if (rowsAffected == 0) {
            // If no records exist, insert a new one
            return save(entity);
        }
        
        return entity;
    }
    
    @Override
    public Optional<CullingFrequency> findById(Long id) {
        log.debug("Finding CullingFrequency by ID: {}", id);
        try {
            CullingFrequency frequency = jdbcTemplate.queryForObject(SELECT_BY_ID, rowMapper, id);
            return Optional.ofNullable(frequency);
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }
    
    @Override
    public List<CullingFrequency> findAll() {
        log.debug("Finding all CullingFrequencies");
        return jdbcTemplate.query(SELECT_ALL, rowMapper);
    }
    
    @Override
    public boolean deleteById(Long id) {
        log.debug("Deleting CullingFrequency: {}", id);
        int rowsAffected = jdbcTemplate.update(DELETE, id);
        return rowsAffected > 0;
    }
    
    @Override
    public boolean existsById(Long id) {
        Integer count = jdbcTemplate.queryForObject(EXISTS, Integer.class, id);
        return count != null && count > 0;
    }
    
    @Override
    public long count() {
        Integer count = jdbcTemplate.queryForObject(COUNT, Integer.class);
        return count != null ? count : 0;
    }
    
    @Override
    public CullingFrequency getConfiguration() {
        log.debug("Getting CullingFrequency configuration");
        try {
            return jdbcTemplate.queryForObject(SELECT_CONFIG, rowMapper);
        } catch (EmptyResultDataAccessException e) {
            log.warn("No CullingFrequency configuration found, returning default values");
            // Return default configuration
            CullingFrequency defaultConfig = new CullingFrequency();
            defaultConfig.setDesenCull(5.0f);
            defaultConfig.setIntmod2Cull(10.0f);
            defaultConfig.setIntmod3Cull(15.0f);
            return defaultConfig;
        }
    }
    
    @Override
    public CullingFrequency updateConfiguration(CullingFrequency cullingFrequency) {
        log.debug("Updating CullingFrequency configuration");
        return update(cullingFrequency);
    }
}
