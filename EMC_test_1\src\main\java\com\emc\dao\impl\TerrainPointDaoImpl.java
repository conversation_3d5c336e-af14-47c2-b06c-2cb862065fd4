package com.emc.dao.impl;

import com.emc.dao.TerrainPointDao;
import com.emc.dao.mapper.TerrainPointRowMapper;
import com.emc.model.TerrainPoint;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.Optional;

/**
 * JDBC implementation of TerrainPointDao.
 */
@Repository
@Slf4j
public class TerrainPointDaoImpl implements TerrainPointDao {
    
    private final JdbcTemplate jdbcTemplate;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    private final TerrainPointRowMapper rowMapper;
    
    // SQL queries
    private static final String SELECT_ALL = 
        "SELECT GRID_EAST, GRID_NORTH FROM FINE_TERRAIN_POINT";
    
    private static final String SELECT_BY_ID = SELECT_ALL + " WHERE ID = ?";
    private static final String SELECT_BY_COORDINATES = SELECT_ALL + " WHERE GRID_EAST = ? AND GRID_NORTH = ?";
    private static final String SELECT_BY_GRID_RANGE = 
        SELECT_ALL + " WHERE GRID_EAST BETWEEN ? AND ? AND GRID_NORTH BETWEEN ? AND ?";
    private static final String SELECT_BY_GRID_EAST = SELECT_ALL + " WHERE GRID_EAST = ?";
    private static final String SELECT_BY_GRID_NORTH = SELECT_ALL + " WHERE GRID_NORTH = ?";
    private static final String SELECT_BY_HEIGHT_GTE = SELECT_ALL + " WHERE HEIGHT >= ?";
    private static final String SELECT_BY_HEIGHT_LTE = SELECT_ALL + " WHERE HEIGHT <= ?";
    
    private static final String INSERT = 
        "INSERT INTO FINE_TERRAIN_POINT (GRID_EAST, GRID_NORTH, HEIGHT) VALUES (?, ?, ?)";
    
    private static final String UPDATE = 
        "UPDATE FINE_TERRAIN_POINT SET GRID_EAST = ?, GRID_NORTH = ?, HEIGHT = ? WHERE ID = ?";
    
    private static final String DELETE = "DELETE FROM FINE_TERRAIN_POINT WHERE ID = ?";
    private static final String EXISTS = "SELECT COUNT(*) FROM FINE_TERRAIN_POINT WHERE ID = ?";
    private static final String COUNT = "SELECT COUNT(*) FROM FINE_TERRAIN_POINT";
    
    @Autowired
    public TerrainPointDaoImpl(JdbcTemplate jdbcTemplate, NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
        this.rowMapper = new TerrainPointRowMapper();
    }
    
    @Override
    public TerrainPoint save(TerrainPoint entity) {
        log.debug("Saving TerrainPoint: east={}, north={}, height={}", 
                entity.getEastGrid(), entity.getNorthGrid(), entity.getHeight());
        
        KeyHolder keyHolder = new GeneratedKeyHolder();
        jdbcTemplate.update(connection -> {
            PreparedStatement ps = connection.prepareStatement(INSERT, Statement.RETURN_GENERATED_KEYS);
            ps.setInt(1, entity.getEastGrid());
            ps.setInt(2, entity.getNorthGrid());
            ps.setInt(3, entity.getHeight());
            return ps;
        }, keyHolder);
        
        return entity;
    }
    
    @Override
    public TerrainPoint update(TerrainPoint entity) {
        log.debug("Updating TerrainPoint: east={}, north={}, height={}", 
                entity.getEastGrid(), entity.getNorthGrid(), entity.getHeight());
        
        // Note: This assumes the entity has an ID field that we're not currently using
        // For now, we'll update based on coordinates as they might be unique
        String updateByCoordinates = 
            "UPDATE FINE_TERRAIN_POINT SET HEIGHT = ? WHERE GRID_EAST = ? AND GRID_NORTH = ?";
        
        int rowsAffected = jdbcTemplate.update(updateByCoordinates,
            entity.getHeight(),
            entity.getEastGrid(),
            entity.getNorthGrid()
        );
        
        if (rowsAffected == 0) {
            throw new RuntimeException("TerrainPoint not found for update at coordinates: " + 
                    entity.getEastGrid() + ", " + entity.getNorthGrid());
        }
        
        return entity;
    }
    
    @Override
    public Optional<TerrainPoint> findById(Long id) {
        log.debug("Finding TerrainPoint by ID: {}", id);
        try {
            TerrainPoint terrainPoint = jdbcTemplate.queryForObject(SELECT_BY_ID, rowMapper, id);
            return Optional.ofNullable(terrainPoint);
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }
    
    @Override
    public List<TerrainPoint> findAll() {
        log.debug("Finding all TerrainPoints");
        return jdbcTemplate.query(SELECT_ALL, rowMapper);
    }
    
    @Override
    public boolean deleteById(Long id) {
        log.debug("Deleting TerrainPoint: {}", id);
        int rowsAffected = jdbcTemplate.update(DELETE, id);
        return rowsAffected > 0;
    }
    
    @Override
    public boolean existsById(Long id) {
        Integer count = jdbcTemplate.queryForObject(EXISTS, Integer.class, id);
        return count != null && count > 0;
    }
    
    @Override
    public long count() {
        Integer count = jdbcTemplate.queryForObject(COUNT, Integer.class);
        return count != null ? count : 0;
    }
    
    @Override
    public Optional<TerrainPoint> findByGridCoordinates(int eastGrid, int northGrid) {
        log.debug("Finding TerrainPoint by coordinates: east={}, north={}", eastGrid, northGrid);
        try {
            TerrainPoint terrainPoint = jdbcTemplate.queryForObject(SELECT_BY_COORDINATES, rowMapper, eastGrid, northGrid);
            return Optional.ofNullable(terrainPoint);
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }
    
    @Override
    public List<TerrainPoint> findByGridRange(int minEastGrid, int maxEastGrid, int minNorthGrid, int maxNorthGrid) {
        log.debug("Finding TerrainPoints in range: east=[{}, {}], north=[{}, {}]", 
                minEastGrid, maxEastGrid, minNorthGrid, maxNorthGrid);
        return jdbcTemplate.query(SELECT_BY_GRID_RANGE, rowMapper, 
                minEastGrid, maxEastGrid, minNorthGrid, maxNorthGrid);
    }
    
    @Override
    public List<TerrainPoint> findByEastGrid(int eastGrid) {
        log.debug("Finding TerrainPoints by east grid: {}", eastGrid);
        return jdbcTemplate.query(SELECT_BY_GRID_EAST, rowMapper, eastGrid);
    }
    
    @Override
    public List<TerrainPoint> findByNorthGrid(int northGrid) {
        log.debug("Finding TerrainPoints by north grid: {}", northGrid);
        return jdbcTemplate.query(SELECT_BY_GRID_NORTH, rowMapper, northGrid);
    }
    
    @Override
    public List<TerrainPoint> findByHeightGreaterThanEqual(int minHeight) {
        log.debug("Finding TerrainPoints with height >= {}", minHeight);
        return jdbcTemplate.query(SELECT_BY_HEIGHT_GTE, rowMapper, minHeight);
    }
    
    @Override
    public List<TerrainPoint> findByHeightLessThanEqual(int maxHeight) {
        log.debug("Finding TerrainPoints with height <= {}", maxHeight);
        return jdbcTemplate.query(SELECT_BY_HEIGHT_LTE, rowMapper, maxHeight);
    }
}
