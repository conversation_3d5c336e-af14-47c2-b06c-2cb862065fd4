package com.emc.dao.mapper;

import com.emc.model.BaseCH;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Row mapper for BaseCH objects.
 */
public class BaseCHRowMapper implements RowMapper<BaseCH> {
    
    @Override
    public BaseCH mapRow(ResultSet rs, int rowNum) throws SQLException {
        BaseCH baseCH = new BaseCH();
        
        baseCH.setSysCategory(rs.getString("SYS_CATEGORY").charAt(0));
        baseCH.setSysType(rs.getString("SYS_TYPE"));
        baseCH.setSysNo(rs.getString("SYS_NO"));
        baseCH.setSysSuffix(rs.getString("SYS_SUFFIX"));
        baseCH.setBaseNo(rs.getInt("BASE_NO"));
        baseCH.setTxFreq(rs.getDouble("TX_FREQ"));
        baseCH.setRxFreq(rs.getDouble("RX_FREQ"));
        baseCH.setToneFreq(rs.getDouble("TONE_FREQ"));
        
        // Channel number might not always be present
        try {
            baseCH.setChannelNo(rs.getInt("CHANNEL_NO"));
        } catch (SQLException e) {
            baseCH.setChannelNo(0);
        }
        
        return baseCH;
    }
}
