package com.emc.dao.mapper;

import com.emc.model.ExistFreq;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Row mapper for ExistFreq entity.
 */
public class ExistFreqRowMapper implements RowMapper<ExistFreq> {
    
    @Override
    public ExistFreq mapRow(ResultSet rs, int rowNum) throws SQLException {
        ExistFreq existFreq = new ExistFreq();
        existFreq.setTxFreq(rs.getDouble("TX_FREQ"));
        existFreq.setRxFreq(rs.getDouble("RX_FREQ"));
        existFreq.setTxChannel(rs.getInt("TX_CHANNEL"));
        existFreq.setRxChannel(rs.getInt("RX_CHANNEL"));
        existFreq.setStnNode(rs.getInt("STN_NODE"));
        return existFreq;
    }
}
