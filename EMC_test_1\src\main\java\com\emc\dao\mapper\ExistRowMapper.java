package com.emc.dao.mapper;

import com.emc.model.Exist;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Row mapper for Exist entity.
 */
public class ExistRowMapper implements RowMapper<Exist> {
    
    @Override
    public Exist mapRow(ResultSet rs, int rowNum) throws SQLException {
        Exist exist = new Exist();
        exist.setEmcUid(rs.getString("EMC_UID"));
        
        String sysCategoryStr = rs.getString("SYS_CATEGORY");
        exist.setSysCategory(sysCategoryStr != null && !sysCategoryStr.isEmpty() ? sysCategoryStr.charAt(0) : ' ');
        
        exist.setSysType(rs.getString("SYS_TYPE"));
        exist.setSysNo(rs.getString("SYS_NO"));
        exist.setSysSuffix(rs.getString("SYS_SUFFIX"));
        exist.setEastGrid(rs.getInt("EAST_GRID"));
        exist.setNorthGrid(rs.getInt("NORTH_GRID"));
        exist.setSubDistrict(rs.getString("SUB_DISTRICT"));
        
        String stationTypeStr = rs.getString("STATION_TYPE");
        exist.setStationType(stationTypeStr != null && !stationTypeStr.isEmpty() ? stationTypeStr.charAt(0) : ' ');
        
        exist.setDesenAttDb(rs.getDouble("DESEN_ATT_DB"));
        exist.setIntmodAttDb(rs.getDouble("INTMOD_ATT_DB"));
        exist.setAntenna(rs.getString("ANTENNA"));
        exist.setAntHeight(rs.getInt("ANT_HEIGHT"));
        exist.setPwDbw(rs.getDouble("PW_DBW"));
        exist.setAzMaxRad(rs.getInt("AZ_MAX_RAD"));
        exist.setAzMaxRadR(rs.getDouble("AZ_MAX_RAD_R"));
        exist.setFeedLoss(rs.getInt("FEED_LOSS"));
        exist.setSfxFilter(rs.getString("SFX_FILTER"));
        exist.setHeightAsl(rs.getDouble("HEIGHT_ASL"));
        
        String distTypeStr = rs.getString("DIST_TYPE");
        exist.setDistType(distTypeStr != null && !distTypeStr.isEmpty() ? distTypeStr.charAt(0) : ' ');
        
        exist.setNoiseCode(rs.getInt("NOISE_CODE"));
        exist.setDistIndex(rs.getInt("DIST_INDEX"));
        
        return exist;
    }
}
