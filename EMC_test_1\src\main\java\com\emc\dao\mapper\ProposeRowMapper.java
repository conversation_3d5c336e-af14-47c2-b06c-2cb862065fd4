package com.emc.dao.mapper;

import com.emc.model.Propose;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Row mapper for Propose entity.
 */
public class ProposeRowMapper implements RowMapper<Propose> {
    
    @Override
    public Propose mapRow(ResultSet rs, int rowNum) throws SQLException {
        Propose propose = new Propose();
        propose.setEmcUid(rs.getString("EMC_UID"));
        
        String sysCategoryStr = rs.getString("SYS_CATEGORY");
        propose.setSysCategory(sysCategoryStr != null && !sysCategoryStr.isEmpty() ? sysCategoryStr.charAt(0) : ' ');
        
        propose.setSysType(rs.getString("SYS_TYPE"));
        propose.setSysNo(rs.getString("SYS_NO"));
        propose.setSysSuffix(rs.getString("SYS_SUFFIX"));
        propose.setEastGrid(rs.getInt("EAST_GRID"));
        propose.setNorthGrid(rs.getInt("NORTH_GRID"));
        propose.setSubDistrict(rs.getString("SUB_DISTRICT"));
        
        String stationTypeStr = rs.getString("STATION_TYPE");
        propose.setStationType(stationTypeStr != null && !stationTypeStr.isEmpty() ? stationTypeStr.charAt(0) : ' ');
        
        propose.setDesenAttDb(rs.getDouble("DESEN_ATT_DB"));
        propose.setIntmodAttDb(rs.getDouble("INTMOD_ATT_DB"));
        propose.setAntenna(rs.getString("ANTENNA"));
        propose.setAntHeight(rs.getInt("ANT_HEIGHT"));
        propose.setPwDbw(rs.getDouble("PW_DBW"));
        propose.setAzMaxRad(rs.getInt("AZ_MAX_RAD"));
        propose.setAzMaxRadR(rs.getDouble("AZ_MAX_RAD_R"));
        propose.setFeedLoss(rs.getInt("FEED_LOSS"));
        propose.setSfxFilter(rs.getString("SFX_FILTER"));
        propose.setHeightAsl(rs.getDouble("HEIGHT_ASL"));
        
        String distTypeStr = rs.getString("DIST_TYPE");
        propose.setDistType(distTypeStr != null && !distTypeStr.isEmpty() ? distTypeStr.charAt(0) : ' ');
        
        propose.setNoiseCode(rs.getInt("NOISE_CODE"));
        propose.setDistIndex(rs.getInt("DIST_INDEX"));
        propose.setTxChannel(rs.getInt("TX_CHANNEL"));
        propose.setRxChannel(rs.getInt("RX_CHANNEL"));
        
        String modeStr = rs.getString("MODE");
        propose.setMode(modeStr != null && !modeStr.isEmpty() ? modeStr.charAt(0) : ' ');
        
        return propose;
    }
}
