package com.emc.dao.mapper;

import com.emc.model.TerrainPoint;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Row mapper for TerrainPoint entity.
 */
public class TerrainPointRowMapper implements RowMapper<TerrainPoint> {
    
    @Override
    public TerrainPoint mapRow(ResultSet rs, int rowNum) throws SQLException {
        TerrainPoint terrainPoint = new TerrainPoint();
        terrainPoint.setEastGrid(rs.getInt("GRID_EAST"));
        terrainPoint.setNorthGrid(rs.getInt("GRID_NORTH"));
        return terrainPoint;
    }
}
