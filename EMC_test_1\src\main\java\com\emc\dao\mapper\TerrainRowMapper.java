package com.emc.dao.mapper;

import com.emc.model.Terrain;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Row mapper for Terrain objects.
 */
public class TerrainRowMapper implements RowMapper<Terrain> {
    
    @Override
    public Terrain mapRow(ResultSet rs, int rowNum) throws SQLException {
        Terrain terrain = new Terrain();
        
        terrain.setEast(rs.getInt("EAST"));
        terrain.setNorth(rs.getInt("NORTH"));
        terrain.setH1(rs.getFloat("H1"));
        terrain.setH2(rs.getFloat("H2"));
        terrain.setH3(rs.getFloat("H3"));
        terrain.setH4(rs.getFloat("H4"));
        
        return terrain;
    }
}
