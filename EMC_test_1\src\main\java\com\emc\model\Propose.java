package com.emc.model;

import lombok.Data;

/**
 * Java equivalent of the PROPOSE struct from the original C++ code.
 * Represents a proposed station for EMC analysis.
 */
@Data
public class Propose {
    private String emcUid;
    private char sysCategory;
    private String sysType;
    private String sysNo;
    private String sysSuffix;
    private int eastGrid;
    private int northGrid;
    private String subDistrict;
    private char stationType;
    private double desenAttDb;
    private double intmodAttDb;
    private String antenna;
    private int antHeight;
    private double pwDbw;
    private int azMaxRad;
    private double azMaxRadR;
    private int feedLoss;
    private String sfxFilter;
    private double heightAsl;
    private char distType;
    private int noiseCode;
    private int distIndex;
    private int txChannel;
    private int rxChannel;
    private char mode;
    private int band;
    private int stnNode;

    /**
     * Sets the station node and returns the previous value.
     *
     * @param node The new station node
     * @return The previous station node
     */
    public int setStnNode(int node) {
        int prev = this.stnNode;
        this.stnNode = node;
        return prev;
    }
}
