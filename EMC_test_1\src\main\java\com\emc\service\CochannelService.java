package com.emc.service;

import com.emc.model.Exist;
import com.emc.model.ExistFreq;
import com.emc.model.Propose;

import java.io.PrintWriter;
import java.util.List;

/**
 * Service for co-channel analysis.
 */
public interface CochannelService {

    /**
     * Performs co-channel analysis.
     * Equivalent to the cochaninf function in the original C++ code.
     *
     * @return OK if successful, ERROR otherwise
     */
    int cochaninf();

    // Setter methods for data injection
    void setProp(Propose prop);
    void setExist(List<Exist> exist);
    void setFqList(List<ExistFreq> fqList);
    void setPropTxFreq(double propTxFreq);
    void setPropRxFreq(double propRxFreq);
    void setAfp(PrintWriter afp);
    void setFqCnt(int fqCnt);

    // Getter methods for results
    int getCochanTot();
}
