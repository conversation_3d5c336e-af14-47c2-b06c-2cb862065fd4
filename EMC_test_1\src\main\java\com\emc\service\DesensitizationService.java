package com.emc.service;

import com.emc.model.Exist;
import com.emc.model.ExistFreq;
import com.emc.model.Propose;

import java.io.PrintWriter;
import java.util.List;

/**
 * Service for desensitization analysis.
 */
public interface DesensitizationService {

    /**
     * Performs desensitization analysis.
     * Equivalent to the desensit_analysis function in the original C++ code.
     *
     * @return OK if successful, ERROR otherwise
     */
    int desensitAnalysis();

    // Setter methods for data injection
    void setProp(Propose prop);
    void setExist(List<Exist> exist);
    void setFqList(List<ExistFreq> fqList);
    void setPropTxFreq(double propTxFreq);
    void setPropRxFreq(double propRxFreq);
    void setAfp(PrintWriter afp);

    // Getter methods for results
    int getCullStnCnt();
    int getDesenTot();
}
