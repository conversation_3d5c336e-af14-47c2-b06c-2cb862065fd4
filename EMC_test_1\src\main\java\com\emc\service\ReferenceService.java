package com.emc.service;

import com.emc.model.CullingFrequency;
import com.emc.model.MinUsableSignal;
import com.emc.model.OffChannelRejection;
import com.emc.model.SubDistrict;
import com.emc.model.TerrainPoint;

import java.util.List;
import java.util.Map;

/**
 * Service for loading and accessing reference data.
 * Equivalent to the load_reference function in the original C++ code.
 */
public interface ReferenceService {

    /**
     * Loads reference data from the database.
     *
     * @return OK if successful, ERROR otherwise
     */
    int loadReference();

    /**
     * Gets the list of sub-districts.
     *
     * @return The list of sub-districts
     */
    List<SubDistrict> getSubDistricts();

    /**
     * Gets the list of off-channel rejections.
     *
     * @return The list of off-channel rejections
     */
    List<OffChannelRejection> getOffChannelRejections();

    /**
     * Gets the list of minimum usable signals.
     *
     * @return The list of minimum usable signals
     */
    List<MinUsableSignal> getMinUsableSignals();

    /**
     * Gets the list of fine terrain points (east).
     *
     * @return The list of fine terrain points
     */
    List<TerrainPoint> getFineTerrainEast();

    /**
     * Gets the list of fine terrain points (north).
     *
     * @return The list of fine terrain points
     */
    List<TerrainPoint> getFineTerrainNorth();

    /**
     * Gets the culling frequency information.
     *
     * @return The culling frequency information
     */
    CullingFrequency getCullingFrequency();

    /**
     * Gets the map of sub-districts by code.
     *
     * @return The map of sub-districts by code
     */
    Map<String, SubDistrict> getSubDistrictMap();

    /**
     * Gets a sub-district by code.
     *
     * @param code The sub-district code
     * @return The sub-district, or null if not found
     */
    SubDistrict getSubDistrictByCode(String code);

    /**
     * Gets district information for a sub-district.
     *
     * @param subDistrict The sub-district code
     * @param distType Output parameter for the district type
     * @param noiseCode Output parameter for the noise code
     * @param distIndex Output parameter for the district index
     * @return OK if successful, ERROR otherwise
     */
    int getDistrict(String subDistrict, char[] distType, int noiseCode, int distIndex);
}
