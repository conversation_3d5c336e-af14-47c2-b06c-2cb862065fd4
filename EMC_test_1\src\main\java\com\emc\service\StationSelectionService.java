package com.emc.service;

import com.emc.model.Exist;
import com.emc.model.ExistFreq;
import com.emc.model.Propose;
import com.emc.model.Station;

import java.util.List;

/**
 * Service for station selection operations.
 * This matches the original ProC select_station function.
 */
public interface StationSelectionService {
    
    /**
     * Selects stations within culling distance for EMC analysis.
     * This is equivalent to the select_station function in the original C++ code.
     *
     * @param prop The proposed station
     * @param cullGrid The culling grid distance
     * @return OK if successful, ERROR otherwise
     */
    int selectStations(Propose prop, int cullGrid);
    
    /**
     * Gets the list of existing stations selected for analysis.
     *
     * @return List of existing stations
     */
    List<Exist> getExistingStations();
    
    /**
     * Gets the list of frequency information for existing stations.
     *
     * @return List of frequency information
     */
    List<ExistFreq> getFrequencyList();
    
    /**
     * Gets the count of selected stations.
     *
     * @return Number of selected stations
     */
    int getStationCount();
    
    /**
     * Gets the count of frequencies.
     *
     * @return Number of frequencies
     */
    int getFrequencyCount();
}
