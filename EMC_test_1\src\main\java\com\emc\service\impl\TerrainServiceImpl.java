package com.emc.service.impl;

import com.emc.constants.EmcConstants;
import com.emc.dao.TerrainDao;
import com.emc.model.TerrainPoint;
import com.emc.model.Terrain;
import com.emc.service.ReferenceService;
import com.emc.service.TerrainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Implementation of the TerrainService interface.
 * This is a translation of the get_local_height function from the original C++ code.
 */
@Service
@Slf4j
public class TerrainServiceImpl implements TerrainService {

    // Constants for terrain calculations
    private static final float BASE_EAST = 800000.0f;
    private static final float BASE_NORTH = 800000.0f;
    private static final float STEP = 1000.0f;

    // Configurable terrain bounds - can be overridden by properties
    @Value("${emc.terrain.min-east:0.0}")
    private float minEast;

    @Value("${emc.terrain.max-east:999999.0}")
    private float maxEast;

    @Value("${emc.terrain.min-north:0.0}")
    private float minNorth;

    @Value("${emc.terrain.max-north:999999.0}")
    private float maxNorth;

    @Autowired
    private ReferenceService referenceService;

    @Autowired(required = false)
    private TerrainDao terrainDao;

    @Autowired(required = false)
    private JdbcTemplate jdbcTemplate;

    @Value("${emc.terrain.use-fine-terrain:true}")
    private boolean useFineTerrainData;

    @Override
    public float getLocalHeight(float gridEast, float gridNorth) {
        log.debug("Getting local height for grid coordinates: {}, {}", gridEast, gridNorth);

        try {
            // Check if we should use fine terrain data
            if (useFineTerrainData) {
                // Try to find the terrain point in the fine terrain data
                float height = findHeightInFineTerrainData(gridEast, gridNorth);
                if (height >= 0) {
                    return height;
                }
            }

            // If we get here, either fine terrain data is not available or the point was not found
            // Use interpolation to calculate the height
            return calculateInterpolatedHeight(gridEast, gridNorth);
        } catch (Exception e) {
            log.error("Error getting local height", e);
            return 100.0f; // Default value in case of error
        }
    }

    /**
     * Finds the height of a point in the fine terrain data.
     *
     * @param gridEast The east grid coordinate
     * @param gridNorth The north grid coordinate
     * @return The height if found, -1 otherwise
     */
    private float findHeightInFineTerrainData(float gridEast, float gridNorth) {
        // In the original C++ code, this would query the FINE_TERRAIN table
        // For now, we'll use the fine terrain data loaded in the reference service

        // Convert to integer grid coordinates (multiplied by 10 as in the original code)
        int eastGrid = (int)(gridEast * 10);
        int northGrid = (int)(gridNorth * 10);

        // Check if the point exists in the fine terrain data
        List<TerrainPoint> fineTerrainPoints = referenceService.getFineTerrainEast();
        for (TerrainPoint point : fineTerrainPoints) {
            if (point.getEastGrid() == eastGrid && point.getNorthGrid() == northGrid) {
                log.debug("Found terrain point in fine terrain data: {}", point.getHeight());
                return point.getHeight();
            }
        }

        // Point not found
        return -1;
    }

    /**
     * Calculates the interpolated height of a point.
     * This matches the original ProC terrain height calculation logic.
     *
     * @param gridEast The east grid coordinate
     * @param gridNorth The north grid coordinate
     * @return The interpolated height
     */
    private float calculateInterpolatedHeight(float gridEast, float gridNorth) {
        // Check if the point is within the valid range
        if (gridEast < minEast || gridEast > maxEast ||
            gridNorth < minNorth || gridNorth > maxNorth) {
            log.debug("Grid coordinates out of range: {}, {} (valid range: E:{}-{}, N:{}-{})",
                     gridEast, gridNorth, minEast, maxEast, minNorth, maxNorth);
            return 100.0f; // Default value for out-of-range coordinates
        }

        // Calculate reference points (matching original ProC logic)
        int eastRef = (int)(BASE_EAST + STEP * (1 + (int)((gridEast - BASE_EAST) / STEP)));
        int northRef = (int)(BASE_NORTH + STEP * (1 + (int)((gridNorth - BASE_NORTH) / STEP)));

        // Get terrain heights from database if available
        float h1, h2, h3, h4;

        if (terrainDao != null) {
            // Query the TERRAIN table for the heights at the reference points
            h1 = getTerrainHeight(eastRef, northRef);
            h2 = getTerrainHeight(eastRef + (int)STEP, northRef);
            h3 = getTerrainHeight(eastRef, northRef + (int)STEP);
            h4 = getTerrainHeight(eastRef + (int)STEP, northRef + (int)STEP);
        } else {
            // Fallback to default values if DAO is not available
            h1 = 100.0f;
            h2 = 110.0f;
            h3 = 120.0f;
            h4 = 130.0f;
        }

        // Calculate interpolation factors
        float x = (gridEast - eastRef) / STEP;
        float y = (gridNorth - northRef) / STEP;

        // Perform bilinear interpolation
        float hp1 = h1 * (1 - x) + h2 * x;
        float hp2 = h3 * (1 - x) + h4 * x;
        float height = hp1 * (1 - y) + hp2 * y;

        log.debug("Calculated interpolated height: {} for coordinates ({}, {})", height, gridEast, gridNorth);
        return height;
    }

    /**
     * Gets terrain height from the database for specific coordinates.
     *
     * @param east East coordinate
     * @param north North coordinate
     * @return The terrain height, or default value if not found
     */
    private float getTerrainHeight(int east, int north) {
        try {
            if (terrainDao != null) {
                return terrainDao.findByGridCoordinates(east, north)
                    .map(terrain -> {
                        // Use H1 as the primary height value (matching original ProC logic)
                        return terrain.getH1();
                    })
                    .orElse(100.0f); // Default height if not found
            }
        } catch (Exception e) {
            log.warn("Error querying terrain height for coordinates ({}, {}): {}", east, north, e.getMessage());
        }

        return 100.0f; // Default height
    }
}
