package com.emc.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Utility class for date and time operations.
 */
public class DateTimeUtils {
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yy");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    private static final DateTimeFormatter YYMMDD_FORMATTER = DateTimeFormatter.ofPattern("yyMMdd");
    private static final DateTimeFormatter HHMMSS_FORMATTER = DateTimeFormatter.ofPattern("HHmmss");
    
    /**
     * Gets the current system date and time in various formats.
     * Equivalent to the get_sys_date_time function in the original C++ code.
     * 
     * @param sysDate Output parameter for the system date (dd/MM/yy)
     * @param sysTime Output parameter for the system time (HH:mm:ss)
     * @param yymmdd Output parameter for the system date (yyMMdd)
     * @param hhmmss Output parameter for the system time (HHmmss)
     * @return OK if successful
     */
    public static int getSysDateTime(StringBuilder sysDate, StringBuilder sysTime, 
                                    StringBuilder yymmdd, StringBuilder hhmmss) {
        LocalDateTime now = LocalDateTime.now();
        
        sysDate.setLength(0);
        sysDate.append(now.format(DATE_FORMATTER));
        
        sysTime.setLength(0);
        sysTime.append(now.format(TIME_FORMATTER));
        
        yymmdd.setLength(0);
        yymmdd.append(now.format(YYMMDD_FORMATTER));
        
        hhmmss.setLength(0);
        hhmmss.append(now.format(HHMMSS_FORMATTER));
        
        return 0; // OK
    }
}
