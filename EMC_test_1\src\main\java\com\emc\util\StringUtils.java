package com.emc.util;

import com.emc.constants.EmcConstants;

/**
 * Utility class for string operations.
 */
public class StringUtils {
    
    /**
     * Strips blanks from a string.
     * Equivalent to the strip_blank function in the original C++ code.
     * 
     * @param str The string to strip blanks from
     * @param mode TRAIL or LEADING
     * @return The number of characters stripped
     */
    public static int stripBlank(StringBuilder str, int mode) {
        int i, j;
        int len = str.length();
        
        if (len == 0) {
            return 0;
        }
        
        if (mode == EmcConstants.TRAIL) {
            // Strip trailing blanks
            for (i = len - 1; i >= 0; i--) {
                if (str.charAt(i) != ' ') {
                    break;
                }
            }
            
            if (i < len - 1) {
                str.setLength(i + 1);
                return len - (i + 1);
            }
        } else {
            // Strip leading blanks
            for (i = 0; i < len; i++) {
                if (str.charAt(i) != ' ') {
                    break;
                }
            }
            
            if (i > 0) {
                str.delete(0, i);
                return i;
            }
        }
        
        return 0;
    }
    
    /**
     * Checks if a string represents a number.
     * 
     * @param str The string to check
     * @return true if the string is a number, false otherwise
     */
    public static boolean isNumber(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}
