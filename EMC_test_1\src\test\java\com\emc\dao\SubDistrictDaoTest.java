package com.emc.dao;

import com.emc.dao.impl.SubDistrictDaoImpl;
import com.emc.model.SubDistrict;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for SubDistrictDao implementation.
 */
@ExtendWith(MockitoExtension.class)
public class SubDistrictDaoTest {
    
    @Mock
    private JdbcTemplate jdbcTemplate;
    
    @Mock
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    
    private SubDistrictDao subDistrictDao;
    
    @BeforeEach
    void setUp() {
        subDistrictDao = new SubDistrictDaoImpl(jdbcTemplate, namedParameterJdbcTemplate);
    }
    
    @Test
    void testFindAllOrderBySubDistrictCode() {
        // Setup
        SubDistrict sd1 = createTestSubDistrict("ABC", 1, "U");
        SubDistrict sd2 = createTestSubDistrict("DEF", 2, "R");
        List<SubDistrict> expectedSubDistricts = Arrays.asList(sd1, sd2);
        
        when(jdbcTemplate.query(anyString(), any(RowMapper.class)))
                .thenReturn(expectedSubDistricts);
        
        // Execute
        List<SubDistrict> result = subDistrictDao.findAllOrderBySubDistrictCode();
        
        // Verify
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("ABC", result.get(0).getSubDistrictCode());
        assertEquals("DEF", result.get(1).getSubDistrictCode());
        
        verify(jdbcTemplate, times(1)).query(anyString(), any(RowMapper.class));
    }
    
    @Test
    void testFindBySubDistrictCode() {
        // Setup
        SubDistrict expectedSubDistrict = createTestSubDistrict("ABC", 1, "U");
        
        when(jdbcTemplate.queryForObject(anyString(), any(RowMapper.class), eq("ABC")))
                .thenReturn(expectedSubDistrict);
        
        // Execute
        Optional<SubDistrict> result = subDistrictDao.findBySubDistrictCode("ABC");
        
        // Verify
        assertTrue(result.isPresent());
        assertEquals("ABC", result.get().getSubDistrictCode());
        assertEquals(1, result.get().getNoiseCode());
        assertEquals("U", result.get().getDistType());
        
        verify(jdbcTemplate, times(1)).queryForObject(anyString(), any(RowMapper.class), eq("ABC"));
    }
    
    @Test
    void testSave() {
        // Setup
        SubDistrict subDistrict = createTestSubDistrict("XYZ", 3, "M");
        
        when(jdbcTemplate.update(anyString(), any(Object[].class)))
                .thenReturn(1);
        
        // Execute
        SubDistrict result = subDistrictDao.save(subDistrict);
        
        // Verify
        assertNotNull(result);
        assertEquals("XYZ", result.getSubDistrictCode());
        
        verify(jdbcTemplate, times(1)).update(anyString(), any(Object[].class));
    }
    
    @Test
    void testExistsById() {
        // Setup
        when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), eq("ABC")))
                .thenReturn(1);
        
        // Execute
        boolean result = subDistrictDao.existsById("ABC");
        
        // Verify
        assertTrue(result);
        
        verify(jdbcTemplate, times(1)).queryForObject(anyString(), eq(Integer.class), eq("ABC"));
    }
    
    @Test
    void testCount() {
        // Setup
        when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class)))
                .thenReturn(5);
        
        // Execute
        long result = subDistrictDao.count();
        
        // Verify
        assertEquals(5, result);
        
        verify(jdbcTemplate, times(1)).queryForObject(anyString(), eq(Integer.class));
    }
    
    private SubDistrict createTestSubDistrict(String code, int noiseCode, String distType) {
        SubDistrict subDistrict = new SubDistrict();
        subDistrict.setSubDistrictCode(code);
        subDistrict.setNoiseCode(noiseCode);
        subDistrict.setDistType(distType);
        subDistrict.setLowVhfReg(10);
        subDistrict.setLowVhfHd(20);
        subDistrict.setVhfReg(30);
        subDistrict.setVhfHd(40);
        subDistrict.setUhfReg(50);
        subDistrict.setUhfHd(60);
        subDistrict.setUhf800Reg(70);
        subDistrict.setUhf800Hd(80);
        return subDistrict;
    }
}
