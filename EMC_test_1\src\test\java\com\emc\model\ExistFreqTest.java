package com.emc.model;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

public class ExistFreqTest {

    @Test
    void testExistFreqProperties() {
        // Setup
        ExistFreq existFreq = new ExistFreq();
        
        // Set properties
        existFreq.setTxFreq(123.45);
        existFreq.setRxFreq(678.90);
        existFreq.setTxChannel(1234);
        existFreq.setRxChannel(5678);
        existFreq.setStnNode(3);
        
        // Verify
        assertEquals(123.45, existFreq.getTxFreq());
        assertEquals(678.90, existFreq.getRxFreq());
        assertEquals(1234, existFreq.getTxChannel());
        assertEquals(5678, existFreq.getRxChannel());
        assertEquals(3, existFreq.getStnNode());
    }
}
