package com.emc.service;

import com.emc.model.Exist;
import com.emc.model.ExistFreq;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for the parseExistLine method in EmcAnalysisService.
 */
@ExtendWith(MockitoExtension.class)
public class EmcAnalysisServiceParseTest {

    @Mock
    private ReferenceService referenceService;

    @Mock
    private TerrainService terrainService;

    @Mock
    private DesensitizationService desensitizationService;

    @Mock
    private IntermodulationService intermodulationService;

    @Mock
    private CochannelService cochannelService;

    @InjectMocks
    private EmcAnalysisService emcAnalysisService;

    private List<Exist> exist;
    private List<ExistFreq> fqList;
    private List<ExistFreq> sFqList;
    private int fqCnt;

    @BeforeEach
    void setUp() {
        // Initialize the lists
        exist = new ArrayList<>();
        exist.add(new Exist());
        
        fqList = new ArrayList<>();
        fqList.add(new ExistFreq());
        
        sFqList = new ArrayList<>();
        sFqList.add(new ExistFreq());
        
        fqCnt = 0;
        
        // Set the fields in the service using reflection
        ReflectionTestUtils.setField(emcAnalysisService, "exist", exist);
        ReflectionTestUtils.setField(emcAnalysisService, "fqList", fqList);
        ReflectionTestUtils.setField(emcAnalysisService, "sFqList", sFqList);
        ReflectionTestUtils.setField(emcAnalysisService, "fqCnt", fqCnt);
    }

    @Test
    void testParseExistLine() throws Exception {
        // Setup
        String instr = 
            "TEST_EMC_UID       " + // EMC UID (19 chars)
            "12345" +              // East grid (5 chars)
            "67890" +              // North grid (5 chars)
            "ABC" +                // Sub-district (3 chars)
            "X" +                  // Station type (1 char)
            "YZ" +                 // Antenna (2 chars)
            "123" +                // Azimuth (3 chars)
            "045" +                // Antenna height (3 chars)
            "+1234" +              // Power in dBW (5 chars)
            "123" +                // Feed loss (3 chars)
            "+1234" +              // Desensitization attenuation (5 chars)
            "+1234" +              // Intermodulation attenuation (5 chars)
            "FILTER123" +          // SFX filter (10 chars)
            "12345678901" +        // TX frequency (11 chars)
            "12345678901";         // RX frequency (11 chars)
        
        // Execute
        // Call the private method using reflection
        java.lang.reflect.Method method = EmcAnalysisService.class.getDeclaredMethod("parseExistLine", String.class, int.class);
        method.setAccessible(true);
        method.invoke(emcAnalysisService, instr, 0);
        
        // Get the updated values
        exist = (List<Exist>) ReflectionTestUtils.getField(emcAnalysisService, "exist");
        fqList = (List<ExistFreq>) ReflectionTestUtils.getField(emcAnalysisService, "fqList");
        sFqList = (List<ExistFreq>) ReflectionTestUtils.getField(emcAnalysisService, "sFqList");
        fqCnt = (int) ReflectionTestUtils.getField(emcAnalysisService, "fqCnt");
        
        // Verify
        Exist existObj = exist.get(0);
        assertEquals("TEST_EMC_UID", existObj.getEmcUid());
        assertEquals(12345, existObj.getEastGrid());
        assertEquals(67890, existObj.getNorthGrid());
        assertEquals("ABC", existObj.getSubDistrict());
        assertEquals('X', existObj.getStationType());
        assertEquals("YZ", existObj.getAntenna());
        assertEquals(123, existObj.getAzMaxRad());
        assertEquals(45, existObj.getAntHeight());
        assertEquals(1234.0, existObj.getPwDbw());
        assertEquals(123, existObj.getFeedLoss());
        assertEquals(1234.0, existObj.getDesenAttDb());
        assertEquals(1234.0, existObj.getIntmodAttDb());
        assertEquals("FILTER123", existObj.getSfxFilter());
        
        // Verify frequency information
        assertEquals(1, fqCnt);
        ExistFreq freqObj = fqList.get(0);
        assertEquals(12345678901.0, freqObj.getTxFreq());
        assertEquals(12345678901.0, freqObj.getRxFreq());
        assertEquals(0, freqObj.getStnNode());
        
        // Verify sorted frequency list
        ExistFreq sFreqObj = sFqList.get(0);
        assertEquals(12345678901.0, sFreqObj.getTxFreq());
        assertEquals(12345678901.0, sFreqObj.getRxFreq());
        assertEquals(0, sFreqObj.getStnNode());
    }
}
