package com.emc.service.impl;

import com.emc.constants.EmcConstants;
import com.emc.dao.*;
import com.emc.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ReferenceServiceImplTest {

    @Mock
    private SubDistrictDao subDistrictDao;

    @Mock
    private OffChannelRejectionDao offChannelRejectionDao;

    @Mock
    private MinUsableSignalDao minUsableSignalDao;

    @Mock
    private CullingFrequencyDao cullingFrequencyDao;

    @Mock
    private TerrainPointDao terrainPointDao;

    private ReferenceServiceImpl referenceService;

    @BeforeEach
    void setUp() {
        referenceService = new ReferenceServiceImpl(
            subDistrictDao, offChannelRejectionDao, minUsableSignalDao,
            cullingFrequencyDao, terrainPointDao);
    }

    @Test
    void testLoadReference() {
        // Setup
        SubDistrict subDistrict = new SubDistrict();
        subDistrict.setSubDistrictCode("ABC");

        MinUsableSignal minSignal = new MinUsableSignal();
        minSignal.setLowVhfSignal(-120.0f);

        CullingFrequency cullingFreq = new CullingFrequency();
        cullingFreq.setDesenCull(5.0f);

        when(subDistrictDao.findAllOrderBySubDistrictCode()).thenReturn(Arrays.asList(subDistrict));
        when(offChannelRejectionDao.findAllOrderByChannelSeparation()).thenReturn(Collections.emptyList());
        when(minUsableSignalDao.getConfiguration()).thenReturn(minSignal);
        when(cullingFrequencyDao.getConfiguration()).thenReturn(cullingFreq);
        when(terrainPointDao.findAll()).thenReturn(Collections.emptyList());

        // Execute
        int result = referenceService.loadReference();

        // Verify
        assertEquals(EmcConstants.OK, result);

        verify(subDistrictDao, times(1)).findAllOrderBySubDistrictCode();
        verify(offChannelRejectionDao, times(1)).findAllOrderByChannelSeparation();
        verify(minUsableSignalDao, times(1)).getConfiguration();
        verify(cullingFrequencyDao, times(1)).getConfiguration();
        verify(terrainPointDao, times(1)).findAll();
    }
}
