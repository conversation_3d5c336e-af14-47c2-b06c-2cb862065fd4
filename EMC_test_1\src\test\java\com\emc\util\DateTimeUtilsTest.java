package com.emc.util;

import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static org.junit.jupiter.api.Assertions.*;

public class DateTimeUtilsTest {

    @Test
    void testGetSysDateTime() {
        // Setup
        StringBuilder sysDate = new StringBuilder();
        StringBuilder sysTime = new StringBuilder();
        StringBuilder yymmdd = new StringBuilder();
        StringBuilder hhmmss = new StringBuilder();
        
        // Execute
        int result = DateTimeUtils.getSysDateTime(sysDate, sysTime, yymmdd, hhmmss);
        
        // Verify
        assertEquals(0, result);
        
        // Verify date format (dd/MM/yy)
        assertTrue(sysDate.toString().matches("\\d{2}/\\d{2}/\\d{2}"));
        
        // Verify time format (HH:mm:ss)
        assertTrue(sysTime.toString().matches("\\d{2}:\\d{2}:\\d{2}"));
        
        // Verify yymmdd format
        assertTrue(yymmdd.toString().matches("\\d{6}"));
        
        // Verify hhmmss format
        assertTrue(hhmmss.toString().matches("\\d{6}"));
        
        // Verify that the date and time are consistent with the current date and time
        LocalDateTime now = LocalDateTime.now();
        String expectedYymmdd = now.format(DateTimeFormatter.ofPattern("yyMMdd"));
        assertEquals(expectedYymmdd, yymmdd.toString());
    }
}
